# SceneLeapPlusDataset Summary

This document summarizes the data fields and their shapes returned by the `collate_fn` of the `SceneLeapPlusDataset` class defined in `datasets/sceneleapplus_dataset.py`.

---

## `SceneLeapPlusDataset`

This dataset is designed for parallel multi-grasp learning architectures. Its key feature is that it returns a **fixed number of grasp poses per item**, controlled by the `num_grasps` initialization parameter. It inherits from `_BaseLeapProDataset` and adapts its data indexing and sampling logic accordingly.

If the number of available collision-free grasps for an object differs from `num_grasps`, the dataset employs a sampling strategy (e.g., random, farthest point, repeat) to ensure each item provides a tensor with a consistent number of grasps.

The `collate_fn` for this dataset performs standard tensor stacking, as each item in the batch already has a fixed-size grasp dimension. This avoids the need for padding that is present in `ForMatchSceneLeapProDataset`.

Assuming a `batch_size` of `B`, a fixed number of grasps `N` (equal to `num_grasps`), and `M` points in the downsampled point cloud, the batched data is a dictionary with the following structure:

| Field Name                      | Shape / Type                                   | Description                                                                 |
| ------------------------------- | ---------------------------------------------- | --------------------------------------------------------------------------- |
| `obj_code`                      | `List[str]` (length `B`)                       | Unique identifier for the object instance (name + UID).                     |
| `scene_pc`                      | `torch.Tensor`, `(B, M, 6)`                    | Scene point cloud with XYZ coordinates and RGB colors.                      |
| `object_mask`                   | `List[torch.Tensor]` (length `B`)              | List of boolean masks for points belonging to the target object. Each tensor is `(M,)`. |
| `hand_model_pose`               | `torch.Tensor`, `(B, N, 23)`                   | A fixed number (`N`) of hand model poses. Shape is `[P(3), Joints(16), Q_wxyz(4)]`. |
| `se3`                           | `torch.Tensor`, `(B, N, 4, 4)`                 | A fixed number (`N`) of SE(3) transformation matrices for the hand poses.   |
| `scene_id`                      | `List[str]` (length `B`)                       | Scene identifier.                                                           |
| `category_id_from_object_index` | `torch.Tensor`, `(B,)`                         | Category ID of the object within the scene.                                 |
| `depth_view_index`              | `torch.Tensor`, `(B,)`                         | Index of the depth view.                                                    |
| `obj_verts`                     | `List[torch.Tensor]` (length `B`)              | List of object mesh vertices. The vertex count `V_i` per object can vary.   |
| `obj_faces`                     | `List[torch.Tensor]` (length `B`)              | List of object mesh faces. The face count `F_i` per object can vary.      |
| `positive_prompt`               | `List[str]` (length `B`)                       | Positive text prompt, typically the object name.                            |
| `negative_prompts`              | `List[List[str]]` (length `B`)                 | List of negative text prompts, usually other object names in the scene.     |
| `error` (optional)              | `List[str]`                                    | Present if data loading fails; other fields may be placeholders.            |

This dataset structure is ideal for models that process a fixed number of grasp candidates in parallel for each object. 