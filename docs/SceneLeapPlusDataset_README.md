# SceneLeapPlusDataset 详细使用指南

## 概述

`SceneLeapPlusDataset` 是专为并行多抓取学习架构设计的数据集类，继承自 `_BaseLeapProDataset`。该数据集的核心特性是为每个样本返回固定数量的抓取姿态，支持批量并行训练多个抓取预测。

### 主要特点

- **固定抓取数量**: 每个样本返回固定数量的抓取姿态 (`num_grasps`)
- **多种采样策略**: 支持随机采样、顺序采样和循环重复等策略
- **批量处理优化**: 专门的 collate 函数支持高效批量处理
- **碰撞检测过滤**: 自动过滤碰撞抓取，仅保留可行抓取
- **多坐标系支持**: 支持相机坐标系、物体坐标系等多种变换模式

## 技术实现要点

### 1. 数据格式规范

```python
# 输出数据格式
{
    'hand_model_pose': torch.Tensor,  # 形状: [num_grasps, 23] - 手部模型姿态
    'se3': torch.Tensor,              # 形状: [num_grasps, 4, 4] - SE3变换矩阵
    'scene_pc': torch.Tensor,         # 形状: [N, 6] - 场景点云 (xyz+rgb)
    'object_mask': torch.Tensor,      # 形状: [N] - 物体掩码
    'obj_verts': torch.Tensor,        # 物体顶点
    'obj_faces': torch.Tensor,        # 物体面片
    'positive_prompt': str,           # 正向文本提示
    'negative_prompts': List[str],    # 负向文本提示列表
    'obj_code': str,                  # 物体编码
    'scene_id': str,                  # 场景ID
    'depth_view_index': int           # 深度视图索引
}
```

### 2. 抓取采样策略

#### Random 策略 (默认)
- 从可用抓取中随机采样
- 当可用抓取不足时，使用随机重复采样

#### First_n 策略
- 按顺序取前 N 个抓取
- 当可用抓取不足时，使用随机重复采样

#### Repeat 策略
- 循环重复可用抓取直到达到目标数量
- 确保所有抓取都被使用

### 3. 碰撞检测过滤

数据集在初始化时会自动过滤碰撞抓取：

```python
def _filter_collision_free_poses(self):
    """基于碰撞检测信息过滤手部姿态数据"""
    # 遍历所有场景和物体
    # 仅保留 collision_free_indices 中指定的抓取
    # 应用 max_grasps_per_object 限制
```

## 关键特性详解

### 1. 固定数量抓取输出

与其他数据集不同，`SceneLeapPlusDataset` 始终返回固定数量的抓取：

<augment_code_snippet path="datasets/sceneleapplus_dataset.py" mode="EXCERPT">
````python
def _sample_grasps_from_available(self, available_grasps: torch.Tensor) -> torch.Tensor:
    """从可用抓取中采样固定数量的抓取"""
    num_available = available_grasps.shape[0]
    
    if num_available >= self.num_grasps:
        # 足够的抓取可用，按策略采样
        if self.grasp_sampling_strategy == "random":
            indices = torch.randperm(num_available)[:self.num_grasps]
            return available_grasps[indices]
````
</augment_code_snippet>

### 2. 专用批处理函数

提供优化的 `collate_fn` 函数处理批量数据：

<augment_code_snippet path="datasets/sceneleapplus_dataset.py" mode="EXCERPT">
````python
@staticmethod
def collate_fn(batch):
    """专用的批处理函数，处理固定数量抓取的批量数据"""
    # 过滤无效项目
    batch = [item for item in batch if isinstance(item, dict)]
    
    # 处理固定维度的张量 (hand_model_pose, se3)
    for key in ['hand_model_pose', 'se3']:
        valid_tensors = []
        for item in current_key_items:
            if isinstance(item, torch.Tensor) and item.shape == expected_shape:
                valid_tensors.append(item)
````
</augment_code_snippet>

## 使用示例

### 基本初始化

```python
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

# 创建数据集实例
dataset = SceneLeapPlusDataset(
    root_dir="/path/to/scene/data",
    succ_grasp_dir="/path/to/grasp/data",
    obj_root_dir="/path/to/object/meshes",
    num_grasps=8,                          # 每个样本8个抓取
    mode="camera_centric",                 # 相机坐标系
    max_grasps_per_object=200,             # 每个物体最大抓取数
    mesh_scale=0.1,                        # 网格缩放因子
    num_neg_prompts=4,                     # 负向提示数量
    enable_cropping=True,                  # 启用点云裁剪
    max_points=10000,                      # 最大点数
    grasp_sampling_strategy="random"       # 采样策略
)

print(f"数据集包含 {len(dataset)} 个样本")
```

### DataLoader 集成

```python
from torch.utils.data import DataLoader

# 创建 DataLoader
dataloader = DataLoader(
    dataset,
    batch_size=16,
    shuffle=True,
    num_workers=4,
    collate_fn=SceneLeapPlusDataset.collate_fn  # 使用专用collate函数
)

# 训练循环
for batch in dataloader:
    hand_poses = batch['hand_model_pose']    # [16, 8, 23]
    se3_matrices = batch['se3']              # [16, 8, 4, 4]
    scene_pcs = batch['scene_pc']            # 列表或张量
    
    # 进行模型训练...
```

### 不同采样策略测试

```python
strategies = ["random", "first_n", "repeat"]

for strategy in strategies:
    dataset = SceneLeapPlusDataset(
        root_dir=root_dir,
        succ_grasp_dir=succ_grasp_dir,
        obj_root_dir=obj_root_dir,
        num_grasps=6,
        grasp_sampling_strategy=strategy,
        max_grasps_per_object=10  # 限制数量加快测试
    )
    
    sample = dataset[0]
    print(f"{strategy} 策略: {sample['hand_model_pose'].shape}")
```

## 与其他数据集类的对比

| 特性 | SceneLeapPlusDataset | SceneLeapProDataset | ForMatchSceneLeapProDataset |
|------|---------------------|--------------------|-----------------------------|
| 抓取数量 | 固定数量 (num_grasps) | 单个抓取 | 变长批次 (所有可用抓取) |
| 输出格式 | [num_grasps, 23] | [23] | [N, 23] (N可变) |
| 适用场景 | 并行多抓取学习 | 单抓取预测 | 抓取匹配/评估 |
| 批处理 | 固定维度，易批处理 | 简单批处理 | 需要特殊collate函数 |
| 采样策略 | 多种策略可选 | 随机选择 | 返回所有抓取 |

### 选择建议

- **SceneLeapPlusDataset**: 适用于需要预测多个抓取的并行学习架构
- **SceneLeapProDataset**: 适用于传统的单抓取预测任务
- **ForMatchSceneLeapProDataset**: 适用于抓取质量评估和匹配任务

## 注意事项和最佳实践

### 1. 参数配置建议

```python
# 训练配置
dataset = SceneLeapPlusDataset(
    num_grasps=8,                    # 根据模型架构选择
    max_grasps_per_object=200,       # 平衡质量和速度
    grasp_sampling_strategy="random", # 训练时使用随机策略
    enable_cropping=True,            # 提高训练效率
    max_points=10000                 # 控制内存使用
)

# 评估配置  
dataset = SceneLeapPlusDataset(
    num_grasps=16,                   # 评估时可以使用更多抓取
    grasp_sampling_strategy="first_n", # 评估时使用确定性策略
    enable_cropping=False            # 评估时保持完整数据
)
```

### 2. 内存优化

- 合理设置 `max_points` 控制点云大小
- 使用 `max_grasps_per_object` 限制每个物体的抓取数量
- 在 DataLoader 中设置适当的 `num_workers`

### 3. 错误处理

数据集内置完善的错误处理机制：

```python
sample = dataset[0]
if 'error' in sample:
    print(f"数据加载错误: {sample['error']}")
    # 处理错误情况
else:
    # 正常处理数据
    hand_poses = sample['hand_model_pose']
```

### 4. 调试建议

```python
# 检查数据格式
sample = dataset[0]
print(f"手部姿态形状: {sample['hand_model_pose'].shape}")
print(f"SE3矩阵形状: {sample['se3'].shape}")
print(f"场景点云形状: {sample['scene_pc'].shape}")

# 验证批处理
batch = next(iter(dataloader))
print(f"批次手部姿态形状: {batch['hand_model_pose'].shape}")
```

## 扩展和定制

### 自定义采样策略

可以通过继承和重写 `_sample_grasps_from_available` 方法实现自定义采样策略：

```python
class CustomSceneLeapPlusDataset(SceneLeapPlusDataset):
    def _sample_grasps_from_available(self, available_grasps):
        # 实现自定义采样逻辑
        # 例如：基于质量分数的加权采样
        pass
```

### 数据增强

可以在 `_transform_data_by_mode` 方法中添加数据增强：

```python
def _transform_data_by_mode(self, **kwargs):
    # 调用父类方法
    result = super()._transform_data_by_mode(**kwargs)
    
    # 添加数据增强
    # 例如：随机旋转、噪声添加等
    
    return result
```

这个数据集类为并行多抓取学习提供了强大而灵活的数据加载解决方案，通过合理配置参数和使用最佳实践，可以显著提升训练效率和模型性能。

## 运行测试

### 基本功能测试

```bash
# 运行基本测试
python tests/test_sceneleapplus_dataset.py

# 运行缓存版本测试
python tests/test_sceneleapplus_cached.py
```

### 示例代码运行

```bash
# 运行缓存版本示例
python examples/sceneleapplus_cached_example.py
```

## 调试工具

```python
def debug_dataset_sample(dataset, idx=0):
    """调试数据集样本的详细信息"""
    sample = dataset[idx]

    print(f"=== 样本 {idx} 调试信息 ===")
    print(f"物体代码: {sample.get('obj_code', 'N/A')}")
    print(f"场景ID: {sample.get('scene_id', 'N/A')}")

    if 'error' in sample:
        print(f"错误信息: {sample['error']}")
        return

    # 检查张量形状和数值范围
    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: 形状={value.shape}, 范围=[{value.min():.3f}, {value.max():.3f}]")
        elif isinstance(value, (list, tuple)):
            print(f"{key}: 长度={len(value)}")
        else:
            print(f"{key}: {type(value)}")
```

## 相关资源

- **测试脚本**: `tests/test_sceneleapplus_dataset.py`
- **示例代码**: `examples/sceneleapplus_cached_example.py`
- **基类文档**: `datasets/sceneleappro_dataset.py`
- **缓存版本**: `datasets/sceneleapplus_cached.py`

---

*最后更新: 2025-07-25*

## 完整示例代码

### 训练脚本示例

```python
#!/usr/bin/env python3
"""SceneLeapPlusDataset 训练示例"""
import torch
from torch.utils.data import DataLoader
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def main():
    # 数据集配置
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=8,
        mode="camera_centric",
        max_grasps_per_object=2,  # 测试时使用小数值
        grasp_sampling_strategy="random"
    )

    # DataLoader配置
    dataloader = DataLoader(
        dataset,
        batch_size=4,
        shuffle=True,
        num_workers=2,
        collate_fn=SceneLeapPlusDataset.collate_fn
    )

    # 模拟训练循环
    for epoch in range(2):
        print(f"Epoch {epoch + 1}")
        for batch_idx, batch in enumerate(dataloader):
            if batch_idx >= 3:  # 只处理前3个批次用于演示
                break

            # 获取批次数据
            hand_poses = batch['hand_model_pose']  # [batch_size, num_grasps, 23]
            se3_matrices = batch['se3']            # [batch_size, num_grasps, 4, 4]

            print(f"  Batch {batch_idx}: hand_poses {hand_poses.shape}, se3 {se3_matrices.shape}")

if __name__ == "__main__":
    main()
```

### 数据验证脚本

```python
#!/usr/bin/env python3
"""SceneLeapPlusDataset 数据验证脚本"""
import torch
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def validate_dataset():
    """验证数据集的完整性和正确性"""
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=6,
        max_grasps_per_object=2
    )

    print(f"数据集大小: {len(dataset)}")

    # 验证多个样本
    for i in range(min(5, len(dataset))):
        sample = dataset[i]

        if 'error' in sample:
            print(f"样本 {i} 有错误: {sample['error']}")
            continue

        # 验证数据形状
        hand_pose = sample['hand_model_pose']
        se3 = sample['se3']
        scene_pc = sample['scene_pc']

        assert hand_pose.shape == (6, 23), f"手部姿态形状错误: {hand_pose.shape}"
        assert se3.shape == (6, 4, 4), f"SE3矩阵形状错误: {se3.shape}"
        assert scene_pc.shape[1] == 6, f"场景点云特征维度错误: {scene_pc.shape}"

        print(f"样本 {i} 验证通过")

    print("数据集验证完成!")

if __name__ == "__main__":
    validate_dataset()
```

## 性能优化建议

### 1. 数据加载优化

```python
# 推荐的高性能配置
dataloader = DataLoader(
    dataset,
    batch_size=32,           # 根据GPU内存调整
    shuffle=True,
    num_workers=8,           # 根据CPU核心数调整
    pin_memory=True,         # GPU训练时启用
    persistent_workers=True, # 保持worker进程
    collate_fn=SceneLeapPlusDataset.collate_fn
)
```

### 2. 内存使用优化

```python
# 内存优化配置
dataset = SceneLeapPlusDataset(
    max_points=8192,         # 减少点云大小
    max_grasps_per_object=100, # 限制抓取数量
    enable_cropping=True,    # 启用裁剪减少数据量
    mesh_scale=0.1          # 适当的网格缩放
)
```

### 3. 训练稳定性

```python
# 错误处理和数据清理
def safe_collate_fn(batch):
    """安全的批处理函数，处理异常数据"""
    valid_batch = []
    for item in batch:
        if isinstance(item, dict) and 'error' not in item:
            valid_batch.append(item)

    if not valid_batch:
        return None

    return SceneLeapPlusDataset.collate_fn(valid_batch)
```

## 故障排除

### 常见问题及解决方案

#### 1. 数据加载缓慢
- 增加 `num_workers` 数量
- 启用 `pin_memory=True`
- 减少 `max_points` 和 `max_grasps_per_object`
- 使用SSD存储数据

#### 2. 内存不足
- 减少 `batch_size`
- 降低 `max_points` 参数
- 启用 `enable_cropping=True`
- 使用梯度累积技术

#### 3. 抓取数量不一致
- 检查 `num_grasps` 参数设置
- 验证采样策略是否正确
- 确保使用正确的 `collate_fn`

#### 4. 坐标系转换错误
- 检查 `mode` 参数设置
- 验证相机标定数据
- 确认物体网格的坐标系

## 完整示例代码

### 训练脚本示例

```python
#!/usr/bin/env python3
"""
SceneLeapPlusDataset 训练示例
"""
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def main():
    # 数据集配置
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=8,
        mode="camera_centric",
        max_grasps_per_object=2,  # 测试时使用小数值
        grasp_sampling_strategy="random"
    )

    # DataLoader配置
    dataloader = DataLoader(
        dataset,
        batch_size=4,
        shuffle=True,
        num_workers=2,
        collate_fn=SceneLeapPlusDataset.collate_fn
    )

    # 模拟训练循环
    for epoch in range(2):
        print(f"Epoch {epoch + 1}")
        for batch_idx, batch in enumerate(dataloader):
            if batch_idx >= 3:  # 只处理前3个批次用于演示
                break

            # 获取批次数据
            hand_poses = batch['hand_model_pose']  # [batch_size, num_grasps, 23]
            se3_matrices = batch['se3']            # [batch_size, num_grasps, 4, 4]

            print(f"  Batch {batch_idx}: hand_poses {hand_poses.shape}, se3 {se3_matrices.shape}")

            # 这里可以添加模型前向传播和损失计算
            # loss = model(batch)
            # loss.backward()
            # optimizer.step()

if __name__ == "__main__":
    main()
```

### 数据验证脚本

```python
#!/usr/bin/env python3
"""
SceneLeapPlusDataset 数据验证脚本
"""
import torch
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def validate_dataset():
    """验证数据集的完整性和正确性"""

    # 创建数据集
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=6,
        max_grasps_per_object=2
    )

    print(f"数据集大小: {len(dataset)}")

    # 验证多个样本
    for i in range(min(5, len(dataset))):
        sample = dataset[i]

        if 'error' in sample:
            print(f"样本 {i} 有错误: {sample['error']}")
            continue

        # 验证数据形状
        hand_pose = sample['hand_model_pose']
        se3 = sample['se3']
        scene_pc = sample['scene_pc']

        assert hand_pose.shape == (6, 23), f"手部姿态形状错误: {hand_pose.shape}"
        assert se3.shape == (6, 4, 4), f"SE3矩阵形状错误: {se3.shape}"
        assert scene_pc.shape[1] == 6, f"场景点云特征维度错误: {scene_pc.shape}"

        print(f"样本 {i} 验证通过")

    print("数据集验证完成!")

if __name__ == "__main__":
    validate_dataset()
```

## 性能优化建议

### 1. 数据加载优化

```python
# 推荐的高性能配置
dataloader = DataLoader(
    dataset,
    batch_size=32,           # 根据GPU内存调整
    shuffle=True,
    num_workers=8,           # 根据CPU核心数调整
    pin_memory=True,         # GPU训练时启用
    persistent_workers=True, # 保持worker进程
    collate_fn=SceneLeapPlusDataset.collate_fn
)
```

### 2. 内存使用优化

```python
# 内存优化配置
dataset = SceneLeapPlusDataset(
    max_points=8192,         # 减少点云大小
    max_grasps_per_object=100, # 限制抓取数量
    enable_cropping=True,    # 启用裁剪减少数据量
    mesh_scale=0.1          # 适当的网格缩放
)
```

### 3. 训练稳定性

```python
# 错误处理和数据清理
def safe_collate_fn(batch):
    """安全的批处理函数，处理异常数据"""
    # 过滤错误样本
    valid_batch = []
    for item in batch:
        if isinstance(item, dict) and 'error' not in item:
            valid_batch.append(item)

    if not valid_batch:
        return None

    return SceneLeapPlusDataset.collate_fn(valid_batch)

# 在训练循环中使用
for batch in dataloader:
    if batch is None:
        continue  # 跳过无效批次
    # 正常训练...
```

## 故障排除

### 常见问题及解决方案

#### 1. 数据加载缓慢
**问题**: 数据加载速度慢，成为训练瓶颈
**解决方案**:
- 增加 `num_workers` 数量
- 启用 `pin_memory=True`
- 减少 `max_points` 和 `max_grasps_per_object`
- 使用SSD存储数据

#### 2. 内存不足
**问题**: GPU或系统内存不足
**解决方案**:
- 减少 `batch_size`
- 降低 `max_points` 参数
- 启用 `enable_cropping=True`
- 使用梯度累积技术

#### 3. 抓取数量不一致
**问题**: 不同样本的抓取数量不同
**解决方案**:
- 检查 `num_grasps` 参数设置
- 验证采样策略是否正确
- 确保使用正确的 `collate_fn`

#### 4. 坐标系转换错误
**问题**: 抓取姿态在错误的坐标系中
**解决方案**:
- 检查 `mode` 参数设置
- 验证相机标定数据
- 确认物体网格的坐标系

## 完整示例代码

### 训练脚本示例

```python
#!/usr/bin/env python3
"""
SceneLeapPlusDataset 训练示例
"""
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def main():
    # 数据集配置
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=8,
        mode="camera_centric",
        max_grasps_per_object=2,  # 测试时使用小数值
        grasp_sampling_strategy="random"
    )

    # DataLoader配置
    dataloader = DataLoader(
        dataset,
        batch_size=4,
        shuffle=True,
        num_workers=2,
        collate_fn=SceneLeapPlusDataset.collate_fn
    )

    # 模拟训练循环
    for epoch in range(2):
        print(f"Epoch {epoch + 1}")
        for batch_idx, batch in enumerate(dataloader):
            if batch_idx >= 3:  # 只处理前3个批次用于演示
                break

            # 获取批次数据
            hand_poses = batch['hand_model_pose']  # [batch_size, num_grasps, 23]
            se3_matrices = batch['se3']            # [batch_size, num_grasps, 4, 4]

            print(f"  Batch {batch_idx}: hand_poses {hand_poses.shape}, se3 {se3_matrices.shape}")

            # 这里可以添加模型前向传播和损失计算
            # loss = model(batch)
            # loss.backward()
            # optimizer.step()

if __name__ == "__main__":
    main()
```

### 数据验证脚本

```python
#!/usr/bin/env python3
"""
SceneLeapPlusDataset 数据验证脚本
"""
import torch
from datasets.sceneleapplus_dataset import SceneLeapPlusDataset

def validate_dataset():
    """验证数据集的完整性和正确性"""

    # 创建数据集
    dataset = SceneLeapPlusDataset(
        root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        num_grasps=6,
        max_grasps_per_object=2
    )

    print(f"数据集大小: {len(dataset)}")

    # 验证多个样本
    for i in range(min(5, len(dataset))):
        sample = dataset[i]

        if 'error' in sample:
            print(f"样本 {i} 有错误: {sample['error']}")
            continue

        # 验证数据形状
        hand_pose = sample['hand_model_pose']
        se3 = sample['se3']
        scene_pc = sample['scene_pc']

        assert hand_pose.shape == (6, 23), f"手部姿态形状错误: {hand_pose.shape}"
        assert se3.shape == (6, 4, 4), f"SE3矩阵形状错误: {se3.shape}"
        assert scene_pc.shape[1] == 6, f"场景点云特征维度错误: {scene_pc.shape}"

        print(f"样本 {i} 验证通过")

    print("数据集验证完成!")

if __name__ == "__main__":
    validate_dataset()
```

## 性能优化建议

### 1. 数据加载优化

```python
# 推荐的高性能配置
dataloader = DataLoader(
    dataset,
    batch_size=32,           # 根据GPU内存调整
    shuffle=True,
    num_workers=8,           # 根据CPU核心数调整
    pin_memory=True,         # GPU训练时启用
    persistent_workers=True, # 保持worker进程
    collate_fn=SceneLeapPlusDataset.collate_fn
)
```

### 2. 内存使用优化

```python
# 内存优化配置
dataset = SceneLeapPlusDataset(
    max_points=8192,         # 减少点云大小
    max_grasps_per_object=100, # 限制抓取数量
    enable_cropping=True,    # 启用裁剪减少数据量
    mesh_scale=0.1          # 适当的网格缩放
)
```

### 3. 训练稳定性

```python
# 错误处理和数据清理
def safe_collate_fn(batch):
    """安全的批处理函数，处理异常数据"""
    # 过滤错误样本
    valid_batch = []
    for item in batch:
        if isinstance(item, dict) and 'error' not in item:
            valid_batch.append(item)

    if not valid_batch:
        return None

    return SceneLeapPlusDataset.collate_fn(valid_batch)

# 在训练循环中使用
for batch in dataloader:
    if batch is None:
        continue  # 跳过无效批次
    # 正常训练...
```

## 故障排除

### 常见问题及解决方案

#### 1. 数据加载缓慢
**问题**: 数据加载速度慢，成为训练瓶颈
**解决方案**:
- 增加 `num_workers` 数量
- 启用 `pin_memory=True`
- 减少 `max_points` 和 `max_grasps_per_object`
- 使用SSD存储数据

#### 2. 内存不足
**问题**: GPU或系统内存不足
**解决方案**:
- 减少 `batch_size`
- 降低 `max_points` 参数
- 启用 `enable_cropping=True`
- 使用梯度累积技术

#### 3. 抓取数量不一致
**问题**: 不同样本的抓取数量不同
**解决方案**:
- 检查 `num_grasps` 参数设置
- 验证采样策略是否正确
- 确保使用正确的 `collate_fn`

#### 4. 坐标系转换错误
**问题**: 抓取姿态在错误的坐标系中
**解决方案**:
- 检查 `mode` 参数设置
- 验证相机标定数据
- 确认物体网格的坐标系

### 调试工具

```python
def debug_dataset_sample(dataset, idx=0):
    """调试数据集样本的详细信息"""
    sample = dataset[idx]

    print(f"=== 样本 {idx} 调试信息 ===")
    print(f"物体代码: {sample.get('obj_code', 'N/A')}")
    print(f"场景ID: {sample.get('scene_id', 'N/A')}")
    print(f"深度视图索引: {sample.get('depth_view_index', 'N/A')}")

    if 'error' in sample:
        print(f"错误信息: {sample['error']}")
        return

    # 检查张量形状和数值范围
    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: 形状={value.shape}, 数据类型={value.dtype}, 范围=[{value.min():.3f}, {value.max():.3f}]")
        elif isinstance(value, (list, tuple)):
            print(f"{key}: 类型={type(value)}, 长度={len(value)}")
        else:
            print(f"{key}: {type(value)} = {value}")

# 使用示例
debug_dataset_sample(dataset, 0)
```

## 版本兼容性

### 依赖要求

```
torch >= 1.9.0
pytorch3d >= 0.6.0
numpy >= 1.19.0
opencv-python >= 4.5.0
```

### 更新日志

- **v1.0**: 初始版本，支持固定数量抓取输出
- **v1.1**: 添加多种采样策略支持
- **v1.2**: 优化批处理性能和内存使用
- **v1.3**: 增强错误处理和调试功能

## 贡献指南

如需扩展或修改 `SceneLeapPlusDataset`，请遵循以下指南：

1. **保持API兼容性**: 新功能应向后兼容
2. **添加单元测试**: 新功能需要相应的测试用例
3. **更新文档**: 修改后及时更新文档
4. **性能测试**: 确保修改不会显著影响性能

## 相关资源

- **测试脚本**: `tests/test_sceneleapplus_dataset.py`
- **示例代码**: `examples/sceneleapplus_cached_example.py`
- **基类文档**: `datasets/sceneleappro_dataset.py`
- **缓存版本**: `datasets/sceneleapplus_cached.py`

---

*最后更新: 2025-07-25*
