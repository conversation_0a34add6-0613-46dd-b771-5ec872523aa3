# 验证和测试阶段多抓取重构实施计划

## 重构目标

将SceneLeapPlus从单抓取点学习重构为并行多抓取分布学习，实现以下核心变化：

**数据格式变化**：
- `hand_model_pose`: `[B, pose_dim]` → `[B, num_grasps, pose_dim]`
- `se3`: `[B, 4, 4]` → `[B, num_grasps, 4, 4]`
- `scene_pc`: 保持 `[B, max_points, 6]` 不变
- 文本条件: 保持不变

**核心理念**：
- 从学习孤立抓取点转向学习完整抓取姿态分布
- 实现并行加噪去噪和One-shot Parallel Decoding
- 提升训练效率和建模能力

我们已经按照docs/multi_grasp_refactoring_plan.md完成了训练阶段的多抓取架构，现在请将验证和测试阶段从单抓取推理模式重构为并行多抓取推理模式，与训练阶段的多抓取架构保持一致。

**核心变化**：
- **推理模式**: 从单次生成单个预测转向一次生成多个抓取预测
- **匹配算法**: 从1对多匹配升级为多对多最优分配
- **评估方式**: 从单点评估转向分布级评估
- **数据流**: 保持与训练阶段一致的多抓取数据格式

**技术目标**：
- 实现验证/测试阶段的并行多抓取推理
- 升级匹配器支持多预测vs多目标匹配
- 确保与训练阶段数据格式和处理流程的一致性
- 保持向后兼容性，支持单抓取模式

## 实施计划概览

重构任务按功能模块分为3个阶段，共12个子任务：

1. **阶段1: 推理模式重构** (5个子任务)
2. **阶段2: 匹配器算法升级** (4个子任务)  
3. **阶段3: 集成测试和验证** (3个子任务)

---

## 阶段1: 推理模式重构

### 任务1.1: validation_step方法重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改DDPMLightning.validation_step支持多抓取并行推理

**涉及文件**:
- `models/diffuser_lightning.py` (行318-342)

**具体修改**:
```python
def validation_step(self, batch, batch_idx):
    # 数据预处理保持不变（已支持多抓取）
    batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
    
    # 推理模式重构
    pred_x0 = self.sample(batch)  # 返回 [B, k, T+1, num_grasps, pose_dim] 或 [B, k, T+1, pose_dim]
    
    # 根据数据维度确定处理方式
    if pred_x0.dim() == 5:
        # 多抓取模式: [B, k, T+1, num_grasps, pose_dim]
        pred_x0 = pred_x0[:,0,-1]  # [B, num_grasps, pose_dim]
        B, num_grasps, pose_dim = pred_x0.shape
        batch_size = B * num_grasps
        
        # 构建多抓取预测字典
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:] if pose_dim > 25 else pred_x0[..., 6:25],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    else:
        # 单抓取模式（向后兼容）
        pred_x0 = pred_x0[:,0,-1]  # [B, pose_dim]
        batch_size = self.batch_size
        pred_dict = self._build_single_grasp_pred_dict(pred_x0)
    
    # 损失计算
    loss_dict = self.criterion(pred_dict, batch, mode='val')
    loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)
    
    # 日志记录使用正确的batch_size
    self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
    self.validation_step_outputs.append({
        "loss": loss.item(),
        "loss_dict": {k: v.item() for k, v in loss_dict.items()}
    })
    
    return {"loss": loss, "loss_dict": loss_dict}

def _build_single_grasp_pred_dict(self, pred_x0):
    """构建单抓取预测字典（向后兼容）"""
    return {
        "pred_pose_norm": pred_x0,
        "qpos_norm": pred_x0[..., 25:] if pred_x0.shape[-1] > 25 else pred_x0[..., 23:],
        "translation_norm": pred_x0[..., :3],
        "rotation": pred_x0[..., 6:25] if pred_x0.shape[-1] > 25 else pred_x0[..., 6:23],
        "pred_noise": torch.tensor([1.0], device=self.device),
        "noise": torch.tensor([1.0], device=self.device)
    }
```

**验证方法**:
```python
def test_validation_step_multi_grasp():
    model = DDPMLightning(multi_grasp_config)
    
    # 测试多抓取数据
    multi_batch = create_multi_grasp_validation_batch()
    result = model.validation_step(multi_batch, 0)
    
    assert 'loss' in result
    assert result['loss'].item() >= 0
    
    # 测试单抓取兼容性
    single_batch = create_single_grasp_validation_batch()
    result = model.validation_step(single_batch, 0)
    assert 'loss' in result
```

### 任务1.2: test_step方法重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改DDPMLightning.test_step支持多抓取测试

**涉及文件**:
- `models/diffuser_lightning.py` (行381-399)

**具体修改**:
```python
def test_step(self, batch, batch_idx):
    # 数据预处理
    batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
    
    # 推理
    pred_x0 = self.sample(batch)
    
    # 根据维度确定处理方式
    if pred_x0.dim() == 5:
        # 多抓取模式
        pred_x0 = pred_x0[:,0,-1]  # [B, num_grasps, pose_dim]
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    else:
        # 单抓取模式（向后兼容）
        pred_x0 = pred_x0[:,0,-1]  # [B, pose_dim]
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    
    # 指标计算
    metric_dict, metric_details = self.criterion(pred_dict, batch, mode='test')
    self.metric_results.append(metric_details)
    
    return metric_dict
```

### 任务1.3: forward_infer方法重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改推理接口支持多抓取输出

**涉及文件**:
- `models/diffuser_lightning.py` (行467-483)

**具体修改**:
```python
def forward_infer(self, data: Dict, k=4, timestep=-1):
    data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
    pred_x0 = self.sample(data, k=k)
    pred_x0 = pred_x0[:, :, timestep]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]

    if pred_x0.dim() == 4:
        # 多抓取模式: [B, k, num_grasps, pose_dim]
        B, k, num_grasps, pose_dim = pred_x0.shape
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    else:
        # 单抓取模式: [B, k, pose_dim]
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    
    preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
    return preds_hand, targets_hand
```

### 任务1.4: forward_get_pose系列方法重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改姿态获取接口支持多抓取

**涉及文件**:
- `models/diffuser_lightning.py` (行506-542)

**具体修改**:
```python
def forward_get_pose(self, data: Dict, k=4):
    data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
    pred_x0 = self.sample(data, k=k)
    pred_x0 = pred_x0[:, :, -1]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]
    
    print(f"pred_x0.shape {pred_x0.shape}")

    if pred_x0.dim() == 4:
        # 多抓取模式
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    else:
        # 单抓取模式
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    
    outputs, targets = self.criterion.infer_norm_process_dict_get_pose(pred_dict, data)
    return outputs, targets

def forward_get_pose_matched(self, data: Dict, k=4):
    # 类似的多抓取适配逻辑
    data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
    pred_x0 = self.sample(data, k=k)
    pred_x0 = pred_x0[:, :, -1]
    
    print(f"pred_x0.shape {pred_x0.shape}")
    
    # 构建pred_dict（支持多抓取和单抓取）
    pred_dict = self._build_pred_dict_adaptive(pred_x0)
    
    matched_pred, matched_targets, outputs, targets = self.criterion.infer_norm_process_dict_get_pose_matched(pred_dict, data)
    return matched_pred, matched_targets, outputs, targets

def _build_pred_dict_adaptive(self, pred_x0):
    """自适应构建预测字典"""
    return {
        "pred_pose_norm": pred_x0,
        "qpos_norm": pred_x0[..., 3:19],
        "translation_norm": pred_x0[..., :3],
        "rotation": pred_x0[..., 19:],
        "pred_noise": torch.tensor([1.0], device=self.device),
        "noise": torch.tensor([1.0], device=self.device)
    }
```

### 任务1.5: 推理模式验证脚本
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 创建验证脚本确保推理模式重构正确

**涉及文件**:
- `tests/test_multi_grasp_inference.py` (新建)

**具体内容**:
```python
def test_multi_grasp_inference_modes():
    """测试多抓取推理模式"""
    
    # 1. 测试validation_step
    model = DDPMLightning(multi_grasp_config)
    
    # 多抓取数据测试
    multi_batch = create_multi_grasp_batch(B=2, num_grasps=4)
    val_result = model.validation_step(multi_batch, 0)
    
    assert 'loss' in val_result
    assert 'loss_dict' in val_result
    assert val_result['loss'].item() >= 0
    
    # 2. 测试test_step
    test_result = model.test_step(multi_batch, 0)
    assert test_result is not None
    
    # 3. 测试forward_infer
    infer_result = model.forward_infer(multi_batch, k=2)
    preds_hand, targets_hand = infer_result
    assert preds_hand is not None
    assert targets_hand is not None
    
    # 4. 测试向后兼容性
    single_batch = create_single_grasp_batch(B=2)
    single_val_result = model.validation_step(single_batch, 0)
    assert 'loss' in single_val_result
    
    print("✅ 多抓取推理模式测试通过")

def test_inference_output_dimensions():
    """测试推理输出维度正确性"""
    
    model = DDPMLightning(multi_grasp_config)
    
    # 多抓取测试
    multi_data = create_multi_grasp_data(B=2, num_grasps=4)
    pred_poses = model.forward_get_pose_raw(multi_data, k=3)
    
    # 验证输出维度
    if pred_poses.dim() == 4:
        B, k, num_grasps, pose_dim = pred_poses.shape
        assert B == 2
        assert k == 3
        assert num_grasps == 4
        print(f"✅ 多抓取输出维度正确: {pred_poses.shape}")
    else:
        B, k, pose_dim = pred_poses.shape
        assert B == 2
        assert k == 3
        print(f"✅ 单抓取输出维度正确: {pred_poses.shape}")
```

---

## 阶段2: 匹配器算法升级

### 任务2.1: Matcher类核心算法重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改Matcher.forward支持多预测vs多目标匹配

**涉及文件**:
- `models/loss/matcher.py` (行16-69)

**具体修改**:
```python
@torch.no_grad()
def forward(self, preds, targets):
    """执行多预测与多目标的最优匹配"""
    device = preds["rotation"].device
    
    # 获取维度信息
    if preds["rotation"].dim() == 3:
        # 多抓取模式: [B, num_grasps, rot_dim]
        batch_size, nqueries = preds["rotation"].shape[:2]
    else:
        # 单抓取模式: [B, rot_dim] 
        batch_size = preds["rotation"].shape[0]
        nqueries = 1
        # 扩展维度以统一处理
        for key in preds:
            if isinstance(preds[key], torch.Tensor) and preds[key].dim() == 2:
                preds[key] = preds[key].unsqueeze(1)  # [B, 1, D]
    
    # 计算有效目标掩码
    valid_mask = (targets["norm_pose"].abs().sum(dim=-1) > 0)
    
    # 初始化成本矩阵
    cost_matrices = torch.zeros(
        len(self.weight_dict), 
        batch_size, 
        nqueries, 
        targets["norm_pose"].size(1), 
        device=device
    )
    
    # 计算各项成本
    for i, (name, weight) in enumerate(self.weight_dict.items()):
        m = getattr(self, f"get_{name}_cost_mat")
        if name == "rotation":
            cost_matrices[i] = m(
                preds, targets, weight, self.rot_type, valid_mask
            )
        else:
            cost_matrices[i] = m(
                preds, targets, weight, valid_mask
            )
    
    # 合并成本矩阵
    final_cost = cost_matrices.sum(0).cpu().numpy()
    
    # 匈牙利算法求解
    assignments = []
    per_query_gt_inds = torch.zeros(
        [batch_size, nqueries], dtype=torch.int64, device=device
    )
    query_matched_mask = torch.zeros(
        [batch_size, nqueries], dtype=torch.int64, device=device
    )
    
    for b in range(batch_size):
        valid_targets = valid_mask[b].sum().item()
        if valid_targets > 0:
            # 多对多匹配：nqueries个预测 vs valid_targets个目标
            assign = linear_sum_assignment(final_cost[b, :, :valid_targets])
            assign = [torch.from_numpy(x).long().to(device) for x in assign]
            per_query_gt_inds[b, assign[0]] = assign[1]
            query_matched_mask[b, assign[0]] = 1
            assignments.append(assign)
        else:
            assignments.append([
                torch.tensor([], device=device), 
                torch.tensor([], device=device)
            ])

    return {
        "final_cost": final_cost,
        "assignments": assignments,
        "per_query_gt_inds": per_query_gt_inds,
        "query_matched_mask": query_matched_mask,
    }
```

### 任务2.2: 成本函数适配
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改成本计算函数支持多预测输入

**涉及文件**:
- `models/loss/matcher.py` (行71-137)

**具体修改**:
```python
def _get_cost_mat_by_elementwise(
    self,
    prediction,
    targets,
    weight=1.0,
    valid_mask=None,
    element_wise_func=partial(F.l1_loss, reduction="none")
):
    """通用成本矩阵计算，支持多预测输入"""

    # 处理输入维度
    if prediction.dim() == 2:
        # 单预测模式: [B, D] -> [B, 1, D]
        prediction = prediction.unsqueeze(1)

    B, nqueries, D = prediction.shape
    N = targets.shape[1]

    # 广播计算成本矩阵
    cost = element_wise_func(
        prediction.unsqueeze(2).expand(-1, -1, N, -1),  # [B, nqueries, N, D]
        targets.unsqueeze(1).expand(-1, nqueries, -1, -1)  # [B, nqueries, N, D]
    ).sum(-1)  # [B, nqueries, N]

    # 应用有效性掩码
    if valid_mask is not None:
        cost = cost.masked_fill(~valid_mask.unsqueeze(1), float('inf'))

    return weight * cost

def get_qpos_cost_mat(self, prediction, targets, weight=1.0, valid_mask=None):
    """关节角度成本矩阵计算"""
    pred_qpos = prediction["qpos_norm"]
    target_qpos = targets["norm_pose"][..., 3:19]

    return self._get_cost_mat_by_elementwise(
        pred_qpos, target_qpos, weight, valid_mask
    )

def get_translation_cost_mat(self, prediction, targets, weight=1.0, valid_mask=None):
    """平移成本矩阵计算"""
    pred_trans = prediction["translation_norm"]
    target_trans = targets["norm_pose"][..., :3]

    return self._get_cost_mat_by_elementwise(
        pred_trans, target_trans, weight, valid_mask
    )

def get_rotation_cost_mat(
    self, prediction, targets, weight=1.0, rotation_type="euler", valid_mask=None
):
    """旋转成本矩阵计算"""
    pred_rot = prediction["rotation"]
    target_rot = targets["norm_pose"][..., 19:]

    m = getattr(self, f"_get_{rotation_type}_cost_mat", None)
    if m:
        return m(pred_rot, target_rot, weight, valid_mask)
    raise NotImplementedError(f"Rotation type {rotation_type} not supported")
```

### 任务2.3: 匹配结果处理重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改get_matched_by_assignment支持多预测匹配

**涉及文件**:
- `models/loss/grasp_loss_pose.py` (行441-515)

**具体修改**:
```python
def get_matched_by_assignment(self, predictions: Dict, targets: Dict, assignment: Dict) -> Tuple[Dict, Dict]:
    """
    根据匹配分配提取对应的预测和目标
    支持多预测vs多目标匹配

    Args:
        predictions: 预测字典，可能包含 [B, num_grasps, D] 或 [B, D] 格式
        targets: 目标字典，包含 [B, num_targets, D] 格式
        assignment: 匹配分配结果

    Returns:
        matched_preds: 匹配的预测 Dict[str, Tensor of size (K, D)]
        matched_targets: 匹配的目标 Dict[str, Tensor of size (K, D)]
    """
    per_query_gt_inds = assignment["per_query_gt_inds"]  # [B, num_queries]
    query_matched_mask = assignment["query_matched_mask"]  # [B, num_queries]
    K = query_matched_mask.long().sum()  # K = 总匹配数量
    B = query_matched_mask.size(0)

    matched_preds, matched_targets = {}, {}

    pred_target_match_key_map = {
        "pred_pose_norm": "norm_pose",
        "hand_model_pose": "hand_model_pose",
    }

    pose_slices = {
        "pred_pose_norm": (0, None),  # 完整姿态
        "hand_model_pose": (0, None),  # 完整姿态
    }

    for pred_key, target_key in pred_target_match_key_map.items():
        if pred_key not in predictions.keys():
            continue

        pred = predictions[pred_key]
        target = targets[target_key]

        # 处理预测数据维度
        if pred.dim() == 2:
            # 单预测模式: [B, D] -> [B, 1, D]
            pred = pred.unsqueeze(1)

        matched_pred_buffer = []
        matched_target_buffer = []

        for i in range(B):
            _matched_pred, _matched_target = self._get_matched(
                pred[i],  # [num_queries, D]
                target[i],  # [num_targets, D]
                per_query_gt_inds[i],  # [num_queries]
                query_matched_mask[i],  # [num_queries]
            )
            matched_pred_buffer.append(_matched_pred)
            matched_target_buffer.append(_matched_target)

        matched_preds[pred_key] = torch.cat(matched_pred_buffer, dim=0)
        matched_targets[target_key] = torch.cat(matched_target_buffer, dim=0)

        # 应用姿态切片
        if pred_key in pose_slices.keys():
            _s, _e = pose_slices[pred_key]
            if _e is not None:
                matched_targets[target_key] = matched_targets[target_key][:, _s:_e]

    return matched_preds, matched_targets

def _get_matched(self, pred, target, per_query_gt_inds, query_matched_mask):
    """
    提取单个batch的匹配对

    Args:
        pred: [num_queries, D] 预测
        target: [num_targets, D] 目标
        per_query_gt_inds: [num_queries] 每个query对应的target索引
        query_matched_mask: [num_queries] query是否被匹配的掩码

    Returns:
        matched_pred: [num_matched, D]
        matched_target: [num_matched, D]
    """
    matched_indices = query_matched_mask.nonzero(as_tuple=True)[0]  # 被匹配的query索引

    if len(matched_indices) == 0:
        # 没有匹配，返回空张量
        return torch.zeros(0, pred.shape[-1], device=pred.device), \
               torch.zeros(0, target.shape[-1], device=target.device)

    matched_pred = pred[matched_indices]  # [num_matched, D]
    matched_target_indices = per_query_gt_inds[matched_indices]  # [num_matched]
    matched_target = target[matched_target_indices]  # [num_matched, D]

    return matched_pred, matched_target
```

### 任务2.4: 匹配器验证脚本
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 创建验证脚本确保匹配器重构正确

**涉及文件**:
- `tests/test_multi_grasp_matcher.py` (新建)

**具体内容**:
```python
def test_multi_grasp_matcher():
    """测试多抓取匹配器"""

    from models.loss.matcher import Matcher

    # 初始化匹配器
    weight_dict = {'translation': 1.0, 'qpos': 1.0, 'rotation': 1.0}
    matcher = Matcher(weight_dict, rot_type='r6d')

    # 1. 测试多预测vs多目标匹配
    B, num_preds, num_targets, pose_dim = 2, 4, 6, 25

    preds = {
        'translation_norm': torch.randn(B, num_preds, 3),
        'qpos_norm': torch.randn(B, num_preds, 16),
        'rotation': torch.randn(B, num_preds, 6),
    }

    targets = {
        'norm_pose': torch.randn(B, num_targets, pose_dim)
    }

    # 设置部分目标为有效
    targets['norm_pose'][0, 3:] = 0  # 第一个batch只有3个有效目标
    targets['norm_pose'][1, 4:] = 0  # 第二个batch只有4个有效目标

    # 执行匹配
    assignment = matcher(preds, targets)

    # 验证匹配结果
    assert 'assignments' in assignment
    assert 'per_query_gt_inds' in assignment
    assert 'query_matched_mask' in assignment

    # 验证匹配数量合理
    total_matched = assignment['query_matched_mask'].sum().item()
    assert total_matched <= min(num_preds * B, 3 + 4)  # 不超过有效目标总数

    print(f"✅ 多对多匹配测试通过，总匹配数: {total_matched}")

    # 2. 测试向后兼容性（单预测）
    single_preds = {
        'translation_norm': torch.randn(B, 3),
        'qpos_norm': torch.randn(B, 16),
        'rotation': torch.randn(B, 6),
    }

    single_assignment = matcher(single_preds, targets)
    single_matched = single_assignment['query_matched_mask'].sum().item()
    assert single_matched <= B  # 每个batch最多匹配1个

    print(f"✅ 单预测兼容性测试通过，匹配数: {single_matched}")

def test_matched_extraction():
    """测试匹配结果提取"""

    from models.loss.grasp_loss_pose import GraspLossPose

    # 创建损失函数实例
    loss_cfg = create_test_loss_config()
    criterion = GraspLossPose(loss_cfg)

    # 模拟匹配结果
    B, num_preds, num_targets = 2, 3, 4

    predictions = {
        'pred_pose_norm': torch.randn(B, num_preds, 25),
        'hand_model_pose': torch.randn(B, num_preds, 25),
    }

    targets = {
        'norm_pose': torch.randn(B, num_targets, 25),
        'hand_model_pose': torch.randn(B, num_targets, 25),
    }

    # 模拟匹配分配
    assignment = {
        'per_query_gt_inds': torch.tensor([[0, 1, 2], [0, 2, 3]]),  # 每个query对应的target
        'query_matched_mask': torch.tensor([[1, 1, 0], [1, 0, 1]]),  # 匹配掩码
    }

    # 提取匹配对
    matched_preds, matched_targets = criterion.get_matched_by_assignment(
        predictions, targets, assignment
    )

    # 验证匹配结果
    expected_matched_count = assignment['query_matched_mask'].sum().item()  # 应该是3个匹配

    assert matched_preds['pred_pose_norm'].shape[0] == expected_matched_count
    assert matched_targets['norm_pose'].shape[0] == expected_matched_count

    print(f"✅ 匹配提取测试通过，提取了{expected_matched_count}个匹配对")
```

---

## 阶段3: 集成测试和验证

### 任务3.1: 端到端集成测试
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 创建端到端测试验证整个验证/测试流程

**涉及文件**:
- `tests/test_validation_test_integration.py` (新建)

**具体内容**:
```python
def test_validation_test_end_to_end():
    """端到端验证和测试流程测试"""

    # 1. 配置加载
    cfg = load_multi_grasp_config()

    # 2. 模型初始化
    model = DDPMLightning(cfg.model)

    # 3. 数据准备
    val_batch = create_multi_grasp_validation_batch(B=2, num_grasps=4)
    test_batch = create_multi_grasp_test_batch(B=2, num_grasps=6)

    # 4. 验证流程测试
    print("测试验证流程...")
    val_result = model.validation_step(val_batch, 0)

    assert 'loss' in val_result
    assert 'loss_dict' in val_result
    assert val_result['loss'].item() >= 0

    print(f"✅ 验证损失: {val_result['loss'].item():.4f}")

    # 5. 测试流程测试
    print("测试测试流程...")
    test_result = model.test_step(test_batch, 0)

    assert test_result is not None
    print("✅ 测试流程完成")

    # 6. 推理接口测试
    print("测试推理接口...")

    # forward_infer测试
    preds_hand, targets_hand = model.forward_infer(val_batch, k=2)
    assert preds_hand is not None
    assert targets_hand is not None

    # forward_get_pose测试
    outputs, targets = model.forward_get_pose(val_batch, k=2)
    assert outputs is not None
    assert targets is not None

    print("✅ 推理接口测试通过")

    # 7. 向后兼容性测试
    print("测试向后兼容性...")
    single_batch = create_single_grasp_batch(B=2)

    single_val_result = model.validation_step(single_batch, 0)
    assert 'loss' in single_val_result

    single_test_result = model.test_step(single_batch, 0)
    assert single_test_result is not None

    print("✅ 向后兼容性测试通过")

    print("🎉 端到端集成测试全部通过")

def test_data_flow_consistency():
    """测试数据流一致性"""

    model = DDPMLightning(multi_grasp_config)

    # 创建测试数据
    batch = create_multi_grasp_batch(B=2, num_grasps=4)

    # 追踪数据流
    print("追踪验证数据流...")

    # 1. 数据预处理
    processed_batch = process_hand_pose_test(batch, 'r6d', 'camera_centric')
    print(f"预处理后norm_pose形状: {processed_batch['norm_pose'].shape}")

    # 2. 模型推理
    pred_x0 = model.sample(processed_batch)
    print(f"推理输出形状: {pred_x0.shape}")

    # 3. 预测字典构建
    if pred_x0.dim() == 5:
        pred_x0_final = pred_x0[:,0,-1]
        print(f"最终预测形状: {pred_x0_final.shape}")

        # 验证维度一致性
        if pred_x0_final.dim() == 3:
            B, num_grasps, pose_dim = pred_x0_final.shape
            assert B == 2
            assert num_grasps == 4
            print(f"✅ 多抓取维度一致: [B={B}, num_grasps={num_grasps}, pose_dim={pose_dim}]")
        else:
            print(f"✅ 单抓取维度: {pred_x0_final.shape}")

    # 4. 匹配器测试
    pred_dict = {
        "pred_pose_norm": pred_x0_final,
        "qpos_norm": pred_x0_final[..., 3:19],
        "translation_norm": pred_x0_final[..., :3],
        "rotation": pred_x0_final[..., 19:],
    }

    # 模拟匹配过程
    outputs = model.criterion.get_hand_model_pose_test(pred_dict)
    assignments = model.criterion.matcher(outputs, processed_batch)

    print(f"匹配结果: {assignments['query_matched_mask'].sum().item()}个匹配")

    print("✅ 数据流一致性验证通过")
```

### 任务3.2: 性能对比测试
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 对比单抓取vs多抓取模式的性能差异

**涉及文件**:
- `tests/benchmark_validation_performance.py` (新建)

**具体内容**:
```python
def benchmark_validation_performance():
    """基准测试验证性能"""

    import time

    # 配置
    single_cfg = load_single_grasp_config()
    multi_cfg = load_multi_grasp_config()

    # 模型
    single_model = DDPMLightning(single_cfg.model)
    multi_model = DDPMLightning(multi_cfg.model)

    # 数据
    single_batch = create_single_grasp_batch(batch_size=16)
    multi_batch = create_multi_grasp_batch(batch_size=16, num_grasps=4)

    results = {}

    # 1. 验证步骤性能测试
    print("测试验证步骤性能...")

    # 单抓取验证时间
    start_time = time.time()
    for _ in range(10):
        single_result = single_model.validation_step(single_batch, 0)
    single_time = (time.time() - start_time) / 10
    results['single_validation_time'] = single_time

    # 多抓取验证时间
    start_time = time.time()
    for _ in range(10):
        multi_result = multi_model.validation_step(multi_batch, 0)
    multi_time = (time.time() - start_time) / 10
    results['multi_validation_time'] = multi_time

    # 2. 推理性能测试
    print("测试推理性能...")

    # 单抓取推理（需要多次采样获得多个候选）
    start_time = time.time()
    for _ in range(5):
        single_preds = single_model.forward_infer(single_batch, k=4)
    single_infer_time = (time.time() - start_time) / 5
    results['single_inference_time'] = single_infer_time

    # 多抓取推理（一次获得多个候选）
    start_time = time.time()
    for _ in range(5):
        multi_preds = multi_model.forward_infer(multi_batch, k=1)
    multi_infer_time = (time.time() - start_time) / 5
    results['multi_inference_time'] = multi_infer_time

    # 3. 内存使用测试
    print("测试内存使用...")

    torch.cuda.reset_peak_memory_stats()
    single_model.validation_step(single_batch, 0)
    results['single_memory'] = torch.cuda.max_memory_allocated() / 1024**3  # GB

    torch.cuda.reset_peak_memory_stats()
    multi_model.validation_step(multi_batch, 0)
    results['multi_memory'] = torch.cuda.max_memory_allocated() / 1024**3  # GB

    # 4. 输出结果
    print("\n性能基准测试结果:")
    print(f"单抓取验证时间: {results['single_validation_time']:.4f}s")
    print(f"多抓取验证时间: {results['multi_validation_time']:.4f}s")
    print(f"验证时间比率: {results['multi_validation_time']/results['single_validation_time']:.2f}x")

    print(f"单抓取推理时间: {results['single_inference_time']:.4f}s")
    print(f"多抓取推理时间: {results['multi_inference_time']:.4f}s")
    print(f"推理时间比率: {results['multi_inference_time']/results['single_inference_time']:.2f}x")

    print(f"单抓取内存使用: {results['single_memory']:.2f}GB")
    print(f"多抓取内存使用: {results['multi_memory']:.2f}GB")
    print(f"内存使用比率: {results['multi_memory']/results['single_memory']:.2f}x")

    return results
```

### 任务3.3: 配置文件更新
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 更新配置文件支持验证/测试阶段多抓取模式

**涉及文件**:
- `config/model/diffuser/diffuser.yaml`
- `config/model/diffuser/criterion/loss.yaml`

**具体修改**:

1. **更新扩散模型配置** (`config/model/diffuser/diffuser.yaml`):
```yaml
# 验证和测试阶段多抓取配置
validation_test:
  multi_grasp_mode: true  # 启用多抓取验证/测试
  backward_compatibility: true  # 保持向后兼容

# 现有配置保持不变
name: GraspDiffuser
steps: 100
pred_x0: true
# ...
```

2. **更新损失配置** (`config/model/diffuser/criterion/loss.yaml`):
```yaml
# 匹配器多抓取配置
matcher:
  multi_grasp_matching: true  # 启用多对多匹配

# 现有配置
loss_weights:
  para: 1.0
  translation: 1.0
  qpos: 1.0
```

---

## 实施进度跟踪

### 完成状态统计
- **阶段1 (推理模式重构)**: 0/5 完成
- **阶段2 (匹配器算法升级)**: 0/4 完成
- **阶段3 (集成测试和验证)**: 0/3 完成

**总进度**: 0/12 (0%) 完成

### 关键里程碑
- [ ] 阶段1完成：验证/测试推理支持多抓取并行生成
- [ ] 阶段2完成：匹配器支持多对多最优分配
- [ ] 阶段3完成：端到端集成测试通过

### 风险评估
1. **高风险**: 匹配器算法重构（任务2.1-2.3）- 核心算法变化
2. **中风险**: 推理模式重构（任务1.1-1.4）- 影响验证流程
3. **低风险**: 集成测试和配置（任务3.1-3.3）- 主要是验证和配置

### 建议实施顺序
1. 先完成推理模式重构（阶段1），确保多抓取推理正确
2. 再进行匹配器升级（阶段2），实现多对多匹配
3. 最后进行集成测试（阶段3），验证整体功能

### 预期收益
1. **推理一致性**: 验证/测试与训练阶段使用相同的多抓取架构
2. **评估准确性**: 多对多匹配提供更准确的性能评估
3. **系统完整性**: 端到端多抓取分布学习架构
4. **向后兼容**: 保持对现有单抓取模式的支持

---

## 总结

这个验证和测试阶段重构计划将现有的单抓取推理模式升级为多抓取并行推理模式，确保与训练阶段的多抓取架构保持一致。通过分阶段、模块化的实施方式，每个子任务都相对独立，便于code agent逐个完成，保证修改质量和系统稳定性。

**核心技术升级**：
- 并行多抓取推理生成
- 多对多最优匹配算法
- 分布级验证评估
- 端到端架构一致性

**实施保障**：
- 向后兼容性设计
- 分阶段渐进式重构
- 充分的测试验证
- 性能基准对比

通过这个系统性的重构，验证和测试阶段将与训练阶段形成完整统一的多抓取分布学习架构。
