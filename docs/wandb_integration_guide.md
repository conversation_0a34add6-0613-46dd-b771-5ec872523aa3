# WandB集成指南

本文档详细记录了为SceneLeapMax项目集成Weights & Biases (WandB)监控系统所进行的所有改造。

## 📋 目录

1. [概述](#概述)
2. [配置文件改造](#配置文件改造)
3. [训练脚本改造](#训练脚本改造)
4. [模型代码改造](#模型代码改造)
5. [新增工具文件](#新增工具文件)
6. [测试脚本](#测试脚本)
7. [使用指南](#使用指南)
8. [流量优化](#流量优化)
9. [故障排除](#故障排除)

## 概述

### 集成目标
- 监控训练和验证损失
- 记录超参数和实验配置
- 支持分布式训练
- 优化网络带宽使用
- 提供可选的可视化功能

### 技术架构
- 基于PyTorch Lightning的WandbLogger
- 符合Lightning官方最佳实践
- 自动处理分布式训练
- 配置驱动的功能开关

## 配置文件改造

### 文件：`config/config.yaml`

#### 新增WandB配置块

```yaml
# WandB配置
wandb:
  # 是否启用wandb日志记录
  enabled: true
  # 项目名称
  project: "scene-leap-diffusion-grasp"
  # 实验名称 (如果为null，将自动生成)
  name: null
  # 实验组名 (用于组织相关实验)
  group: null
  # 实验标签
  tags: 
    - "diffusion"
    - "grasp-generation"
    - "hand-pose"
  # 实验备注
  notes: "SceneLeap diffusion model training for grasp generation"
  # 是否保存模型到wandb (建议false以节省流量)
  save_model: false
  # 是否监控系统资源 (可选，会增加少量流量)
  monitor_system: false
  # 日志记录频率 (steps) - 增大以减少流量
  log_freq: 100
  # 是否在分布式训练中只在主进程记录
  log_only_main_process: true
  
  # 流量优化配置
  optimization:
    # 是否启用可视化 (图片上传消耗较多流量)
    enable_visualization: false
    # 可视化频率 (epoch) - 如果启用，建议设大一些
    visualization_freq: 20
    # 是否记录参数直方图 (消耗较多流量)
    log_histograms: false
    # 直方图记录频率 (epoch)
    histogram_freq: 50
    # 是否记录梯度信息
    log_gradients: false
    # 梯度记录频率 (steps)
    gradient_freq: 1000
    # 系统资源记录频率 (steps)
    system_freq: 500
  
  # 实验管理配置
  experiment:
    # 是否自动生成实验描述
    auto_description: true
    # 是否记录代码变更 (一次性，流量很小)
    log_code_changes: false
    # 是否记录环境信息 (一次性，流量很小)
    log_environment: true
    # 是否记录数据集信息 (一次性，流量很小)
    log_dataset_info: false
```

#### 配置说明

| 配置项 | 默认值 | 说明 | 流量影响 |
|--------|--------|------|----------|
| `enabled` | `true` | 是否启用wandb | - |
| `project` | `"scene-leap-diffusion-grasp"` | WandB项目名 | - |
| `log_freq` | `100` | 日志记录频率 | 高频率=高流量 |
| `enable_visualization` | `false` | 是否启用可视化 | ~100MB/1000epochs |
| `log_histograms` | `false` | 是否记录参数直方图 | ~500MB/1000epochs |
| `log_gradients` | `false` | 是否记录梯度信息 | ~5MB/1000epochs |
| `monitor_system` | `false` | 是否监控系统资源 | ~5MB/1000epochs |

## 训练脚本改造

### 文件：`train_lightning.py`

#### 1. 新增导入

```python
from pytorch_lightning.loggers import WandbLogger
from utils.wandb_callbacks import WandbVisualizationCallback, WandbMetricsCallback
```

#### 2. WandB Logger初始化


```python
# 初始化wandb logger
wandb_logger = None
if cfg.get("wandb", {}).get("enabled", False):
    # 生成实验名称
    exp_name = cfg.get("wandb", {}).get("name", None)
    if exp_name is None:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_name = cfg.model.name
        exp_name = f"{model_name}_{timestamp}"

    # 准备wandb配置
    wandb_config = OmegaConf.to_container(cfg, resolve=True)

    # 添加实验管理信息
    experiment_info = {
        "experiment_name": exp_name,
        "model_type": cfg.model.name,
        "dataset": cfg.data.name if hasattr(cfg.data, 'name') else "unknown",
        "batch_size": cfg.batch_size,
        "learning_rate": cfg.model.optimizer.lr if hasattr(cfg.model, 'optimizer') else "unknown",
        "epochs": cfg.epochs,
        "seed": cfg.seed,
        "git_commit": get_git_head_hash() or "unknown",
        "save_root": str(save_dir),
        "distributed": dist_config.get('enabled', False),
        "num_gpus": dist_config.get('devices', 1) if isinstance(dist_config.get('devices'), int) else len(dist_config.get('devices', [1])),
    }

    # 合并配置
    wandb_config.update(experiment_info)

    # 准备标签
    tags = cfg.get("wandb", {}).get("tags", [])
    tags.extend([
        cfg.model.name,
        f"batch_{cfg.batch_size}",
        f"lr_{cfg.model.optimizer.lr}" if hasattr(cfg.model, 'optimizer') else "lr_unknown",
        "distributed" if dist_config.get('enabled', False) else "single_gpu"
    ])

    # 根据PyTorch Lightning最佳实践，WandbLogger会自动处理分布式训练
    # 只有rank 0进程会实际记录到wandb，其他进程的logger会是None
    wandb_logger = WandbLogger(
        project=cfg.get("wandb", {}).get("project", "scene-leap-diffusion-grasp"),
        name=exp_name,
        group=cfg.get("wandb", {}).get("group", None),
        tags=list(set(tags)),  # 去重
        notes=cfg.get("wandb", {}).get("notes", ""),
        config=wandb_config,
        save_dir=str(lightning_log_dir),
        log_model=cfg.get("wandb", {}).get("save_model", False)
    )

    logging.info(f"Initialized WandB logger for project: {cfg.get('wandb', {}).get('project', 'scene-leap-diffusion-grasp')}")
    logging.info(f"Experiment name: {exp_name}")
else:
    logging.info("WandB logging disabled in configuration")
```

#### 3. 配置传递给模型


```python
# 初始化模型和数据模块
logging.info("Initializing model and data module...")

# 将wandb优化配置传递给模型
if cfg.get("wandb", {}).get("enabled", False):
    wandb_opt = cfg.get("wandb", {}).get("optimization", {})
    cfg.model.wandb_optimization = wandb_opt
    cfg.model.wandb_optimization.monitor_system = cfg.get("wandb", {}).get("monitor_system", False)

if cfg.model.name == "GraspCVAE":
    model = GraspCVAELightning(cfg.model)
elif cfg.model.name == "GraspDiffuser":
    model = DDPMLightning(cfg.model)
else:
    raise ValueError(f"Unknown model name: {cfg.model.name}")
```

#### 4. 回调函数配置


```python
# 添加WandB可视化回调（如果启用wandb且启用了可视化）
if cfg.get("wandb", {}).get("enabled", False):
    wandb_opt = cfg.get("wandb", {}).get("optimization", {})
    
    # 可视化回调（根据配置决定是否启用）
    if wandb_opt.get("enable_visualization", False):
        viz_callback = WandbVisualizationCallback(
            log_model_graph=True,
            log_sample_predictions=True,
            sample_log_freq=wandb_opt.get("visualization_freq", 20),
            max_samples_to_log=4
        )
        callbacks.append(viz_callback)
        logging.info("Added WandB visualization callback")
    
    # 指标回调（根据配置决定是否启用直方图）
    if wandb_opt.get("log_histograms", False):
        metrics_callback = WandbMetricsCallback(
            log_histograms=True,
            histogram_freq=wandb_opt.get("histogram_freq", 50)
        )
        callbacks.append(metrics_callback)
        logging.info("Added WandB metrics callback with histograms")
    
    logging.info("WandB callbacks configured for minimal bandwidth usage")
```

#### 5. Trainer配置


```python
trainer = pl.Trainer(
    # ... 其他配置 ...
    logger=wandb_logger,  # 启用WandB logger
    # ... 其他配置 ...
)
```

## 模型代码改造

### 文件：`models/diffuser_lightning.py`

#### 1. 新增导入

```python
from utils.distributed_utils import is_main_process
```

#### 2. 初始化时添加WandB配置


```python
# WandB优化配置参数（从配置中获取，避免在训练时访问trainer）
wandb_opt = cfg.get('wandb_optimization', {})
self._log_gradients = wandb_opt.get('log_gradients', False)
self._gradient_freq = wandb_opt.get('gradient_freq', 1000)
self._monitor_system = wandb_opt.get('monitor_system', False)
self._system_freq = wandb_opt.get('system_freq', 500)
```

#### 3. 训练步骤日志记录


```python
# 记录训练损失 - 使用train/前缀便于wandb组织
train_log_dict = {}
for k, v in loss_dict.items():
    train_log_dict[f"train/{k}"] = v
train_log_dict["train/total_loss"] = train_loss

self.log_dict(train_log_dict, prog_bar=True, logger=True, on_step=True, on_epoch=True, batch_size=B)

# 记录学习率和优化器信息
optimizer = self.optimizers()
self.log("train/lr", optimizer.param_groups[0]['lr'], prog_bar=False, logger=True, on_step=True, batch_size=B)

# 记录梯度信息（根据配置决定是否记录，减少流量消耗）
# 使用更安全的方式获取配置，避免在分布式训练中出现问题
log_gradients = getattr(self, '_log_gradients', False)
gradient_freq = getattr(self, '_gradient_freq', 1000)
    
if log_gradients and batch_idx % gradient_freq == 0:
    total_norm = 0
    param_count = 0
    for p in self.parameters():
        if p.grad is not None:
            param_norm = p.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
            param_count += 1
    if param_count > 0:
        total_norm = total_norm ** (1. / 2)
        self.log("train/grad_norm", total_norm, prog_bar=False, logger=True, on_step=True, batch_size=B)
```

#### 4. 验证步骤日志记录


```python
# 记录验证损失到wandb - 使用val/前缀便于组织
val_log_dict = {}
for k, v in val_detailed_loss.items():
    val_log_dict[f"val/{k}"] = v
val_log_dict["val/total_loss"] = avg_loss
val_log_dict["val/full_loss"] = avg_full_loss

self.log_dict(val_log_dict, prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size, sync_dist=True)

# 记录用于checkpoint保存的损失（不包含neg_loss）
self.log('val_loss', avg_loss, prog_bar=True, batch_size=self.batch_size, sync_dist=True)  # ModelCheckpoint监控

# 记录验证阶段的额外信息
self.log('val/epoch', float(self.current_epoch), prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size)

# 如果有学习率调度器，记录当前学习率
if hasattr(self, 'lr_schedulers') and self.lr_schedulers():
    current_lr = self.lr_schedulers().get_last_lr()[0] if hasattr(self.lr_schedulers(), 'get_last_lr') else self.optimizers().param_groups[0]['lr']
    self.log('val/lr', current_lr, prog_bar=False, logger=True, on_epoch=True, batch_size=self.batch_size)
```

#### 5. 系统资源监控


```python
# 记录系统资源信息（根据配置决定频率，减少流量消耗）
# 使用更安全的方式获取配置
monitor_system = getattr(self, '_monitor_system', False)
system_freq = getattr(self, '_system_freq', 500)
    
if monitor_system and batch_idx % system_freq == 0:
    # GPU内存使用情况
    if torch.cuda.is_available():
        gpu_memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        gpu_memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
        self.log("system/gpu_memory_allocated_gb", gpu_memory_allocated, prog_bar=False, logger=True, on_step=True, batch_size=B)
        self.log("system/gpu_memory_reserved_gb", gpu_memory_reserved, prog_bar=False, logger=True, on_step=True, batch_size=B)
    
    # 训练速度信息
    if hasattr(self.trainer, 'fit_loop') and hasattr(self.trainer.fit_loop, 'epoch_loop'):
        # 计算每秒处理的样本数
        if hasattr(self, '_last_log_time'):
            import time
            current_time = time.time()
            time_diff = current_time - self._last_log_time
            if time_diff > 0:
                samples_per_sec = B / time_diff
                self.log("system/samples_per_sec", samples_per_sec, prog_bar=False, logger=True, on_step=True, batch_size=B)
        
        import time
        self._last_log_time = time.time()
```

#### 6. 分布式训练兼容


```python
# 只在主进程打印详细日志（PyTorch Lightning会自动处理分布式训练）
if batch_idx % self.print_freq == 0 and self.trainer.is_global_zero:
```

## 新增工具文件

### 文件：`utils/wandb_callbacks.py`

这是一个全新的文件，提供WandB可视化和指标回调功能。

#### 主要功能

1. **WandbVisualizationCallback**: 处理模型可视化
   - 记录模型架构图
   - 记录样本预测结果
   - 创建手部姿态可视化图表

2. **WandbMetricsCallback**: 处理指标监控
   - 记录参数和梯度直方图
   - 监控模型参数分布

#### 关键特性

- 只在主进程执行（分布式训练兼容）
- 可配置的记录频率
- 错误处理和日志记录
- 内存友好的实现

### 文件结构

```python
class WandbVisualizationCallback(Callback):
    """WandB可视化回调，用于记录模型架构、损失曲线、样本可视化等"""

    def __init__(self, log_model_graph=True, log_sample_predictions=True,
                 sample_log_freq=5, max_samples_to_log=4):
        # 初始化参数

    def on_train_start(self, trainer, pl_module):
        # 记录模型架构图

    def on_validation_epoch_end(self, trainer, pl_module):
        # 记录样本预测结果

    def _log_model_graph(self, trainer, pl_module):
        # 实现模型图记录

    def _log_sample_predictions(self, trainer, pl_module):
        # 实现样本预测记录

    def _create_prediction_visualization(self, batch, predictions, epoch):
        # 创建预测结果可视化

class WandbMetricsCallback(Callback):
    """WandB指标回调，用于记录额外的训练指标和统计信息"""

    def __init__(self, log_histograms=True, histogram_freq=10):
        # 初始化参数

    def on_train_epoch_end(self, trainer, pl_module):
        # 记录参数和梯度直方图

    def _log_parameter_histograms(self, pl_module, epoch):
        # 实现参数直方图记录
```

## 测试脚本

### 文件：`test_wandb_simple.py`

这是一个全新的测试脚本，用于验证WandB集成的正确性。

#### 测试功能

1. **配置文件加载测试**: 验证`config.yaml`中的WandB配置
2. **WandB导入测试**: 检查WandB库是否正确安装
3. **离线模式测试**: 测试WandB离线功能
4. **PyTorch Lightning集成测试**: 验证WandbLogger集成
5. **带宽使用估算**: 计算不同配置下的流量消耗

#### 运行方法

```bash
python test_wandb_simple.py
```

#### 预期输出

```
🚀 WandB配置验证...
==================================================
📋 配置文件加载 ✅
📋 WandB导入 ✅
📋 WandB离线模式 ✅
📋 PyTorch Lightning集成 ✅
📋 带宽使用估算 ✅
==================================================
📊 测试结果: 5/5 通过
🎉 WandB配置验证通过！
```

## 使用指南

### 1. 环境准备

```bash
# 安装WandB
pip install wandb

# 登录WandB
wandb login
```

### 2. 配置设置

根据需要修改`config/config.yaml`中的WandB配置：

```yaml
wandb:
  enabled: true  # 启用WandB
  project: "your-project-name"  # 设置项目名
  optimization:
    enable_visualization: false  # 根据网络情况决定
    log_histograms: false       # 根据需要决定
```

### 3. 启动训练

```bash
# 单GPU训练
python train_lightning.py

# 分布式训练
python train_lightning.py trainer.devices=4 trainer.strategy=ddp
```

### 4. 查看结果

访问 https://wandb.ai 查看训练进度和结果。

## 流量优化

### 流量消耗对比表

| 功能 | 状态 | 单次大小 | 频率 | 1000epochs总计 |
|------|------|----------|------|----------------|
| 基础指标 | ✅启用 | ~250字节 | 每100步 | ~2.4MB |
| 数据可视化 | ❌禁用 | ~2MB | 每20epoch | ~100MB |
| 参数直方图 | ❌禁用 | ~5-10MB | 每50epoch | ~100-200MB |
| 模型保存 | ❌禁用 | 100MB-1GB+ | 按需 | 数GB |
| 系统监控 | ❌禁用 | ~50字节 | 每500步 | ~5MB |
| 梯度信息 | ❌禁用 | ~10字节 | 每1000步 | ~1MB |

### 优化建议

#### 🟢 低带宽环境（<10MB/小时）
```yaml
wandb:
  optimization:
    enable_visualization: false
    log_histograms: false
    log_gradients: false
    monitor_system: false
  log_freq: 200  # 降低记录频率
```

#### 🟡 中等带宽环境（10-50MB/小时）
```yaml
wandb:
  optimization:
    enable_visualization: true
    visualization_freq: 50  # 降低可视化频率
    log_histograms: false
    log_gradients: false
    monitor_system: true
    system_freq: 1000
```

#### 🟢 高带宽环境（>50MB/小时）
```yaml
wandb:
  optimization:
    enable_visualization: true
    visualization_freq: 10
    log_histograms: true
    histogram_freq: 20
    log_gradients: true
    gradient_freq: 500
    monitor_system: true
    system_freq: 200
```

## 故障排除

### 常见问题

#### 1. WandB登录失败
```bash
# 解决方案
export WANDB_API_KEY=your_api_key
wandb login
```

#### 2. 分布式训练中重复日志
**问题**: 多个进程都在记录日志
**解决方案**: 检查配置中的`log_only_main_process: true`

#### 3. 内存不足
**问题**: 可视化功能消耗过多内存
**解决方案**:
```yaml
wandb:
  optimization:
    enable_visualization: false
    log_histograms: false
```

#### 4. 网络连接问题
**问题**: 无法连接到WandB服务器
**解决方案**: 使用离线模式
```bash
export WANDB_MODE=offline
python train_lightning.py
```

#### 5. 配置不生效
**问题**: 修改配置后没有效果
**解决方案**: 检查配置文件路径和语法，重启训练

### 调试技巧

1. **启用详细日志**:
```bash
export WANDB_DEBUG=true
python train_lightning.py
```

2. **检查配置加载**:
```python
from omegaconf import OmegaConf
cfg = OmegaConf.load("config/config.yaml")
print(cfg.wandb)
```

3. **测试离线模式**:
```bash
python test_wandb_simple.py
```

## 总结

本次WandB集成改造涉及：

- **配置文件**: 1个文件修改
- **训练脚本**: 1个文件修改
- **模型代码**: 1个文件修改
- **新增工具**: 1个新文件
- **测试脚本**: 1个新文件
- **文档**: 1个新文件

所有改造都严格遵循PyTorch Lightning的最佳实践，确保：
- ✅ 分布式训练兼容性
- ✅ 最小化网络带宽使用
- ✅ 灵活的配置选项
- ✅ 完整的错误处理
- ✅ 详细的文档和测试

现在你可以安全地使用WandB监控训练过程，同时保持对流量消耗的精确控制！
```
