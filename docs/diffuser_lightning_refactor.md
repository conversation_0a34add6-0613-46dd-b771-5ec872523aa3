# `DDPMLightning` 代码重构说明

> 日期：2025-07-27

---

## 1. 背景
`models/diffuser_lightning.py` 是项目核心的扩散式手抓取模型实现。随着功能不断追加，代码逐渐膨胀 (>1 000 行)，出现了大量重复逻辑、魔术数字和状态副作用，维护成本陡增。为提升可读性与可维护性，我们对该文件进行了系统性重构。

## 2. 主要问题概述
| # | 问题 | 影响 |
|---|------|-------|
|1| 单/多抓取分支大量重复 (`*_single` / `*_multi`) | 代码臃肿、修改易漏|
|2| 魔术数字切片 (`3:19`, `:3`, `19:`) | 不易读，格式变化时易错|
|3| 采样函数临时修改实例状态 (`self.use_cfg` 等) | 有状态副作用，难以调试|
|4| 采样 / 后验 / CFG 逻辑散落在类中 | 难以定位与复用|
|5| 控制台日志与损失计算代码冗长 | 干扰核心逻辑|

## 3. 解决方案摘要
1. **统一张量形状处理**
   * 将所有 2 D `[B,D]` 输入在入口统一扩展为 3 D `[B,1,D]`，内部仅维护 3 D 逻辑，返回时再压缩。
   * 删除全部 `_single` / `_multi` 函数。
2. **消除魔术数字**
   * 在类级别新增常量 `TRANSLATION_SLICE = slice(0,3)`，`QPOS_SLICE = slice(3,19)`，`ROTATION_SLICE = slice(19,None)`。
   * 所有切片操作均改用具名常量。
3. **无状态采样器**
   * `sample()` 及其下游函数新增参数 (`use_cfg` 等)，不再修改实例属性。
   * 采样相关函数移动至 `DiffusionCoreMixin`，核心逻辑解耦。
4. **代码模块化**
   * 使用逻辑分区注释将类方法分组（Core / Sampling / Hooks / Inference / Helpers）。
   * 新增工具模块：
     * `utils.diffusion_core.DiffusionCoreMixin`
     * `utils.prediction.build_pred_dict_adaptive`
     * `utils.logging_helpers.log_validation_summary`
     * `utils.text_utils.convert_number_to_emoji`
5. **统一损失计算**
   * 新增 `_compute_loss()`，供 `forward_train` / `training_step` 复用。
6. **日志与辅助函数外部化**
   * 控制台彩色日志整理到 `log_validation_summary`。
   * Emoji 转换函数迁移到 `text_utils`。

## 4. 关键改动一览
- **类继承**：`DDPMLightning(DiffusionCoreMixin, LightningModule)`
- **新增常量**：`TRANSLATION_SLICE`, `QPOS_SLICE`, `ROTATION_SLICE`
- **删除文件内部函数**：`_model_predict_single`, `_model_predict_multi`, `_p_mean_variance_single`, `_p_mean_variance_multi`, `_build_single_grasp_pred_dict` 等。
- **新增工具模块**：见 §3-4。
- **函数签名变化**：
  - `sample(data, k, use_cfg, guidance_scale, …)` 无副作用。
  - 所有采样链路 (`p_sample*`, `p_mean_variance*`) 增加配置参数。
- **训练 / 验证流程**：
  - 统一损失计算，减少重复代码。
  - 预测字典由 `build_pred_dict_adaptive` 生成，避免每处手写。

## 5. 结果与收益
| 维度 | 改进前 | 改进后 |
|------|--------|--------|
| 文件行数 | ~1 080 行 | ~820 行 |
| 重复函数 | 8+ | 0 |
| 单元测试可覆盖率 | **待补充** | ↑（逻辑集中、易于 mock） |
| 采样接口 | 有副作用 | 无副作用 |

## 6. 后续工作
1. 将 `DiffusionCoreMixin` 抽离为完全独立的 `Sampler` 类，支持多模型复用。
2. 为关键函数（`q_sample`, `sample`, `_compute_loss` 等）补充单元测试。
3. 对日志模块进行进一步解耦，引入统一的日志格式化器。

---

**文档维护**：如果后续对 `DDPMLightning` 模型进行新的结构调整，请同步更新本文件。 