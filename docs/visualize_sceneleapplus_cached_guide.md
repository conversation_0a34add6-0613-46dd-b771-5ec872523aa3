# SceneLeapPlusDatasetCached 可视化指南

本指南介绍如何使用 `visualize_sceneleapplus_cached.py` 来可视化和分析 SceneLeapPlusDatasetCached 数据集。

## 概述

SceneLeapPlusDatasetCached 是一个支持多抓取并行学习的缓存数据集，具有以下特性：

- **多抓取支持**: 每个样本包含固定数量的抓取姿态 (num_grasps, 23)
- **SE3矩阵**: 每个样本包含对应的SE3变换矩阵 (num_grasps, 4, 4)
- **HDF5缓存**: 高效的数据存储和加载
- **6D点云**: xyz坐标 + rgb颜色信息
- **物体网格**: 目标物体的顶点和面数据
- **文本条件**: 正面和负面提示词

## 文件结构

```
├── visualize_sceneleapplus_cached.py          # 主可视化脚本
├── tests/test_visualize_sceneleapplus_cached.py    # 测试脚本
├── examples/example_visualize_sceneleapplus_cached.py  # 示例脚本
└── docs/visualize_sceneleapplus_cached_guide.md       # 本指南
```

## 快速开始

### 1. 基础使用

```bash
# 运行主可视化脚本
python visualize_sceneleapplus_cached.py

# 运行测试
python tests/test_visualize_sceneleapplus_cached.py

# 运行示例
python examples/example_visualize_sceneleapplus_cached.py
```

### 2. 数据路径配置

确保以下路径存在并包含有效数据：

```python
ROOT_DIR = "/home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15"
SUCC_GRASP_DIR = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
OBJ_ROOT_DIR = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
```

## 主要功能

### 1. 创建和测试缓存数据集

```python
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

dataset = SceneLeapPlusDatasetCached(
    root_dir=ROOT_DIR,
    succ_grasp_dir=SUCC_GRASP_DIR,
    obj_root_dir=OBJ_ROOT_DIR,
    num_grasps=6,  # 每个样本6个抓取
    mode="camera_centric",
    max_grasps_per_object=10,
    mesh_scale=0.1,
    num_neg_prompts=4,
    enable_cropping=True,
    max_points=15000,
    grasp_sampling_strategy="random",
    cache_version="v1.0_plus"
)
```

### 2. 可视化样本

```python
from visualize_sceneleapplus_cached import visualize_cached_sample

# 基础可视化（显示前3个抓取）
visualize_cached_sample(dataset, sample_idx=0)

# 指定抓取索引
visualize_cached_sample(dataset, sample_idx=0, grasp_indices=[0, 2, 4])

# 自定义最大显示数量
visualize_cached_sample(dataset, sample_idx=0, max_grasps_to_show=5)
```

## 可视化组件

### 1. 点云可视化
- **彩色点云**: 6D点云数据 (xyz + rgb)
- **高亮显示**: 可选择高亮目标物体区域

### 2. 手部模型可视化
- **多抓取支持**: 同时显示多个抓取姿态
- **颜色区分**: 不同抓取使用不同颜色
  - 蓝色: 第1个抓取
  - 紫色: 第2个抓取
  - 青色: 第3个抓取
  - 橙色: 第4个抓取
  - 等等...

### 3. 物体网格可视化
- **绿色mesh**: 目标物体的3D网格
- **实体/线框**: 支持实体和线框显示模式

### 4. 坐标系
- **RGB坐标轴**: 世界坐标系参考

## 支持的坐标模式

1. **camera_centric**: 相机坐标系（原始）
2. **object_centric**: 物体模型坐标系
3. **camera_centric_obj_mean_normalized**: 相机坐标系 + 物体中心归一化
4. **camera_centric_scene_mean_normalized**: 相机坐标系 + 场景中心归一化

## 交互式使用

### 1. 主脚本交互模式

运行主脚本后，可以选择以下选项：

```
1. 创建和测试 SceneLeapPlusDatasetCached
2. 测试不同坐标系统模式
3. 数据一致性测试
4. 交互式可视化
5. 运行所有测试
```

### 2. 交互式可视化

在交互式模式中，可以使用以下格式：

```
# 显示样本0的前3个抓取
0

# 显示样本1的第1、3、5个抓取
1 [0,2,4]

# 显示样本2的第2、4个抓取
2 [1,3]

# 退出
q
```

## 示例用法

### 示例1: 基础可视化

```python
# 创建数据集
dataset = SceneLeapPlusDatasetCached(
    root_dir=ROOT_DIR,
    succ_grasp_dir=SUCC_GRASP_DIR,
    obj_root_dir=OBJ_ROOT_DIR,
    num_grasps=6,
    mode="camera_centric",
    cache_version="v1.0_example"
)

# 可视化第一个样本
visualize_cached_sample(dataset, sample_idx=0, max_grasps_to_show=3)
```

### 示例2: 指定抓取可视化

```python
# 只显示特定的抓取姿态
specific_grasps = [0, 2, 4]  # 第1、3、5个抓取
visualize_cached_sample(dataset, sample_idx=0, grasp_indices=specific_grasps)
```

### 示例3: 不同模式对比

```python
modes = ["camera_centric", "object_centric", "camera_centric_obj_mean_normalized"]

for mode in modes:
    dataset = SceneLeapPlusDatasetCached(
        root_dir=ROOT_DIR,
        succ_grasp_dir=SUCC_GRASP_DIR,
        obj_root_dir=OBJ_ROOT_DIR,
        num_grasps=4,
        mode=mode,
        cache_version=f"v1.0_{mode}"
    )
    visualize_cached_sample(dataset, sample_idx=0)
```

## 数据分析功能

### 1. 样本数据分析

```python
from visualize_sceneleapplus_cached import analyze_sample_data

sample = dataset[0]
analyzed_sample = analyze_sample_data(sample, 0)
```

输出包括：
- 基本信息（场景ID、物体代码、提示词）
- 数据形状分析
- SceneLeapPlus特性验证
- 多抓取数据格式检查

### 2. 缓存信息查看

```python
cache_info = dataset.get_cache_info()
print(f"缓存路径: {cache_info['cache_path']}")
print(f"缓存状态: {cache_info['cache_loaded']}")
```

## 故障排除

### 1. 数据路径问题

如果遇到路径错误，请检查：
- 数据目录是否存在
- 权限是否正确
- 路径配置是否正确

### 2. 内存问题

如果遇到内存不足：
- 减少 `max_points` 参数
- 减少 `max_grasps_per_object` 参数
- 减少 `num_grasps` 参数

### 3. 可视化问题

如果Open3D可视化失败：
- 检查是否安装了Open3D
- 检查是否有图形界面支持
- 尝试减少可视化的数据量

## 性能优化

### 1. 缓存优化

- 使用合适的 `cache_version` 避免重复创建缓存
- 定期清理旧的缓存文件
- 使用SSD存储缓存文件

### 2. 可视化优化

- 限制同时显示的抓取数量
- 减少点云密度
- 使用线框模式显示复杂网格

## 扩展功能

### 1. 自定义颜色方案

可以修改 `create_hand_meshes` 函数中的颜色列表来自定义抓取颜色。

### 2. 添加新的可视化组件

可以扩展 `visualize_cached_sample` 函数来添加新的可视化元素。

### 3. 批量可视化

可以创建脚本来批量处理多个样本的可视化。

## 相关文件

- `datasets/sceneleapplus_cached.py`: 缓存数据集实现
- `datasets/sceneleapplus_dataset.py`: 原始数据集实现
- `utils/hand_model_origin.py`: 手部模型工具
- `visualize_sceneleappro_cached.py`: 单抓取版本的可视化工具
