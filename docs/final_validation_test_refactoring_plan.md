# 验证和测试阶段多抓取重构最终实施计划

## 重构目标与范围

### 核心目标
将验证和测试阶段从单抓取推理模式重构为并行多抓取推理模式，与训练阶段的多抓取架构保持完全一致。

### 技术变化
- **数据格式**: 假设数据加载器已提供符合要求的固定大小 `[B, num_grasps, pose_dim]` 格式
- **推理模式**: 从单次生成单个预测转向一次生成多个抓取预测
- **匹配算法**: 从1对多匹配升级为多对多最优分配
- **评估方式**: 从单点评估转向分布级评估（高级指标如Top-K、多样性等将在后续实现）
- **损失聚合**: 支持mean、sum、weighted等多种聚合策略

### 向后兼容性
所有修改必须保持对现有单抓取模式的完全兼容，通过维度检查自动切换处理逻辑。

## 实施计划概览

重构任务按依赖关系分为3个阶段，共12个原子任务：

1. **阶段1: 数据预处理层重构** (2个任务) - 基础数据格式验证
2. **阶段2: 模型推理层重构** (5个任务) - 推理接口适配
3. **阶段3: 损失匹配层重构** (5个任务) - 匹配算法升级（重点）

---

## 阶段1: 数据预处理层重构

### 任务1.1: 数据格式假设验证
**状态**: [ ] 未开始
**优先级**: � 中


**目标**: 验证数据加载器提供的数据格式符合多抓取要求

**假设条件**:
- 数据加载器已经提供固定大小的多抓取数据
- 数据格式: `hand_model_pose: [B, num_grasps, 23]`
- 数据格式: `se3: [B, num_grasps, 4, 4]`
- 数据格式: `norm_pose: [B, num_grasps, pose_dim]`
- 数据格式: `scene_pc: [B, max_points, 6]`

**验证内容**:
```python
def verify_data_format_assumptions(batch):
    """验证数据加载器提供的数据格式"""
    # 验证基本键存在
    required_keys = ['hand_model_pose', 'se3', 'scene_pc']
    for key in required_keys:
        assert key in batch, f"Missing required key: {key}"

    # 验证多抓取维度
    assert batch['hand_model_pose'].dim() == 3, "hand_model_pose应为3维"
    assert batch['se3'].dim() == 4, "se3应为4维"

    # 验证维度一致性
    B1, ng1, _ = batch['hand_model_pose'].shape
    B2, ng2, _, _ = batch['se3'].shape
    assert B1 == B2 and ng1 == ng2, "批次大小和抓取数量应一致"

    print(f"✅ 数据格式验证通过: B={B1}, num_grasps={ng1}")
    return True
```

**涉及文件**:
- `tests/test_data_format_assumptions.py` (新建)

### 任务1.2: process_hand_pose_test函数适配
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 任务1.1

**目标**: 确保process_hand_pose_test函数能正确处理多抓取数据

**涉及文件**:
- `utils/hand_helper.py` (行599-666)

**具体修改**:
```python
def process_hand_pose_test(data, rot_type, mode):
    """
    处理验证/测试数据，支持多抓取格式
    假设输入数据已经是正确的多抓取格式
    """
    # 检查数据格式
    if 'hand_model_pose' in data and data['hand_model_pose'].dim() == 3:
        # 多抓取格式: [B, num_grasps, 23]
        # 直接调用原有处理逻辑，process_hand_pose应该能处理3维输入
        return process_hand_pose(data, rot_type, mode)

    elif isinstance(data.get('hand_model_pose'), list):
        # 如果仍然是列表格式，转换为张量格式
        # 这种情况应该很少见，因为假设数据加载器已经处理好了
        processed_poses = []
        processed_se3s = []

        for poses, se3s in zip(data['hand_model_pose'], data['se3']):
            processed_poses.append(poses)
            processed_se3s.append(se3s)

        data['hand_model_pose'] = torch.stack(processed_poses)
        data['se3'] = torch.stack(processed_se3s)

        return process_hand_pose(data, rot_type, mode)

    else:
        # 单抓取格式（向后兼容）
        return process_hand_pose(data, rot_type, mode)
```

**验证标准**:
- 正确处理多抓取张量格式
- 向后兼容单抓取格式
- 输出norm_pose维度正确

---

## 阶段2: 模型推理层重构

### 任务2.1: 重构validation_step方法
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 阶段1完成

**目标**: 修改验证步骤支持多抓取并行推理

**涉及文件**:
- `models/diffuser_lightning.py` (行558-649)

**具体修改**:
```python
def validation_step(self, batch, batch_idx):
    """验证步骤：支持多抓取并行推理"""
    # 获取配置参数
    num_grasps = getattr(self, 'num_grasps', 8)
    sampling_strategy = getattr(self, 'val_sampling_strategy', 'best')
    
    # 数据预处理 - 转换为固定多抓取格式
    batch = process_hand_pose_test(
        batch, 
        rot_type=self.rot_type, 
        mode=self.mode,
        num_grasps=num_grasps,
        sampling_strategy=sampling_strategy,
        deterministic=True
    )
    
    # 推理生成
    pred_x0 = self.sample(batch)  # [B, k, T+1, num_grasps, pose_dim] 或 [B, k, T+1, pose_dim]
    pred_x0 = pred_x0[:,0,-1]     # 取第一个采样的最后时间步
    
    # 根据维度确定处理方式
    if pred_x0.dim() == 3:
        # 多抓取模式: [B, num_grasps, pose_dim]
        B, num_grasps_pred, pose_dim = pred_x0.shape
        batch_size = B * num_grasps_pred
        
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 25:] if pose_dim > 25 else pred_x0[..., 23:],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 6:25] if pose_dim > 25 else pred_x0[..., 6:23],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    else:
        # 单抓取模式（向后兼容）
        batch_size = self.batch_size
        pred_dict = self._build_single_grasp_pred_dict(pred_x0)
    
    # 损失计算
    loss_dict = self.criterion(pred_dict, batch, mode='val')
    loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)
    
    # 日志记录
    self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
    self.validation_step_outputs.append({
        "loss": loss.item(),
        "loss_dict": {k: v.item() for k, v in loss_dict.items()}
    })
    
    return {"loss": loss, "loss_dict": loss_dict}
```

**验证标准**:
- 多抓取数据正确处理
- 损失计算无错误
- 日志记录batch_size正确
- 向后兼容单抓取模式

### 任务2.2: 重构test_step方法
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 任务2.1

**目标**: 修改测试步骤支持多抓取评估

**涉及文件**:
- `models/diffuser_lightning.py` (行651-669)

**具体修改**: 类似validation_step的逻辑，但使用test模式进行指标计算

### 任务2.3: 重构forward_infer系列方法
**状态**: [ ] 未开始
**优先级**: 🟡 中
**前置任务**: 任务2.1

**目标**: 修改推理接口支持多抓取输出

**涉及文件**:
- `models/diffuser_lightning.py` (行755+)

**修改方法**:
- `forward_infer_step()`
- `forward_get_pose_matched()`
- `forward_get_pose()`

### 任务2.4: 创建自适应预测字典构建函数
**状态**: [ ] 未开始
**优先级**: 🟢 中
**前置任务**: 任务2.1

**目标**: 创建统一的预测字典构建函数

**涉及文件**:
- `models/diffuser_lightning.py`

### 任务2.5: 推理层单元测试
**状态**: [ ] 未开始
**优先级**: 🟢 中
**前置任务**: 任务2.1-2.4

**目标**: 创建推理层的全面测试

**涉及文件**:
- `tests/test_multi_grasp_inference.py` (新建)



---

## 阶段3: 损失匹配层重构（核心重点）

### 任务3.1: 深度分析现有匹配器实现
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 阶段2完成

**目标**: 深入理解现有匹配器的工作机制，为重构做准备

**分析要点**:

#### **当前Matcher.forward()实现分析**:
```python
# 当前实现假设：
# - preds["rotation"].shape = [B, nqueries, rot_dim]  # nqueries通常为1（单预测）
# - targets["norm_pose"].shape = [B, num_targets, pose_dim]  # num_targets可变

# 关键问题：
# 1. 当前nqueries=1，即每个场景只有1个预测
# 2. 匹配是1对多：1个预测匹配到最佳的1个目标
# 3. 成本矩阵形状：[B, 1, num_targets]
# 4. 匈牙利算法在每个batch内独立运行
```

#### **需要解决的核心问题**:
1. **维度扩展**: `nqueries`从1扩展到`num_grasps`（如8）
2. **匹配策略**: 从1对多变为多对多最优分配
3. **成本计算**: 成本矩阵从`[B, 1, num_targets]`变为`[B, num_grasps, num_targets]`
4. **分配结果**: 需要处理多个预测的分配结果

#### **现有代码的优势**:
- 匈牙利算法框架已经存在
- 成本计算函数已经模块化
- 批处理逻辑已经实现

**涉及文件**:
- `models/loss/matcher.py` (详细分析行16-69)

### 任务3.2: 重构Matcher.forward()支持多预测匹配
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 任务3.1

**目标**: 修改匹配器核心算法支持多预测vs多目标的最优匹配

**涉及文件**:
- `models/loss/matcher.py` (行16-69)

**详细实现策略**:

#### **Step 1: 输入维度检测和统一**
```python
@torch.no_grad()
def forward(self, preds, targets):
    """支持多预测与多目标的最优匹配"""
    device = preds["rotation"].device

    # 检测预测格式并统一处理
    if preds["rotation"].dim() == 3:
        # 多抓取模式: [B, num_grasps, rot_dim]
        batch_size, nqueries = preds["rotation"].shape[:2]
        is_multi_grasp = True
    elif preds["rotation"].dim() == 2:
        # 单抓取模式: [B, rot_dim] -> 扩展为 [B, 1, rot_dim]
        batch_size = preds["rotation"].shape[0]
        nqueries = 1
        is_multi_grasp = False

        # 统一维度：将所有预测张量扩展一个维度
        for key in preds:
            if isinstance(preds[key], torch.Tensor) and preds[key].dim() == 2:
                preds[key] = preds[key].unsqueeze(1)  # [B, 1, D]
    else:
        raise ValueError(f"Unexpected prediction dimension: {preds['rotation'].dim()}")
```

#### **Step 2: 目标格式统一**
```python
    # 统一目标格式
    if targets["norm_pose"].dim() == 2:
        # 单目标格式: [B, pose_dim] -> [B, 1, pose_dim]
        targets["norm_pose"] = targets["norm_pose"].unsqueeze(1)
        num_targets = 1
    elif targets["norm_pose"].dim() == 3:
        # 多目标格式: [B, num_targets, pose_dim]
        num_targets = targets["norm_pose"].shape[1]
    else:
        raise ValueError(f"Unexpected target dimension: {targets['norm_pose'].dim()}")

    # 计算有效目标掩码
    valid_mask = (targets["norm_pose"].abs().sum(dim=-1) > 1e-6)  # [B, num_targets]
```

#### **Step 3: 成本矩阵计算**
```python
    # 初始化成本矩阵 [num_cost_types, B, nqueries, num_targets]
    cost_matrices = torch.zeros(
        len(self.weight_dict),
        batch_size,
        nqueries,
        num_targets,
        device=device
    )

    # 计算各项成本（现有函数已支持多维度）
    for i, (name, weight) in enumerate(self.weight_dict.items()):
        cost_func = getattr(self, f"get_{name}_cost_mat")
        if name == "rotation":
            cost_matrices[i] = cost_func(
                preds, targets, weight, self.rot_type, valid_mask
            )
        else:
            cost_matrices[i] = cost_func(
                preds, targets, weight, valid_mask
            )
```

#### **Step 4: 多对多匈牙利分配**
```python
    # 合并成本矩阵
    final_cost = cost_matrices.sum(0).cpu().numpy()  # [B, nqueries, num_targets]

    # 执行匈牙利算法分配
    assignments = []
    per_query_gt_inds = torch.zeros([batch_size, nqueries], dtype=torch.int64, device=device)
    query_matched_mask = torch.zeros([batch_size, nqueries], dtype=torch.int64, device=device)

    for b in range(batch_size):
        valid_targets = valid_mask[b].sum().item()
        if valid_targets > 0:
            # 关键改进：多对多匹配
            # 成本矩阵: [nqueries, valid_targets]
            cost_matrix = final_cost[b, :, :valid_targets]

            # 匈牙利算法：找到最优分配
            # 注意：如果nqueries > valid_targets，部分预测不会被分配
            # 如果nqueries < valid_targets，部分目标不会被分配
            assign = linear_sum_assignment(cost_matrix)
            assign = [torch.from_numpy(x).long().to(device) for x in assign]

            # 记录分配结果
            per_query_gt_inds[b, assign[0]] = assign[1]
            query_matched_mask[b, assign[0]] = 1
            assignments.append(assign)
        else:
            # 没有有效目标
            assignments.append([
                torch.tensor([], dtype=torch.long, device=device),
                torch.tensor([], dtype=torch.long, device=device)
            ])

    return {
        "final_cost": final_cost,
        "assignments": assignments,
        "per_query_gt_inds": per_query_gt_inds,
        "query_matched_mask": query_matched_mask,
        "is_multi_grasp": is_multi_grasp,
        "nqueries": nqueries,
        "num_targets": num_targets
    }
```

**关键改进点**:
1. **自动维度检测**: 支持单抓取和多抓取输入
2. **统一处理流程**: 内部统一为3维张量处理
3. **多对多分配**: 匈牙利算法处理多预测vs多目标
4. **向后兼容**: 单抓取模式完全兼容

**验证标准**:
- 单抓取模式结果与原实现一致
- 多抓取模式正确执行多对多分配
- 成本矩阵计算正确
- 分配结果格式正确

### 任务3.3: 重构get_matched_by_assignment支持多预测提取
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 任务3.2

**目标**: 修改匹配结果提取函数支持多预测格式

**涉及文件**:
- `models/loss/grasp_loss_pose.py` (行464-517)

**详细实现策略**:

#### **当前实现问题分析**:
```python
# 当前get_matched_by_assignment假设：
# - predictions中的张量形状为[B, D]（单预测）
# - targets中的张量形状为[B, num_targets, D]（多目标）
# - 匹配结果：每个batch最多1个匹配对

# 需要支持的新格式：
# - predictions: [B, num_grasps, D]（多预测）
# - targets: [B, num_targets, D]（多目标）
# - 匹配结果：每个batch可能有多个匹配对
```

#### **重构实现**:
```python
def get_matched_by_assignment(self, predictions: Dict, targets: Dict, assignment: Dict) -> Tuple[Dict, Dict]:
    """
    根据匹配分配提取对应的预测和目标
    支持多预测vs多目标匹配
    """
    per_query_gt_inds = assignment["per_query_gt_inds"]  # [B, nqueries]
    query_matched_mask = assignment["query_matched_mask"]  # [B, nqueries]
    is_multi_grasp = assignment.get("is_multi_grasp", False)
    nqueries = assignment.get("nqueries", 1)

    K = query_matched_mask.long().sum()  # K = 总匹配数量
    B = query_matched_mask.size(0)

    matched_preds, matched_targets = {}, {}

    pred_target_match_key_map = {
        "pred_pose_norm": "norm_pose",
        "hand_model_pose": "hand_model_pose",
    }

    for pred_key, target_key in pred_target_match_key_map.items():
        if pred_key not in predictions.keys():
            continue

        pred = predictions[pred_key]
        target = targets[target_key]

        # 统一预测数据维度
        if pred.dim() == 2 and is_multi_grasp:
            # 单预测但在多抓取上下文中: [B, D] -> [B, 1, D]
            pred = pred.unsqueeze(1)
        elif pred.dim() == 3:
            # 已经是多预测格式: [B, nqueries, D]
            pass
        elif pred.dim() == 2:
            # 单预测单目标格式: [B, D] -> [B, 1, D]
            pred = pred.unsqueeze(1)
        else:
            raise ValueError(f"Unexpected prediction dimension for {pred_key}: {pred.shape}")

        # 统一目标数据维度
        if target.dim() == 2:
            # 单目标格式: [B, D] -> [B, 1, D]
            target = target.unsqueeze(1)
        elif target.dim() == 3:
            # 多目标格式: [B, num_targets, D]
            pass
        else:
            raise ValueError(f"Unexpected target dimension for {target_key}: {target.shape}")

        # 提取匹配对
        matched_pred_buffer = []
        matched_target_buffer = []

        for i in range(B):
            _matched_pred, _matched_target = self._get_matched_multi_grasp(
                pred[i],  # [nqueries, D]
                target[i],  # [num_targets, D]
                per_query_gt_inds[i],  # [nqueries]
                query_matched_mask[i],  # [nqueries]
            )
            matched_pred_buffer.append(_matched_pred)
            matched_target_buffer.append(_matched_target)

        # 合并所有batch的匹配结果
        if len(matched_pred_buffer) > 0 and matched_pred_buffer[0].numel() > 0:
            matched_preds[pred_key] = torch.cat(matched_pred_buffer, dim=0)  # [total_matched, D]
            matched_targets[target_key] = torch.cat(matched_target_buffer, dim=0)  # [total_matched, D]
        else:
            # 处理没有匹配的情况
            device = pred.device
            matched_preds[pred_key] = torch.zeros(0, pred.shape[-1], device=device)
            matched_targets[target_key] = torch.zeros(0, target.shape[-1], device=device)

    return matched_preds, matched_targets

def _get_matched_multi_grasp(self, pred, target, per_query_gt_inds, query_matched_mask):
    """
    提取单个batch的匹配对（多抓取版本）

    Args:
        pred: [nqueries, D] 预测
        target: [num_targets, D] 目标
        per_query_gt_inds: [nqueries] 每个query对应的target索引
        query_matched_mask: [nqueries] query是否被匹配的掩码

    Returns:
        matched_pred: [num_matched, D]
        matched_target: [num_matched, D]
    """
    matched_indices = query_matched_mask.nonzero(as_tuple=True)[0]  # 被匹配的query索引

    if len(matched_indices) == 0:
        # 没有匹配，返回空张量
        return torch.zeros(0, pred.shape[-1], device=pred.device, dtype=pred.dtype), \
               torch.zeros(0, target.shape[-1], device=target.device, dtype=target.dtype)

    matched_pred = pred[matched_indices]  # [num_matched, D]
    matched_target_indices = per_query_gt_inds[matched_indices]  # [num_matched]
    matched_target = target[matched_target_indices]  # [num_matched, D]

    return matched_pred, matched_target
```

**关键改进点**:
1. **维度统一处理**: 自动检测并统一预测和目标的维度
2. **多匹配支持**: 支持每个batch内多个匹配对
3. **批处理优化**: 高效处理多个batch的匹配结果
4. **错误处理**: 完善的维度检查和错误提示

### 任务3.4: 重构损失计算函数支持多抓取聚合
**状态**: [ ] 未开始
**优先级**: 🔴 高
**前置任务**: 任务3.3

**目标**: 修改所有损失函数支持多抓取格式和聚合策略

**涉及文件**:
- `models/loss/grasp_loss_pose.py` (行573-687及其他损失函数)

**详细实现策略**:

#### **需要修改的损失函数列表**:
1. `get_para_loss()` - 参数重建损失
2. `get_noise_loss()` - 噪声预测损失
3. `get_translation_loss()` - 平移损失
4. `get_qpos_loss()` - 关节角度损失
5. `get_rotation_loss()` - 旋转损失
6. `get_neg_loss()` - 负样本损失

#### **统一的多抓取损失处理模式**:
```python
def get_para_loss(self, prediction, target) -> Dict[str, Tensor]:
    """参数重建损失 - 多抓取版本"""
    pred_para = prediction['matched']['pred_pose_norm']
    para = target['matched']['norm_pose']

    # 检测数据格式
    if pred_para.dim() == 2 and para.dim() == 2:
        # 单抓取格式: [K, pose_dim] (K为匹配数量)
        para_loss = F.mse_loss(pred_para, para)

    elif pred_para.dim() == 2 and para.dim() == 2:
        # 多抓取匹配后格式: [total_matched, pose_dim]
        # 这是get_matched_by_assignment输出的格式
        para_loss = F.mse_loss(pred_para, para)

    else:
        raise ValueError(f"Unexpected tensor dimensions: pred {pred_para.shape}, target {para.shape}")

    return {"para": para_loss}

def get_translation_loss(self, prediction, target) -> Dict[str, Tensor]:
    """平移损失 - 多抓取版本"""
    pred = prediction['matched']['pred_pose_norm'][...,:3]
    target_trans = target['matched']['norm_pose'][...,:3]

    # 匹配后的数据应该是[total_matched, 3]格式
    translation_loss = F.mse_loss(pred, target_trans)

    return {"translation": translation_loss}

def get_qpos_loss(self, prediction, target) -> Dict[str, Tensor]:
    """关节角度损失 - 多抓取版本"""
    pred = prediction['matched']['pred_pose_norm'][...,3:19]
    target_qpos = target['matched']['norm_pose'][...,3:19]

    # 匹配后的数据应该是[total_matched, 16]格式
    qpos_loss = F.mse_loss(pred, target_qpos)

    return {"qpos": qpos_loss}

def get_rotation_loss(self, prediction, target) -> Dict[str, Tensor]:
    """旋转损失 - 多抓取版本"""
    pred_rot = prediction['matched']['pred_pose_norm'][...,19:]
    target_rot = target['matched']['norm_pose'][...,19:]

    # 根据旋转类型计算损失
    if self.rot_type == 'r6d':
        rotation_loss = F.mse_loss(pred_rot, target_rot)
    elif self.rot_type == 'quat':
        # 四元数损失：1 - |<q1, q2>|
        rotation_loss = 1 - torch.abs(torch.sum(pred_rot * target_rot, dim=-1)).mean()
    else:
        rotation_loss = F.mse_loss(pred_rot, target_rot)

    return {"rotation": rotation_loss}
```

#### **关键设计决策**:

**1. 损失聚合策略简化**:
- 由于`get_matched_by_assignment`已经提取了匹配对，损失函数接收的是`[total_matched, D]`格式
- 不需要在损失函数内部处理多抓取聚合，直接计算匹配对的损失即可
- 这样设计更简洁，避免了复杂的聚合逻辑

**2. 向后兼容性**:
- 所有损失函数保持相同的接口
- 自动检测输入格式并适配处理
- 单抓取模式完全兼容

**3. 错误处理**:
- 完善的维度检查
- 清晰的错误信息
- 边界情况处理（如没有匹配的情况）

### 任务3.5: 匹配层综合测试
**状态**: [ ] 未开始
**优先级**: 🟡 中
**前置任务**: 任务3.1-3.4

**目标**: 创建匹配层的全面测试，确保重构正确性

**涉及文件**:
- `tests/test_multi_grasp_matcher.py` (新建)

**测试内容**:
1. **单抓取兼容性测试**: 确保单抓取模式结果不变
2. **多抓取匹配测试**: 验证多对多匹配算法正确性
3. **边界情况测试**: 无匹配、部分匹配等情况
4. **损失计算测试**: 验证各损失函数在多抓取下的正确性
5. **性能测试**: 确保重构后性能可接受

```python
def test_multi_grasp_matcher_compatibility():
    """测试多抓取匹配器的向后兼容性"""
    # 创建单抓取测试数据
    single_preds = create_single_grasp_predictions()
    single_targets = create_single_grasp_targets()

    # 测试原始匹配器
    original_matcher = OriginalMatcher(weight_dict, rot_type)
    original_result = original_matcher(single_preds, single_targets)

    # 测试新匹配器
    new_matcher = MultiGraspMatcher(weight_dict, rot_type)
    new_result = new_matcher(single_preds, single_targets)

    # 验证结果一致性
    assert torch.allclose(original_result['final_cost'], new_result['final_cost'])
    assert torch.equal(original_result['query_matched_mask'], new_result['query_matched_mask'])

def test_multi_grasp_matching():
    """测试多抓取匹配功能"""
    # 创建多抓取测试数据
    multi_preds = create_multi_grasp_predictions(B=2, num_grasps=4)
    multi_targets = create_multi_grasp_targets(B=2, num_targets=6)

    # 执行匹配
    matcher = MultiGraspMatcher(weight_dict, rot_type)
    result = matcher(multi_preds, multi_targets)

    # 验证匹配结果
    assert result['per_query_gt_inds'].shape == (2, 4)  # [B, num_grasps]
    assert result['query_matched_mask'].shape == (2, 4)
    assert result['is_multi_grasp'] == True

    # 验证匹配逻辑正确性
    for b in range(2):
        matched_queries = result['query_matched_mask'][b].sum().item()
        assert matched_queries <= min(4, 6)  # 不超过预测数和目标数的最小值
```

**验证标准**:
- 所有测试用例通过
- 单抓取模式结果与原实现一致
- 多抓取模式逻辑正确
- 边界情况处理正确
- 性能满足要求



---

## 风险评估与缓解策略

### 高风险任务
1. **任务3.1 (Matcher重构)**: 核心算法变化
   - **缓解**: 先实现向后兼容版本，逐步迁移
2. **任务2.1 (validation_step重构)**: 影响验证流程
   - **缓解**: 保持原有接口，内部逻辑适配

### 中风险任务
1. **任务1.2 (数据预处理重构)**: 可能影响数据流
   - **缓解**: 充分测试，保持默认参数向后兼容

### 关键依赖关系
- 阶段1必须完成后才能开始阶段2
- 阶段2和阶段3可以并行进行
- 阶段4依赖前三个阶段完成

## 验证策略

### 单元测试
每个任务都包含对应的单元测试，确保功能正确性

### 集成测试
阶段4提供端到端集成测试，验证整体功能

### 性能测试
包含内存使用、推理速度、准确性对比

### 向后兼容测试
确保所有修改不破坏现有单抓取功能

## 预期收益

1. **架构一致性**: 验证/测试与训练使用相同多抓取架构
2. **评估准确性**: 多对多匹配提供更准确的性能评估
3. **推理效率**: 并行生成多个候选抓取
4. **系统完整性**: 端到端多抓取分布学习架构

---

## 详细任务实现指南

### 阶段3详细实现

#### 任务3.1: 重构Matcher核心算法 (详细实现)

**具体修改代码**:
```python
@torch.no_grad()
def forward(self, preds, targets):
    """执行多预测与多目标的最优匹配"""
    device = preds["rotation"].device

    # 获取维度信息并统一格式
    if preds["rotation"].dim() == 3:
        # 多抓取模式: [B, num_grasps, rot_dim]
        batch_size, nqueries = preds["rotation"].shape[:2]
        is_multi_grasp = True
    else:
        # 单抓取模式: [B, rot_dim] -> 扩展为 [B, 1, rot_dim]
        batch_size = preds["rotation"].shape[0]
        nqueries = 1
        is_multi_grasp = False

        # 扩展维度以统一处理
        for key in preds:
            if isinstance(preds[key], torch.Tensor) and preds[key].dim() == 2:
                preds[key] = preds[key].unsqueeze(1)  # [B, 1, D]

    # 计算有效目标掩码
    if targets["norm_pose"].dim() == 3:
        valid_mask = (targets["norm_pose"].abs().sum(dim=-1) > 1e-6)  # [B, num_targets]
    else:
        valid_mask = (targets["norm_pose"].abs().sum(dim=-1) > 1e-6).unsqueeze(1)  # [B, 1]

    # 初始化成本矩阵
    max_targets = targets["norm_pose"].size(1) if targets["norm_pose"].dim() == 3 else 1
    cost_matrices = torch.zeros(
        len(self.weight_dict),
        batch_size,
        nqueries,
        max_targets,
        device=device
    )

    # 计算各项成本
    for i, (name, weight) in enumerate(self.weight_dict.items()):
        cost_func = getattr(self, f"get_{name}_cost_mat")
        if name == "rotation":
            cost_matrices[i] = cost_func(
                preds, targets, weight, self.rot_type, valid_mask
            )
        else:
            cost_matrices[i] = cost_func(
                preds, targets, weight, valid_mask
            )

    # 合并成本矩阵
    final_cost = cost_matrices.sum(0).cpu().numpy()  # [B, nqueries, max_targets]

    # 匈牙利算法求解最优分配
    assignments = []
    per_query_gt_inds = torch.zeros(
        [batch_size, nqueries], dtype=torch.int64, device=device
    )
    query_matched_mask = torch.zeros(
        [batch_size, nqueries], dtype=torch.int64, device=device
    )

    for b in range(batch_size):
        valid_targets = valid_mask[b].sum().item()
        if valid_targets > 0:
            # 多对多匹配：nqueries个预测 vs valid_targets个目标
            cost_matrix = final_cost[b, :, :valid_targets]
            assign = linear_sum_assignment(cost_matrix)
            assign = [torch.from_numpy(x).long().to(device) for x in assign]

            per_query_gt_inds[b, assign[0]] = assign[1]
            query_matched_mask[b, assign[0]] = 1
            assignments.append(assign)
        else:
            # 没有有效目标
            assignments.append([
                torch.tensor([], dtype=torch.long, device=device),
                torch.tensor([], dtype=torch.long, device=device)
            ])

    return {
        "final_cost": final_cost,
        "assignments": assignments,
        "per_query_gt_inds": per_query_gt_inds,
        "query_matched_mask": query_matched_mask,
        "is_multi_grasp": is_multi_grasp
    }
```

#### 任务3.2: 重构损失计算函数 (详细实现)

**修改get_para_loss函数**:
```python
def get_para_loss(self, prediction, target) -> Dict[str, Tensor]:
    """计算参数重建损失，支持多抓取聚合"""
    pred_para = prediction['matched']['pred_pose_norm']
    para = target['matched']['norm_pose']

    # 处理多抓取格式
    if para.dim() == 3:
        # 多抓取格式: [B, num_grasps, pose_dim]
        B, num_grasps, pose_dim = para.shape
        mse_loss = F.mse_loss(pred_para, para, reduction='none')  # [B, num_grasps, pose_dim]

        # 应用聚合策略
        if self.loss_aggregation == 'mean':
            para_loss = mse_loss.mean()
        elif self.loss_aggregation == 'sum':
            para_loss = mse_loss.sum() / B  # 按批次归一化
        elif self.loss_aggregation == 'weighted':
            # 使用抓取权重（如果可用）
            weights = target.get('grasp_weights', torch.ones(B, num_grasps, device=para.device))
            weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
            para_loss = weighted_loss.sum() / weights.sum()
        else:
            para_loss = mse_loss.mean()

        # 应用有效性掩码（如果存在）
        if 'grasp_validity_mask' in target:
            validity_mask = target['grasp_validity_mask']  # [B, num_grasps]
            if validity_mask is not None:
                # 重新计算损失，只考虑有效抓取
                valid_loss = mse_loss.mean(dim=-1) * validity_mask.float()  # [B, num_grasps]
                para_loss = valid_loss.sum() / validity_mask.sum().clamp(min=1)
    else:
        # 单抓取格式（向后兼容）
        para_loss = F.mse_loss(pred_para, para)

    return {"para": para_loss}
```

#### 任务3.3: 重构匹配结果处理 (详细实现)

**修改get_matched_by_assignment函数**:
```python
def get_matched_by_assignment(self, predictions: Dict, targets: Dict, assignment: Dict) -> Tuple[Dict, Dict]:
    """
    根据匹配分配提取对应的预测和目标
    支持多预测vs多目标匹配
    """
    per_query_gt_inds = assignment["per_query_gt_inds"]  # [B, num_queries]
    query_matched_mask = assignment["query_matched_mask"]  # [B, num_queries]
    is_multi_grasp = assignment.get("is_multi_grasp", False)

    K = query_matched_mask.long().sum()  # K = 总匹配数量
    B = query_matched_mask.size(0)

    matched_preds, matched_targets = {}, {}

    pred_target_match_key_map = {
        "pred_pose_norm": "norm_pose",
        "hand_model_pose": "hand_model_pose",
    }

    for pred_key, target_key in pred_target_match_key_map.items():
        if pred_key not in predictions.keys():
            continue

        pred = predictions[pred_key]
        target = targets[target_key]

        # 处理预测数据维度
        if pred.dim() == 2 and is_multi_grasp:
            # 单预测模式但在多抓取上下文中: [B, D] -> [B, 1, D]
            pred = pred.unsqueeze(1)
        elif pred.dim() == 3:
            # 已经是多预测格式: [B, num_queries, D]
            pass
        else:
            # 单预测单目标格式: [B, D]
            pred = pred.unsqueeze(1)

        # 处理目标数据维度
        if target.dim() == 2:
            # 单目标格式: [B, D] -> [B, 1, D]
            target = target.unsqueeze(1)

        matched_pred_buffer = []
        matched_target_buffer = []

        for i in range(B):
            _matched_pred, _matched_target = self._get_matched_multi_grasp(
                pred[i],  # [num_queries, D]
                target[i],  # [num_targets, D]
                per_query_gt_inds[i],  # [num_queries]
                query_matched_mask[i],  # [num_queries]
            )
            matched_pred_buffer.append(_matched_pred)
            matched_target_buffer.append(_matched_target)

        if len(matched_pred_buffer) > 0 and matched_pred_buffer[0].numel() > 0:
            matched_preds[pred_key] = torch.cat(matched_pred_buffer, dim=0)
            matched_targets[target_key] = torch.cat(matched_target_buffer, dim=0)
        else:
            # 处理没有匹配的情况
            device = pred.device
            matched_preds[pred_key] = torch.zeros(0, pred.shape[-1], device=device)
            matched_targets[target_key] = torch.zeros(0, target.shape[-1], device=device)

    return matched_preds, matched_targets

def _get_matched_multi_grasp(self, pred, target, per_query_gt_inds, query_matched_mask):
    """
    提取单个batch的匹配对（多抓取版本）

    Args:
        pred: [num_queries, D] 预测
        target: [num_targets, D] 目标
        per_query_gt_inds: [num_queries] 每个query对应的target索引
        query_matched_mask: [num_queries] query是否被匹配的掩码

    Returns:
        matched_pred: [num_matched, D]
        matched_target: [num_matched, D]
    """
    matched_indices = query_matched_mask.nonzero(as_tuple=True)[0]  # 被匹配的query索引

    if len(matched_indices) == 0:
        # 没有匹配，返回空张量
        return torch.zeros(0, pred.shape[-1], device=pred.device, dtype=pred.dtype), \
               torch.zeros(0, target.shape[-1], device=target.device, dtype=target.dtype)

    matched_pred = pred[matched_indices]  # [num_matched, D]
    matched_target_indices = per_query_gt_inds[matched_indices]  # [num_matched]
    matched_target = target[matched_target_indices]  # [num_matched, D]

    return matched_pred, matched_target
```

### 阶段4详细实现

#### 任务4.1: 端到端集成测试 (详细实现)

**创建完整测试脚本**:
```python
def test_validation_test_end_to_end():
    """端到端验证和测试流程测试"""

    print("🚀 开始端到端集成测试...")

    # 1. 配置加载和模型初始化
    print("1. 初始化模型和配置...")
    cfg = create_test_config()
    cfg.model.multi_grasp = {
        'enabled': True,
        'num_grasps': 4,
        'loss_aggregation': 'mean'
    }

    model = DDPMLightning(cfg.model)
    model.eval()

    # 2. 创建测试数据
    print("2. 创建测试数据...")
    val_batch = create_multi_grasp_validation_batch(B=2, num_grasps=6)  # 变长输入
    test_batch = create_multi_grasp_test_batch(B=2, num_grasps=8)

    # 验证输入数据格式
    print(f"   输入数据格式: hand_model_pose {val_batch['hand_model_pose'][0].shape}")
    print(f"   输入数据格式: se3 {val_batch['se3'][0].shape}")

    # 3. 数据预处理测试
    print("3. 测试数据预处理...")
    processed_batch = process_hand_pose_test(
        val_batch,
        rot_type='r6d',
        mode='camera_centric',
        num_grasps=4,
        sampling_strategy='best',
        deterministic=True
    )

    # 验证预处理后格式
    assert processed_batch['hand_model_pose'].shape == (2, 4, 23), \
        f"预处理后格式错误: {processed_batch['hand_model_pose'].shape}"
    assert processed_batch['norm_pose'].shape == (2, 4, 25), \
        f"norm_pose格式错误: {processed_batch['norm_pose'].shape}"
    assert 'grasp_validity_mask' in processed_batch, "缺少有效性掩码"

    print("   ✅ 数据预处理格式正确")

    # 4. 验证流程测试
    print("4. 测试验证流程...")
    with torch.no_grad():
        val_result = model.validation_step(processed_batch, 0)

    assert 'loss' in val_result, "验证结果缺少loss"
    assert 'loss_dict' in val_result, "验证结果缺少loss_dict"
    assert val_result['loss'].item() >= 0, f"验证损失为负: {val_result['loss'].item()}"

    print(f"   ✅ 验证损失: {val_result['loss'].item():.4f}")
    print(f"   ✅ 损失组成: {list(val_result['loss_dict'].keys())}")

    # 5. 测试流程测试
    print("5. 测试测试流程...")
    with torch.no_grad():
        test_result = model.test_step(processed_batch, 0)

    assert test_result is not None, "测试结果为空"
    print("   ✅ 测试流程完成")

    # 6. 推理接口测试
    print("6. 测试推理接口...")

    # forward_infer测试
    with torch.no_grad():
        preds_hand, targets_hand = model.forward_infer(processed_batch, k=2)

    assert preds_hand is not None, "推理结果为空"
    assert targets_hand is not None, "目标结果为空"
    print("   ✅ forward_infer接口正常")

    # forward_get_pose测试
    with torch.no_grad():
        outputs, targets = model.forward_get_pose(processed_batch, k=2)

    assert outputs is not None, "姿态输出为空"
    assert targets is not None, "姿态目标为空"
    print("   ✅ forward_get_pose接口正常")

    # 7. 向后兼容性测试
    print("7. 测试向后兼容性...")
    single_batch = create_single_grasp_batch(B=2)

    with torch.no_grad():
        single_val_result = model.validation_step(single_batch, 0)
        single_test_result = model.test_step(single_batch, 0)

    assert 'loss' in single_val_result, "单抓取验证失败"
    assert single_test_result is not None, "单抓取测试失败"
    print("   ✅ 向后兼容性测试通过")

    # 8. 数据流一致性验证
    print("8. 验证数据流一致性...")

    # 追踪完整数据流
    with torch.no_grad():
        # 推理
        pred_x0 = model.sample(processed_batch)
        print(f"   推理输出形状: {pred_x0.shape}")

        # 提取最终预测
        pred_final = pred_x0[:,0,-1]
        print(f"   最终预测形状: {pred_final.shape}")

        # 验证维度一致性
        if pred_final.dim() == 3:
            B, num_grasps, pose_dim = pred_final.shape
            assert B == 2, f"批次大小错误: {B}"
            assert num_grasps == 4, f"抓取数量错误: {num_grasps}"
            print(f"   ✅ 多抓取维度正确: [B={B}, num_grasps={num_grasps}, pose_dim={pose_dim}]")
        else:
            print(f"   ✅ 单抓取维度: {pred_final.shape}")

    print("🎉 端到端集成测试全部通过!")

    return {
        'validation_loss': val_result['loss'].item(),
        'loss_components': list(val_result['loss_dict'].keys()),
        'data_flow_verified': True,
        'backward_compatibility': True
    }
```

## 实施检查清单

### 阶段1检查项
- [ ] 数据格式假设验证通过
- [ ] `process_hand_pose_test`函数适配多抓取格式

### 阶段2检查项
- [ ] `validation_step`支持多抓取推理
- [ ] `test_step`支持多抓取评估
- [ ] 所有`forward_*`方法适配多抓取
- [ ] 预测字典构建函数统一
- [ ] 推理层测试全部通过

### 阶段3检查项（重点）
- [ ] 现有匹配器实现深度分析完成
- [ ] `Matcher.forward`支持多对多匹配
- [ ] `get_matched_by_assignment`处理多预测
- [ ] 所有损失函数支持多抓取格式
- [ ] 匹配层综合测试验证正确性

## 质量保证

### 代码质量
- 所有函数包含详细文档字符串
- 类型提示完整
- 错误处理健壮
- 日志记录充分

### 测试覆盖
- 单元测试覆盖率 > 90%
- 集成测试覆盖主要流程
- 边界条件测试
- 性能回归测试

### 文档更新
- API文档更新
- 配置说明更新
- 使用示例更新
- 迁移指南编写

## 重构计划总结

### 🎯 **核心改进**

本重构计划经过深度分析和优化，重点解决以下关键问题：

1. **数据流简化**: 假设数据加载器已提供正确格式，专注于核心算法重构
2. **匹配器升级**: 从1对多匹配升级为多对多最优分配，这是最关键的技术挑战
3. **损失函数适配**: 统一处理多抓取格式，保持向后兼容性
4. **实现细节完备**: 提供详细的代码实现和验证标准

### 🔧 **技术重点**

#### **最关键任务 - 阶段3**:
- **任务3.2**: Matcher.forward()重构 - 核心匹配算法
- **任务3.3**: get_matched_by_assignment重构 - 匹配结果处理
- **任务3.4**: 损失函数重构 - 多抓取损失计算

这三个任务是整个重构的核心，需要特别谨慎的实现和充分的测试。

#### **设计原则**:
1. **向后兼容**: 所有修改保持对单抓取模式的完全兼容
2. **维度统一**: 内部统一为3维张量处理，简化逻辑
3. **错误处理**: 完善的维度检查和错误提示
4. **测试驱动**: 每个修改都有对应的测试验证

### 📋 **实施保障**

1. **原子任务**: 每个任务独立完成，便于code agent执行
2. **详细实现**: 提供具体的函数实现代码和修改策略
3. **验证标准**: 明确的完成标准和测试要求
4. **风险控制**: 重点关注核心算法的正确性

### 🚀 **预期成果**

完成重构后，验证和测试阶段将：
- 支持并行多抓取推理
- 使用多对多最优匹配算法
- 提供更准确的性能评估
- 与训练阶段架构完全一致

通过这个详细的分阶段实施计划，可以确保多抓取验证和测试重构的系统性、安全性和可验证性。每个任务都有明确的目标、具体的实现代码和验证标准，便于code agent逐个完成。
