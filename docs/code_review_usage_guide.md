# SceneLeapPlus多抓取重构代码审查使用指南

## 概述

本指南提供了使用代码审查工具进行SceneLeapPlus多抓取重构复盘和修正的详细步骤。

## 工具概览

### 1. 主要文档
- `docs/multi_grasp_refactoring_code_review_plan.md` - 详细的模块划分和审查计划
- `docs/code_review_usage_guide.md` - 本使用指南

### 2. 审查工具
- `scripts/review_module.py` - 模块审查工具
- `scripts/check_refactoring_status.py` - 重构状态检查工具
- `scripts/create_test_template.py` - 测试模板创建工具

## 使用流程

### 第一步: 检查整体重构状态

首先运行状态检查工具，了解当前重构的整体进度：

```bash
# 检查整体重构状态
python scripts/check_refactoring_status.py
```

这将输出：
- 各模块的完成度百分比
- 关键函数的多抓取支持状态
- 配置文件的参数检查结果
- 总体进度和建议

### 第二步: 按模块进行详细审查

根据状态检查结果，按照建议的顺序逐个模块进行审查：

#### 模块1: 数据处理模块
```bash
# 审查数据处理模块
python scripts/review_module.py --module 1

# 如果需要跳过测试运行
python scripts/review_module.py --module 1 --no-tests
```

#### 模块2: 手部姿态处理模块
```bash
# 审查手部姿态处理模块
python scripts/review_module.py --module 2
```

#### 模块3: 模型架构模块
```bash
# 审查模型架构模块
python scripts/review_module.py --module 3
```

#### 其他模块
```bash
# 审查损失计算模块
python scripts/review_module.py --module 4

# 审查训练流程模块
python scripts/review_module.py --module 5

# 审查推理流程模块
python scripts/review_module.py --module 6

# 审查配置管理模块
python scripts/review_module.py --module 7
```

### 第三步: 创建缺失的测试文件

如果发现缺少测试文件，使用测试模板创建工具：

```bash
# 创建手部姿态处理测试
python scripts/create_test_template.py --test hand_pose_processing

# 创建模型架构测试
python scripts/create_test_template.py --test multi_grasp_model

# 创建损失计算测试
python scripts/create_test_template.py --test multi_grasp_loss
```

### 第四步: 运行测试验证修改

在修改代码后，运行相应的测试验证：

```bash
# 运行特定测试
python tests/test_hand_pose_processing.py
python tests/test_multi_grasp_model.py
python tests/test_multi_grasp_loss.py

# 运行现有的数据集测试
python tests/test_sceneleapplus_dataset.py
python tests/test_sceneleapplus_cached.py
```

## 详细审查检查清单

### 每个模块的通用检查项

#### 1. 数据维度处理
- [ ] 输入数据维度检测正确 (`dim() == 2` vs `dim() == 3`)
- [ ] 多抓取数据重塑逻辑正确 (`view`, `reshape`)
- [ ] 批次大小计算考虑了`num_grasps`因子
- [ ] 输出数据维度符合预期

#### 2. 向后兼容性
- [ ] 单抓取模式仍能正常工作
- [ ] 配置开关控制多抓取/单抓取模式
- [ ] 现有API接口保持不变

#### 3. 错误处理
- [ ] 边界情况处理 (num_grasps=0, 数据缺失等)
- [ ] 异常捕获和错误信息
- [ ] 数据验证和类型检查

#### 4. 性能优化
- [ ] 内存使用效率
- [ ] 计算复杂度合理
- [ ] 避免不必要的数据复制

### 模块特定检查项

#### 模块1: 数据处理模块
```python
# 检查 SceneLeapPlusDataset.__getitem__
def __getitem__(self, idx):
    # ✅ 返回固定数量的抓取
    # ✅ hand_model_pose: [num_grasps, 23]
    # ✅ se3: [num_grasps, 4, 4]
    # ✅ scene_pc: [max_points, 6]
    pass

# 检查 _sample_grasps_from_available
def _sample_grasps_from_available(self, available_grasps):
    # ✅ 三种采样策略实现
    # ✅ 边界情况处理
    # ✅ 返回维度正确
    pass

# 检查 collate_fn
@staticmethod
def collate_fn(batch):
    # ✅ 批次堆叠逻辑
    # ✅ 输出维度: [B, num_grasps, ...]
    pass
```

#### 模块2: 手部姿态处理模块
```python
# 检查 process_hand_pose
def process_hand_pose(data, rot_type, mode):
    # ✅ 多抓取路径 vs 单抓取路径
    # ✅ 维度检测和处理
    # ✅ 数据格式转换
    pass

# 检查 _process_batch_pose_logic
def _process_batch_pose_logic(se3, hand_model_pose_input, rot_type, mode):
    # ✅ 批量处理逻辑
    # ✅ 旋转表示转换
    # ✅ 维度重塑正确
    pass
```

#### 模块3: 模型架构模块
```python
# 检查 DDPMLightning.q_sample
def q_sample(self, x0, t, noise):
    # ✅ 时间步广播到多抓取
    # ✅ 噪声加入维度正确
    # ✅ 输出维度匹配输入
    pass

# 检查 UNetModel.forward
def forward(self, x_t, ts, data):
    # ✅ 多抓取输入处理
    # ✅ 抓取编码器适配
    # ✅ 注意力机制更新
    pass
```

#### 模块4: 损失计算模块
```python
# 检查 GraspLossPose.forward
def forward(self, pred_dict, batch, mode='train'):
    # ✅ 多抓取损失聚合
    # ✅ 一致性损失实现
    # ✅ 评估指标计算
    pass
```

## 常见问题和解决方案

### 1. 维度不匹配错误
**问题**: `RuntimeError: The size of tensor a (8) must match the size of tensor b (1)`

**解决方案**:
- 检查时间步广播逻辑
- 确认多抓取维度处理正确
- 验证张量重塑操作

### 2. 内存不足错误
**问题**: `CUDA out of memory`

**解决方案**:
- 减少批次大小或抓取数量
- 优化内存使用，避免不必要的张量复制
- 使用梯度累积

### 3. 配置参数缺失
**问题**: `KeyError: 'num_grasps'`

**解决方案**:
- 检查配置文件是否包含多抓取参数
- 确认参数传递路径正确
- 添加默认值处理

### 4. 向后兼容性问题
**问题**: 单抓取模式不工作

**解决方案**:
- 添加维度检测逻辑
- 实现单抓取和多抓取的分支处理
- 保持原有API接口不变

## 调试技巧

### 1. 数据流跟踪
```python
def trace_data_flow(data, stage_name):
    print(f"[{stage_name}] Data shapes:")
    for key, value in data.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
```

### 2. 维度检查
```python
def check_tensor_dims(tensor, expected_shape, name):
    assert tensor.shape == expected_shape, \
        f"{name} shape mismatch: {tensor.shape} vs {expected_shape}"
```

### 3. 内存监控
```python
def monitor_memory():
    if torch.cuda.is_available():
        print(f"GPU Memory: {torch.cuda.memory_allocated() / 1024**3:.2f}GB")
```

## 完成标准

### 模块完成标准
- [ ] 所有关键函数支持多抓取格式
- [ ] 单元测试通过
- [ ] 向后兼容性验证
- [ ] 性能基准测试通过

### 整体完成标准
- [ ] 所有模块完成度 > 90%
- [ ] 端到端集成测试通过
- [ ] 性能对比满足预期
- [ ] 文档和注释完整

## 下一步行动

1. **立即开始**: 运行状态检查工具了解当前进度
2. **按优先级**: 根据依赖关系按顺序审查模块
3. **持续验证**: 每完成一个模块就运行相关测试
4. **集成测试**: 所有模块完成后进行端到端测试
5. **性能优化**: 根据基准测试结果进行优化

通过系统性地使用这些工具和流程，您可以高效地完成SceneLeapPlus多抓取重构的代码审查和修正工作。
