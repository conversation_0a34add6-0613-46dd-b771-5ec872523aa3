# SceneLeapPlus 模型训练数据流处理分析

## 概述

本文档详细分析了SceneLeapPlus模型训练时的完整数据流处理流程，包括scene_pc（场景点云）、hand_model_pose（手部姿态）、正负向提示词等关键数据的维度变化和处理过程。

## 1. 数据加载阶段

### 1.1 数据集初始化
- **数据集类**: `SceneLeapProDatasetCached` (缓存版本) 或 `SceneLeapProDataset` (原始版本)
- **配置参数**:
  - `max_grasps_per_object`: 每个物体的最大抓取数量 (默认200)
  - `mesh_scale`: 物体网格缩放因子 (默认0.1)
  - `num_neg_prompts`: 负向提示词数量 (默认4)
  - `enable_cropping`: 是否启用点云裁剪 (默认True)
  - `max_points`: 最大点云点数 (默认10000)

### 1.2 原始数据结构
```
场景数据目录结构:
├── scene_xxx/
│   ├── depth_view_0.png          # 深度图像
│   ├── rgb_view_0.png            # RGB图像
│   ├── instance_map_view_0.png   # 实例分割图
│   ├── camera.json               # 相机参数
│   └── scene_gt.json             # 场景真值标注
├── succ_grasp_dir/               # 成功抓取数据
└── obj_root_dir/                 # 物体网格数据
```

## 2. 数据预处理阶段

### 2.1 点云生成与处理
**输入**: 深度图像 + RGB图像 + 相机参数
**处理流程**:
1. **深度图转点云**: `create_point_cloud_from_depth_image()`
   - 输入维度: 深度图 (H, W), RGB图 (H, W, 3)
   - 输出维度: 点云 (N, 3) - xyz坐标

2. **添加RGB信息**: `add_rgb_to_pointcloud()`
   - 输入: 点云 (N, 3) + RGB图像 (H, W, 3)
   - 输出: 6D点云 (N, 6) - xyz + rgb

3. **物体掩码映射**: `map_2d_mask_to_3d_pointcloud()`
   - 输入: 点云 (N, 3) + 2D掩码 (H, W)
   - 输出: 3D掩码 (N,) - 布尔值

4. **点云裁剪**: `crop_point_cloud_to_objects_with_mask()`
   - 根据物体掩码裁剪点云，保留目标物体周围区域

5. **点云下采样**: `downsample_point_cloud_with_mask()`
   - 输入: 6D点云 (N, 6) + 掩码 (N,)
   - 输出: 下采样后的6D点云 (max_points, 6)
   - **最终维度**: `scene_pc` - (max_points=10000, 6)

### 2.2 手部姿态处理
**原始数据格式**: 23维向量 [P(3) + Q_wxyz(4) + Joints(16)]
- P: 位置 (3维)
- Q_wxyz: 四元数旋转 (4维, w-x-y-z格式)
- Joints: 关节角度 (16维)

**重排序处理**: `_process_single_grasp()` / `_process_batch_grasps()`
1. **重新排列**: [P(3) + Joints(16) + Q_wxyz(4)] → 23维
2. **SE3矩阵生成**: `create_se3_matrix_from_pose()`
   - 输入: P(3) + Q_wxyz(4)
   - 输出: SE3变换矩阵 (4, 4)

**最终输出**:
- `hand_model_pose`: (23,) - 重排序后的手部姿态
- `se3`: (4, 4) - SE3变换矩阵

### 2.3 文本提示词处理
**正向提示词**:
- 来源: 从物体代码提取物体名称
- 处理: `extract_object_name_from_code()` + 下划线替换为空格
- **维度**: `positive_prompt` - 字符串

**负向提示词**:
- 来源: 场景中其他物体的名称
- 生成: `generate_negative_prompts()`
- 数量: `num_neg_prompts` (默认4个)
- **维度**: `negative_prompts` - List[str], 长度为4

## 3. 坐标变换阶段

### 3.1 变换策略
根据`mode`参数选择不同的坐标变换策略:
- `camera_centric`: 相机坐标系
- `object_centric`: 物体坐标系  
- `camera_centric_obj_mean_normalized`: 相机坐标系+物体中心归一化

### 3.2 变换数据流
**输入数据**:
- `pc_cam_raw_xyz_rgb`: 相机坐标系6D点云 (N, 6)
- `grasps_omf`: 物体模型坐标系抓取姿态 (23,)
- `obj_verts`: 物体模型坐标系顶点 (V, 3)
- `R_omf_to_cf_np`: 旋转矩阵 (3, 3)
- `t_omf_to_cf_np`: 平移向量 (3,)

**变换处理**: `_transform_data_by_mode()`
- 使用策略模式进行坐标变换
- 输出统一格式的变换后数据

## 4. 批处理阶段

### 4.1 数据整理
**单样本数据结构**:
```python
{
    'scene_pc': torch.Tensor(10000, 6),      # 6D点云
    'hand_model_pose': torch.Tensor(23,),    # 手部姿态
    'se3': torch.Tensor(4, 4),               # SE3变换矩阵
    'positive_prompt': str,                   # 正向提示词
    'negative_prompts': List[str],            # 负向提示词列表
    'object_mask': torch.Tensor(10000,),     # 物体掩码
    # ... 其他辅助信息
}
```

### 4.2 批处理整合
**DataLoader处理**: `collate_batch_data()`
- 使用自定义collate函数处理变长数据
- 对`hand_model_pose`和`se3`进行填充对齐

**批处理后维度**:
```python
batch = {
    'scene_pc': torch.Tensor(B, 10000, 6),        # 批量6D点云
    'hand_model_pose': torch.Tensor(B, max_N, 23), # 批量手部姿态(变长)
    'se3': torch.Tensor(B, max_N, 4, 4),          # 批量SE3矩阵(变长)
    'positive_prompt': List[str],                   # 批量正向提示词
    'negative_prompts': List[List[str]],           # 批量负向提示词
    'object_mask': torch.Tensor(B, 10000),        # 批量物体掩码
}
```

## 5. 模型输入预处理阶段

### 5.1 手部姿态标准化
**处理函数**: `process_hand_pose()`
1. **旋转表示转换**: 根据`rot_type`参数转换旋转表示
   - `quat`: 四元数 (4维)
   - `r6d`: 6D旋转表示 (6维)
   - `axis`: 轴角表示 (3维)
   - `euler`: 欧拉角 (3维)
   - `map`: SE3对数映射 (3维)

2. **姿态重组**: `_process_batch_pose_logic()`
   - 输入: SE3矩阵 (B, 4, 4) + 手部姿态 (B, 23)
   - 提取旋转部分并转换为指定表示
   - 重组为: [平移(3) + 关节(16) + 旋转(rot_dim)]

3. **数据标准化**: `norm_hand_pose_robust()`
   - 使用预计算的统计信息进行标准化
   - 输出范围: [-1, 1]

**输出维度**:
- `norm_pose`: (B, 3+16+rot_dim) - 标准化后的姿态
- `hand_model_pose`: (B, 3+16+rot_dim) - 处理后的姿态

### 5.2 文本编码
**编码器**: `PosNegTextEncoder`
1. **正向提示词编码**:
   - 输入: List[str] (批量正向提示词)
   - 编码器: CLIP文本编码器
   - 输出: (B, 512) - 正向文本特征

2. **负向提示词编码**:
   - 输入: List[List[str]] (批量负向提示词)
   - 处理: 展平后批量编码
   - 输出: (B, num_neg_prompts, 512) - 负向文本特征

### 5.3 场景特征提取
**骨干网络**: PointNet2 (`build_backbone()`)
1. **输入**: 6D点云 (B, 10000, 6)
2. **特征提取**: 多层PointNet2处理
3. **输出**: 场景特征 (B, feature_dim)

## 6. 模型训练阶段

### 6.1 条件信息融合
**融合模块**: `condition()` 方法
1. **文本条件处理**: `TextConditionProcessor`
   - 正向特征: (B, 512) → (B, context_dim)
   - 负向特征: (B, num_neg_prompts, 512) → (B, context_dim)

2. **场景-文本融合**: `CrossAttentionFusion`
   - 场景特征 + 文本特征 → 融合特征
   - 输出: (B, d_model)

### 6.2 扩散过程
**训练步骤**: `training_step()`
1. **噪声采样**:
   - 时间步采样: t ~ Uniform(0, T)
   - 噪声采样: ε ~ N(0, I)
   - 维度: (B, 3+16+rot_dim)

2. **前向扩散**: `q_sample()`
   - x_t = √(α̅_t) * x_0 + √(1-α̅_t) * ε
   - 输入: norm_pose (B, pose_dim)
   - 输出: x_t (B, pose_dim)

3. **噪声预测**: UNet模型
   - 输入: x_t (B, pose_dim) + t (B,) + 条件特征
   - 输出: 预测噪声 (B, pose_dim)

### 6.3 损失计算
**损失函数**: `GraspLossPose`
- 主要损失: MSE(预测噪声, 真实噪声)
- 负向提示词损失: 对比学习损失
- 总损失: 加权组合

## 7. 关键维度总结

| 数据类型 | 原始维度 | 处理后维度 | 批处理维度 | 说明 |
|---------|---------|-----------|-----------|------|
| 深度图 | (H, W) | - | - | 转换为点云 |
| RGB图 | (H, W, 3) | - | - | 融合到点云 |
| 点云 | (N, 3) | (10000, 6) | (B, 10000, 6) | xyz+rgb |
| 手部姿态 | (23,) | (23,) | (B, max_N, 23) | 重排序后 |
| SE3矩阵 | (4, 4) | (4, 4) | (B, max_N, 4, 4) | 变换矩阵 |
| 标准化姿态 | - | (pose_dim,) | (B, pose_dim) | 训练目标 |
| 正向提示词 | str | (512,) | (B, 512) | CLIP编码 |
| 负向提示词 | List[str] | (4, 512) | (B, 4, 512) | CLIP编码 |
| 场景特征 | - | (feature_dim,) | (B, feature_dim) | PointNet2提取 |
| 融合特征 | - | (d_model,) | (B, d_model) | 条件融合 |

其中 `pose_dim = 3 + 16 + rot_dim`，`rot_dim` 根据旋转表示类型确定：
- quat: 4维
- r6d: 6维  
- axis/euler/map: 3维

## 8. 数据流关键节点

1. **原始数据** → **点云生成** → **6D点云** (xyz+rgb)
2. **手部姿态** → **重排序** → **SE3变换** → **标准化**
3. **物体名称** → **文本提示词** → **CLIP编码** → **文本特征**
4. **多模态特征** → **交叉注意力融合** → **条件特征**
5. **条件特征** + **噪声姿态** → **UNet** → **噪声预测**
6. **预测噪声** vs **真实噪声** → **损失计算** → **反向传播**

这个数据流确保了从原始传感器数据到最终抓取姿态预测的完整处理链路，支持多模态条件生成和扩散模型训练。

## 9. 详细代码流程分析

### 9.1 数据集加载流程
```python
# 1. 数据集初始化
dataset = SceneLeapProDatasetCached(
    root_dir="/path/to/scenes",
    succ_grasp_dir="/path/to/grasps",
    obj_root_dir="/path/to/objects",
    mode="camera_centric",
    max_grasps_per_object=200,
    num_neg_prompts=4,
    max_points=10000
)

# 2. 单个样本获取
item = dataset[idx]  # 调用 __getitem__
```

### 9.2 单样本数据处理流程
```python
def __getitem__(self, idx):
    # 1. 加载场景图像和相机参数
    depth_img, rgb_img, camera = load_scene_images(scene_dir, view_idx)

    # 2. 生成点云
    pc_xyz = create_point_cloud_from_depth_image(depth_img, camera)  # (N, 3)
    pc_xyz_rgb = add_rgb_to_pointcloud(pc_xyz, rgb_img, camera)      # (N, 6)

    # 3. 物体掩码处理
    mask_2d = extract_object_mask(instance_map, object_id)           # (H, W)
    mask_3d = map_2d_mask_to_3d_pointcloud(pc_xyz, mask_2d, camera) # (N,)

    # 4. 点云裁剪和下采样
    if enable_cropping:
        pc_cropped = crop_point_cloud_to_objects_with_mask(pc_xyz_rgb, mask_3d)
    pc_final, mask_final = downsample_point_cloud_with_mask(
        pc_cropped, mask_3d, max_points=10000
    )  # (10000, 6), (10000,)

    # 5. 手部姿态处理
    grasp_pose = load_grasp_pose(grasp_file)  # (23,) [P(3) + Q(4) + J(16)]
    hand_model_pose = reorder_pose_components(grasp_pose)  # [P(3) + J(16) + Q(4)]
    se3_matrix = create_se3_matrix_from_pose(P, Q)  # (4, 4)

    # 6. 坐标变换
    transformed_data = transform_data_by_mode(
        pc_final, hand_model_pose, obj_verts, R_transform, t_transform
    )

    # 7. 文本提示词生成
    obj_name = extract_object_name_from_code(object_code)
    positive_prompt = obj_name.replace('_', ' ')
    negative_prompts = generate_negative_prompts(scene_objects, obj_name, 4)

    return {
        'scene_pc': torch.tensor(pc_final),           # (10000, 6)
        'hand_model_pose': torch.tensor(hand_model_pose),  # (23,)
        'se3': torch.tensor(se3_matrix),              # (4, 4)
        'positive_prompt': positive_prompt,           # str
        'negative_prompts': negative_prompts,         # List[str]
        'object_mask': torch.tensor(mask_final),      # (10000,)
    }
```

### 9.3 批处理数据流程
```python
def collate_fn(batch):
    # 1. 收集所有键
    keys = set().union(*[item.keys() for item in batch])
    collated = {}

    # 2. 特殊处理变长数据
    for key in keys:
        items = [item.get(key) for item in batch]

        if key == 'hand_model_pose':
            # 处理变长手部姿态: (N, 23) -> (B, max_N, 23)
            collated[key] = collate_variable_length_tensors(items, (23,))
        elif key == 'se3':
            # 处理变长SE3矩阵: (N, 4, 4) -> (B, max_N, 4, 4)
            collated[key] = collate_variable_length_tensors(items, (4, 4))
        elif key in ['positive_prompt', 'negative_prompts']:
            # 保持文本为列表
            collated[key] = items
        else:
            # 标准批处理
            collated[key] = torch.stack(items)

    return collated
```

### 9.4 模型训练数据流程
```python
def training_step(self, batch, batch_idx):
    # 1. 手部姿态预处理
    batch = process_hand_pose(batch, rot_type='quat', mode='camera_centric')
    # 输出: batch['norm_pose'] (B, 23), batch['hand_model_pose'] (B, 23)

    # 2. 扩散过程设置
    B = batch['norm_pose'].shape[0]
    t = torch.randint(0, self.timesteps, (B,))  # 随机时间步
    noise = torch.randn_like(batch['norm_pose'])  # 高斯噪声 (B, 23)

    # 3. 前向扩散
    x_t = self.q_sample(batch['norm_pose'], t, noise)  # (B, 23)

    # 4. 条件信息处理
    condition_dict = self.eps_model.condition(batch)
    # 包含: scene_features, text_features, neg_text_features等

    # 5. 噪声预测
    pred_noise = self.eps_model(x_t, t, condition_dict)  # (B, 23)

    # 6. 损失计算
    loss_dict = self.criterion({
        'pred_noise': pred_noise,
        'noise': noise,
        'neg_pred': condition_dict.get('neg_pred'),
        'neg_text_features': condition_dict.get('neg_text_features')
    }, batch)

    # 7. 总损失
    total_loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items())

    return total_loss
```

### 9.5 条件信息处理详细流程
```python
def condition(self, batch):
    # 1. 场景特征提取
    scene_pc = batch['scene_pc']  # (B, 10000, 6)
    scene_features = self.scene_model(scene_pc)  # (B, scene_dim)

    # 2. 文本编码
    pos_prompts = batch['positive_prompt']  # List[str]
    neg_prompts = batch['negative_prompts']  # List[List[str]]

    # 正向文本编码
    pos_features = self.text_encoder.encode_positive(pos_prompts)  # (B, 512)

    # 负向文本编码
    neg_features = self.text_encoder.encode_negative(neg_prompts)  # (B, 4, 512)

    # 3. 文本条件处理
    pos_condition = self.text_processor(pos_features)  # (B, context_dim)
    neg_condition = self.text_processor(neg_features.view(-1, 512))  # (B*4, context_dim)
    neg_condition = neg_condition.view(B, 4, -1)  # (B, 4, context_dim)

    # 4. 场景-文本融合
    fused_features = self.cross_attention_fusion(
        scene_features, pos_condition
    )  # (B, d_model)

    # 5. 负向提示词预测(用于对比学习)
    neg_pred = self.predict_negative_alignment(
        scene_features, neg_condition
    )  # (B, 4)

    return {
        'scene_features': scene_features,
        'text_features': pos_condition,
        'neg_text_features': neg_condition,
        'fused_features': fused_features,
        'neg_pred': neg_pred
    }
```

## 10. 关键技术细节

### 10.1 坐标变换策略
不同的坐标系模式影响数据的最终表示：

1. **camera_centric**:
   - 点云保持相机坐标系
   - 抓取姿态转换到相机坐标系
   - 适用于固定视角的抓取

2. **object_centric**:
   - 点云和抓取姿态都转换到物体坐标系
   - 消除物体位置和朝向的影响
   - 适用于物体中心的抓取学习

3. **camera_centric_obj_mean_normalized**:
   - 相机坐标系基础上进行物体中心归一化
   - 平衡了视角信息和物体中心对齐

### 10.2 数据标准化策略
使用预计算的统计信息进行标准化：
```python
# 统计信息文件: assets/formatch_overall_hand_pose_dimension_statistics_by_mode.json
{
    "camera_centric": [
        {"dimension_label": "translation_x", "min": -0.5, "max": 0.5},
        {"dimension_label": "translation_y", "min": -0.3, "max": 0.3},
        {"dimension_label": "translation_z", "min": 0.1, "max": 1.0},
        {"dimension_label": "joint_angle_0", "min": -1.57, "max": 1.57},
        // ... 更多维度
    ]
}

# 标准化公式: x_norm = (x - min) / (max - min) * 2 - 1  # 映射到[-1, 1]
```

### 10.3 负向提示词机制
负向提示词用于提高模型的区分能力：
1. **生成**: 从同一场景的其他物体名称中随机选择
2. **编码**: 使用相同的CLIP编码器
3. **损失**: 通过对比学习降低与负向提示词的相似度
4. **效果**: 提高模型对目标物体的专注度

### 10.4 缓存机制
为了提高训练效率，使用HDF5缓存预处理数据：
```python
# 缓存文件结构
cache.h5
├── item_0/
│   ├── scene_pc          # (10000, 6) float32
│   ├── hand_model_pose   # (23,) float32
│   ├── se3              # (4, 4) float32
│   ├── positive_prompt   # string
│   ├── negative_prompts  # string array
│   └── object_mask      # (10000,) bool
├── item_1/
│   └── ...
```

这个详细的数据流分析展示了从原始传感器数据到模型训练的完整处理链路，每个步骤都有明确的输入输出维度和处理逻辑。
