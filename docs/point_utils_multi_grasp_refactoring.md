# Point Utils 多抓取重构文档

## 概述

本文档描述了 `utils/point_utils.py` 的多抓取重构，使其能够支持多抓取格式 `[B, num_grasps, ...]` 的点云处理。

## 重构目标

1. **支持多抓取格式**：处理 `[B, num_grasps, N, 3]` 格式的点云数据
2. **批量处理优化**：提供高效的批量点云变换
3. **双后端支持**：同时支持 NumPy 和 PyTorch
4. **向后兼容**：保持原有接口的完全兼容性
5. **GPU加速**：充分利用 PyTorch 的 GPU 计算能力

## 主要功能

### 1. 多抓取点云变换

#### 核心函数
- `transform_points_multi_grasp()` - 统一接口函数
- `transform_points_batch_numpy()` - NumPy 批量变换
- `transform_points_batch_torch()` - PyTorch 批量变换

#### 支持的变换格式
```python
# 单变换单点云: [4,4] × [N,3] -> [N,3]
# 单变换多点云: [4,4] × [B,N,3] -> [B,N,3]  
# 多变换多点云: [B,4,4] × [B,N,3] -> [B,N,3]
# 多抓取变换: [B,num_grasps,4,4] × [B,N,3] -> [B,num_grasps,N,3]
```

#### 使用示例
```python
import torch
from utils.point_utils import transform_points_multi_grasp

# 多抓取变换
batch_size, num_grasps, num_points = 4, 8, 1000
se3_matrices = torch.randn(batch_size, num_grasps, 4, 4)
scene_points = torch.randn(batch_size, num_points, 3)

# 应用变换
transformed = transform_points_multi_grasp(se3_matrices, scene_points)
# 输出: [4, 8, 1000, 3]
```

### 2. 批量网格生成

#### 核心函数
- `get_points_in_grid_batch()` - 批量网格点生成

#### 功能特性
- 支持多个边界框的批量处理
- NumPy 和 PyTorch 双后端
- 灵活的网格密度控制

#### 使用示例
```python
# 定义边界框
bounds = torch.tensor([
    [[-1, -1, -1], [1, 1, 1]],  # 边界框1
    [[-2, -2, 0], [0, 0, 2]]    # 边界框2
])

# 生成网格
grid_points = get_points_in_grid_batch(bounds, (10, 10, 10))
# 输出: [2, 10, 10, 10, 3]
```

### 3. 点云距离计算

#### 核心函数
- `compute_point_distances()` - 批量点云距离计算

#### 功能特性
- 支持单点云和批量点云
- 高效的距离矩阵计算
- GPU 加速支持

#### 使用示例
```python
# 计算手部点云与物体点云的距离
hand_points = torch.randn(4, 100, 3)    # [B, N1, 3]
object_points = torch.randn(4, 200, 3)  # [B, N2, 3]

distances = compute_point_distances(hand_points, object_points)
# 输出: [4, 100, 200] - 距离矩阵
```

### 4. 表面采样

#### 核心函数
- `sample_points_on_surface()` - 网格表面采样

#### 功能特性
- 支持批量网格处理
- 可配置采样点数
- 简化的采样算法实现

### 5. SE(3) 变换应用

#### 核心函数
- `apply_se3_to_points()` - SE(3) 变换应用

#### 功能特性
- 专门针对 SE(3) 变换矩阵
- 支持多抓取格式
- 统一的接口设计

## 性能优化

### 1. 批量处理
- 使用向量化操作替代循环
- 充分利用 PyTorch 的批量矩阵乘法
- 内存布局优化

### 2. GPU 加速
- 自动检测 CUDA 设备
- 张量连续性保证
- 高效的内存管理

### 3. 性能基准
在 CUDA 设备上的性能测试结果：

| 批次大小 | 抓取数 | 总点数 | 平均耗时(ms) | 吞吐量(M点/s) |
|---------|--------|--------|-------------|--------------|
| 4       | 4      | 32K    | 0.07        | 432.47       |
| 16      | 8      | 256K   | 0.07        | 3658.40      |
| 64      | 16     | 2048K  | 0.34        | 5972.70      |

## 向后兼容性

### 保留的原始函数
- `transform_point()` - 单点变换
- `transform_points()` - 多点变换  
- `get_points_in_grid()` - 网格生成

### 兼容性保证
- 所有原有接口保持不变
- 函数签名完全兼容
- 输出格式一致

## 错误处理

### 输入验证
- 张量维度检查
- 数据类型验证
- 形状兼容性验证

### 错误信息
- 详细的错误描述
- 支持的格式提示
- 调试友好的信息

## 测试覆盖

### 测试文件
- `tests/test_point_utils_multi_grasp.py` - 完整的功能测试

### 测试内容
- 向后兼容性测试
- NumPy 和 PyTorch 批量变换测试
- 统一接口测试
- 网格生成测试
- 距离计算测试
- 表面采样测试
- 性能基准测试

### 测试结果
```bash
python tests/test_point_utils_multi_grasp.py
# 输出: 🎉 所有测试通过！多抓取点云处理功能正常
```

## 使用指南

### 1. 基础使用
```python
from utils.point_utils import transform_points_multi_grasp

# 自动检测输入类型和维度
result = transform_points_multi_grasp(transforms, points)
```

### 2. 性能优化建议
- 优先使用 PyTorch 后端以获得 GPU 加速
- 批量处理多个变换以提高效率
- 确保张量在同一设备上以避免数据传输

### 3. 内存管理
- 大批量数据时注意 GPU 内存限制
- 使用 `.contiguous()` 确保张量连续性
- 及时释放不需要的中间结果

## 集成示例

### 与手部姿态处理集成
```python
# 多抓取手部姿态变换
se3_matrices = get_se3_from_hand_poses(hand_poses)  # [B, num_grasps, 4, 4]
scene_points = load_scene_pointcloud()             # [B, N, 3]

# 应用变换
transformed_scenes = apply_se3_to_points(se3_matrices, scene_points)
# 输出: [B, num_grasps, N, 3] - 每个抓取对应的变换场景
```

## 总结

通过这次重构，`utils/point_utils.py` 现在能够：

1. ✅ **完全支持多抓取格式**的点云处理
2. ✅ **保持向后兼容性**，不影响现有代码
3. ✅ **提供高性能批量处理**，GPU 加速支持
4. ✅ **统一的接口设计**，易于使用和维护
5. ✅ **完整的测试覆盖**，确保功能正确性
6. ✅ **详细的文档和示例**，便于开发者使用

这为多抓取架构的其他组件提供了强大的点云处理基础设施。
