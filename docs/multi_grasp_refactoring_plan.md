# SceneLeapPlus架构重构实施计划

## 重构目标

将SceneLeapPlus从单抓取点学习重构为并行多抓取分布学习，实现以下核心变化：

**数据格式变化**：
- `hand_model_pose`: `[B, pose_dim]` → `[B, num_grasps, pose_dim]`
- `se3`: `[B, 4, 4]` → `[B, num_grasps, 4, 4]`
- `scene_pc`: 保持 `[B, max_points, 6]` 不变
- 文本条件: 保持不变

**核心理念**：
- 从学习孤立抓取点转向学习完整抓取姿态分布
- 实现并行加噪去噪和One-shot Parallel Decoding
- 提升训练效率和建模能力

## 实施计划概览

重构任务按数据流顺序分为5个阶段，共21个子任务：

1. **阶段1: 数据预处理重构** (4个子任务)
2. **阶段2: 条件编码适配** (4个子任务)  
3. **阶段3: 模型架构重构** (7个子任务)
4. **阶段4: 损失计算更新** (3个子任务)
5. **阶段5: 配置和验证** (3个子任务)

---

## 阶段1: 数据预处理重构

### 任务1.1: 手部姿态处理函数重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `utils/hand_helper.py` 中的手部姿态处理函数，支持多抓取数据格式

**涉及文件**:
- `utils/hand_helper.py`

**具体修改**:

1. **修改 `_process_batch_pose_logic` 函数** (行460-505):
   ```python
   def _process_batch_pose_logic(se3, hand_model_pose_input, rot_type, mode):
       # 输入维度变化:
       # se3: [B, 4, 4] → [B, num_grasps, 4, 4]  
       # hand_model_pose_input: [B, 23] → [B, num_grasps, 23]
       
       B, num_grasps = hand_model_pose_input.shape[:2]
       
       # 重塑为批次处理: [B*num_grasps, ...]
       se3_flat = se3.view(B * num_grasps, 4, 4)
       pose_flat = hand_model_pose_input.view(B * num_grasps, -1)
       
       # 现有处理逻辑保持不变
       rotation_matrix = se3_flat[:, :3, :3]
       translation = se3_flat[:, :3, 3]
       joint_angles = pose_flat[:, 7:23]
       
       # 旋转表示转换
       if rot_type == 'r6d':
           rotation_6d = transforms.matrix_to_rotation_6d(rotation_matrix)
           processed_pose = torch.cat([translation, rotation_6d, joint_angles], dim=-1)
       # ... 其他rot_type处理
       
       # 重塑回多抓取格式: [B, num_grasps, pose_dim]
       pose_dim = processed_pose.shape[-1]
       processed_pose = processed_pose.view(B, num_grasps, pose_dim)
       
       return processed_pose
   ```

2. **修改 `norm_hand_pose_robust` 函数** (行200-250):
   ```python
   def norm_hand_pose_robust(hand_pose, rot_type, mode):
       # 输入: [B, num_grasps, pose_dim]
       original_shape = hand_pose.shape
       B, num_grasps, pose_dim = original_shape
       
       # 重塑为批次处理
       hand_pose_flat = hand_pose.view(B * num_grasps, pose_dim)
       
       # 现有归一化逻辑保持不变
       normalized_flat = apply_normalization(hand_pose_flat, rot_type, mode)
       
       # 重塑回原格式
       return normalized_flat.view(original_shape)
   ```

**验证方法**:
```python
# 测试脚本
def test_multi_grasp_pose_processing():
    B, num_grasps, pose_dim = 4, 8, 23
    se3 = torch.randn(B, num_grasps, 4, 4)
    hand_pose = torch.randn(B, num_grasps, pose_dim)
    
    processed = _process_batch_pose_logic(se3, hand_pose, 'r6d', 'camera_centric')
    assert processed.shape == (B, num_grasps, 25)  # r6d: 25维
```

### 任务1.2: process_hand_pose主函数适配
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改主处理函数以支持新的数据格式

**涉及文件**:
- `utils/hand_helper.py` (行590-633)

**具体修改**:
```python
def process_hand_pose(data, rot_type, mode):
    if 'se3' not in data or 'hand_model_pose' not in data:
        return data
    
    se3 = data['se3']  # [B, num_grasps, 4, 4]
    hand_model_pose_input = data['hand_model_pose']  # [B, num_grasps, 23]
    
    # 调用重构后的处理函数
    norm_pose, processed_hand_pose = _process_batch_pose(se3, hand_model_pose_input, rot_type, mode)
    
    data['norm_pose'] = norm_pose  # [B, num_grasps, pose_dim]
    data['hand_model_pose'] = processed_hand_pose  # [B, num_grasps, pose_dim]
    
    return data
```

### 任务1.3: 反归一化函数重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `denorm_hand_pose_robust` 函数支持多抓取格式

**涉及文件**:
- `utils/hand_helper.py`

**具体修改**:
```python
def denorm_hand_pose_robust(norm_pose, rot_type, mode):
    # 输入: [B, num_grasps, pose_dim] 或 [B, pose_dim]
    if norm_pose.dim() == 3:
        # 多抓取格式
        B, num_grasps, pose_dim = norm_pose.shape
        norm_pose_flat = norm_pose.view(B * num_grasps, pose_dim)
        denorm_flat = apply_denormalization(norm_pose_flat, rot_type, mode)
        return denorm_flat.view(B, num_grasps, pose_dim)
    else:
        # 单抓取格式（向后兼容）
        return apply_denormalization(norm_pose, rot_type, mode)
```

### 任务1.4: 测试函数适配
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `process_hand_pose_test` 函数支持验证/测试阶段的多抓取数据

**涉及文件**:
- `utils/hand_helper.py` (行541-584)

**具体修改**:
```python
def process_hand_pose_test(data, rot_type, mode):
    # ForMatch数据集返回的是多抓取格式
    # hand_model_pose: [B, num_grasps, 23]
    # se3: [B, num_grasps, 4, 4]
    
    if isinstance(data.get('hand_model_pose'), list):
        # 处理列表格式的多抓取数据
        processed_data = []
        for i, (poses, se3s) in enumerate(zip(data['hand_model_pose'], data['se3'])):
            # poses: [num_grasps, 23], se3s: [num_grasps, 4, 4]
            item_data = {'hand_model_pose': poses, 'se3': se3s}
            processed_item = process_hand_pose(item_data, rot_type, mode)
            processed_data.append(processed_item)
        
        # 重新组织数据
        data['norm_pose'] = [item['norm_pose'] for item in processed_data]
        data['hand_model_pose'] = [item['hand_model_pose'] for item in processed_data]
    else:
        # 标准张量格式
        data = process_hand_pose(data, rot_type, mode)
    
    return data
```

---

## 阶段2: 条件编码适配

### 任务2.1: 抓取编码器重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `GraspNet` 编码器支持多抓取输入

**涉及文件**:
- `models/utils/diffusion_utils.py`

**具体修改**:
```python
class GraspNet(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 支持多抓取的编码网络
        self.grasp_encoder = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Linear(512, output_dim)
        )
        
        # 抓取间交互的自注意力层
        self.self_attention = nn.MultiheadAttention(
            embed_dim=output_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
    def forward(self, x):
        # 输入: [B, num_grasps, input_dim] 或 [B, input_dim]
        if x.dim() == 2:
            # 单抓取格式（向后兼容）
            return self.grasp_encoder(x)  # [B, output_dim]
        
        elif x.dim() == 3:
            # 多抓取格式
            B, num_grasps, input_dim = x.shape
            
            # 编码每个抓取
            x_flat = x.view(B * num_grasps, input_dim)
            encoded_flat = self.grasp_encoder(x_flat)  # [B*num_grasps, output_dim]
            encoded = encoded_flat.view(B, num_grasps, self.output_dim)
            
            # 抓取间自注意力交互
            attended, _ = self.self_attention(encoded, encoded, encoded)
            
            return attended  # [B, num_grasps, output_dim]

### 任务2.2: 交叉注意力融合更新
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `CrossAttentionFusion` 支持多抓取查询

**涉及文件**:
- `models/utils/diffusion_utils.py`

**具体修改**:
```python
class CrossAttentionFusion(nn.Module):
    def forward(self, grasp_text_embedding, scene_features):
        # 输入变化:
        # grasp_text_embedding: [B, 512] → [B, num_grasps, 512]
        # scene_features: [B, 128, 512] (保持不变)

        if grasp_text_embedding.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._single_grasp_attention(grasp_text_embedding, scene_features)

        elif grasp_text_embedding.dim() == 3:
            # 多抓取格式
            B, num_grasps, embed_dim = grasp_text_embedding.shape

            # 准备Query, Key, Value
            Q = self.query_proj(grasp_text_embedding)  # [B, num_grasps, 512]
            K = self.key_proj(scene_features)          # [B, 128, 512]
            V = self.value_proj(scene_features)        # [B, 128, 512]

            # 批量多头注意力: 每个抓取独立与场景交互
            attended_list = []
            for i in range(num_grasps):
                Q_i = Q[:, i:i+1, :]  # [B, 1, 512]
                attention_weights = torch.softmax(
                    torch.bmm(Q_i, K.transpose(1, 2)) / math.sqrt(embed_dim), dim=-1
                )  # [B, 1, 128]
                attended_i = torch.bmm(attention_weights, V)  # [B, 1, 512]
                attended_list.append(attended_i)

            attended_features = torch.cat(attended_list, dim=1)  # [B, num_grasps, 512]
            return attended_features
```

### 任务2.3: UNet条件处理更新
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `UNetModel.condition` 方法适配多抓取数据

**涉及文件**:
- `models/decoder/unet.py` (行194-261)

**具体修改**:
```python
def condition(self, data: Dict) -> Dict:
    # 场景特征提取保持不变
    pos = data['scene_pc'].to(torch.float32)
    _, scene_feat = self.scene_model(pos)
    scene_feat = scene_feat.permute(0, 2, 1).contiguous()  # [B, N, C]

    condition_dict = {
        "scene_cond": scene_feat,
        "text_cond": None,
        "neg_pred": None,
        "neg_text_features": None,
        "text_mask": None,
    }

    # 文本条件处理保持不变（每个场景一个文本条件）
    if self.use_text_condition and 'positive_prompt' in data:
        # ... 现有文本处理逻辑保持不变 ...
        # 输出仍然是 [B, 512] 格式，会在forward中广播到多抓取

    return condition_dict
```

### 任务2.4: 条件编码验证脚本
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 验证条件编码适配的正确性

**涉及文件**:
- `tests/test_multi_grasp_conditioning.py` (新建)

**具体内容**:
```python
def test_multi_grasp_conditioning():
    """测试多抓取条件编码"""

    B, num_grasps = 4, 8

    # 1. 测试抓取编码器
    grasp_net = GraspNet(input_dim=25, output_dim=512)
    multi_grasp_input = torch.randn(B, num_grasps, 25)
    encoded = grasp_net(multi_grasp_input)
    assert encoded.shape == (B, num_grasps, 512)

    # 2. 测试交叉注意力融合
    fusion = CrossAttentionFusion(d_model=512, n_heads=8)
    scene_features = torch.randn(B, 128, 512)
    fused = fusion(encoded, scene_features)
    assert fused.shape == (B, num_grasps, 512)

    # 3. 测试向后兼容性
    single_grasp_input = torch.randn(B, 25)
    single_encoded = grasp_net(single_grasp_input)
    assert single_encoded.shape == (B, 512)
```

---

## 阶段3: 模型架构重构

### 任务3.1: UNet前向传播重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `UNetModel.forward` 支持多抓取并行处理

**涉及文件**:
- `models/decoder/unet.py` (行120-192)

**具体修改**:
```python
def forward(self, x_t: torch.Tensor, ts: torch.Tensor, data: Dict) -> torch.Tensor:
    # 输入变化: x_t [B, pose_dim] → [B, num_grasps, pose_dim]
    scene_cond = data["scene_cond"]  # [B, N_points, 512]
    text_cond = data.get("text_cond", None)  # [B, 512]

    # 处理输入维度
    if x_t.dim() == 2:
        # 单抓取格式（向后兼容）
        return self._forward_single_grasp(x_t, ts, data)

    elif x_t.dim() == 3:
        # 多抓取格式
        B, num_grasps, pose_dim = x_t.shape

        # 抓取编码: [B, num_grasps, pose_dim] → [B, num_grasps, 512]
        grasp_embedding = self.grasp_encoder(x_t)

        # 文本条件广播
        if text_cond is not None and self.use_text_condition:
            # 广播文本条件到所有抓取: [B, 512] → [B, num_grasps, 512]
            text_cond_expanded = text_cond.unsqueeze(1).expand(-1, num_grasps, -1)
            grasp_text_embedding = grasp_embedding + text_cond_expanded
        else:
            grasp_text_embedding = grasp_embedding

        # 交叉注意力融合: [B, num_grasps, 512]
        attended_features = self.cross_attention_fusion(
            grasp_text_embedding=grasp_text_embedding,
            scene_features=scene_cond,
        )

        # 时间嵌入
        t_emb = timestep_embedding(ts, self.d_model)  # [B, 512]
        t_emb = self.time_embed(t_emb)  # [B, 1024]

        # 并行处理多个抓取
        return self._forward_multi_grasp(attended_features, t_emb, B, num_grasps)

def _forward_multi_grasp(self, attended_features, t_emb, B, num_grasps):
    # attended_features: [B, num_grasps, 512]
    # t_emb: [B, 1024]

    # 重塑为批次处理: [B*num_grasps, 512]
    h = attended_features.view(B * num_grasps, 512, 1)

    # 时间嵌入扩展: [B, 1024] → [B*num_grasps, 1024]
    t_emb_expanded = t_emb.unsqueeze(1).expand(-1, num_grasps, -1).contiguous()
    t_emb_expanded = t_emb_expanded.view(B * num_grasps, -1)

    # 增强上下文: [B*num_grasps, 1, 512]
    enhanced_context = attended_features.view(B * num_grasps, 1, 512)

    # UNet主干网络处理
    h = self.in_layers(h)  # [B*num_grasps, 512, 1]

    for i in range(self.nblocks):
        res_block_idx, transformer_idx = i * 2, i * 2 + 1
        h = self.layers[res_block_idx](h, t_emb_expanded)
        h = self.layers[transformer_idx](h, context=enhanced_context)

    # 输出层
    h = self.out_layers(h)  # [B*num_grasps, pose_dim, 1]
    h = h.squeeze(-1)  # [B*num_grasps, pose_dim]

    # 重塑回多抓取格式
    output = h.view(B, num_grasps, -1)  # [B, num_grasps, pose_dim]

    return output

### 任务3.2: 扩散过程重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改扩散模型的采样和去噪过程支持多抓取

**涉及文件**:
- `models/diffuser_lightning.py`

**具体修改**:

1. **修改 `q_sample` 方法** (行46-50):
   ```python
   def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
       # 输入: x0 [B, num_grasps, pose_dim], noise [B, num_grasps, pose_dim]
       if x0.dim() == 2:
           # 单抓取格式（向后兼容）
           B, *x_shape = x0.shape
           x_t = self.sqrt_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x0 + \
               self.sqrt_one_minus_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * noise
       elif x0.dim() == 3:
           # 多抓取格式
           B, num_grasps, pose_dim = x0.shape
           # 时间步扩展到所有抓取
           t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]

           alpha_cumprod = self.sqrt_alphas_cumprod[t_expanded]  # [B, num_grasps]
           alpha_cumprod = alpha_cumprod.unsqueeze(-1)  # [B, num_grasps, 1]

           one_minus_alpha = self.sqrt_one_minus_alphas_cumprod[t_expanded]
           one_minus_alpha = one_minus_alpha.unsqueeze(-1)  # [B, num_grasps, 1]

           x_t = alpha_cumprod * x0 + one_minus_alpha * noise

       return x_t
   ```

2. **修改 `model_predict` 方法** (行52-63):
   ```python
   def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
       if x_t.dim() == 2:
           # 单抓取格式处理
           return self._model_predict_single(x_t, t, data)
       elif x_t.dim() == 3:
           # 多抓取格式处理
           B, num_grasps, pose_dim = x_t.shape

           if self.pred_x0:
               pred_x0 = self.eps_model(x_t, t, data)  # [B, num_grasps, pose_dim]

               # 计算预测噪声
               t_expanded = t.unsqueeze(1).expand(-1, num_grasps).unsqueeze(-1)
               sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded.squeeze(-1)]
               sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded.squeeze(-1)]

               pred_noise = (sqrt_recip.unsqueeze(-1) * x_t - pred_x0) / sqrt_recipm1.unsqueeze(-1)
           else:
               pred_noise = self.eps_model(x_t, t, data)
               # 类似计算pred_x0

           return pred_noise, pred_x0
   ```

### 任务3.3: 采样循环重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改采样循环支持多抓取并行去噪

**涉及文件**:
- `models/diffuser_lightning.py` (行170-184)

**具体修改**:
```python
@torch.no_grad()
def p_sample_loop(self, data: Dict) -> torch.Tensor:
    # 根据数据格式确定初始噪声形状
    if 'norm_pose' in data:
        if isinstance(data['norm_pose'], torch.Tensor):
            if data['norm_pose'].dim() == 3:
                # 多抓取格式: [B, num_grasps, pose_dim]
                B, num_grasps, pose_dim = data['norm_pose'].shape
                x_t = torch.randn(B, num_grasps, pose_dim, device=self.device)
            else:
                # 单抓取格式: [B, pose_dim]
                x_t = torch.randn_like(data['norm_pose'], device=self.device)
        else:
            # 列表格式处理
            x_t = torch.randn(len(data['norm_pose']), data['norm_pose'][0].shape[-1], device=self.device)
    else:
        raise ValueError("norm_pose not found in data")

    # 条件编码
    condition_dict = self.eps_model.condition(data)
    data.update(condition_dict)

    # 逐步去噪
    all_x_t = [x_t]
    for t in reversed(range(0, self.timesteps)):
        x_t = self.p_sample(x_t, t, data)
        all_x_t.append(x_t)

    return torch.stack(all_x_t, dim=1)  # [B, timesteps+1, num_grasps, pose_dim] 或 [B, timesteps+1, pose_dim]
```

### 任务3.4: 训练步骤重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改训练步骤支持多抓取并行训练

**涉及文件**:
- `models/diffuser_lightning.py` (行258-312)

**具体修改**:
```python
def training_step(self, batch, batch_idx):
    # 手部姿态处理（已支持多抓取）
    batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)

    # 获取批次大小和抓取数量
    norm_pose = batch['norm_pose']
    if norm_pose.dim() == 3:
        B, num_grasps, pose_dim = norm_pose.shape
        total_samples = B * num_grasps
    else:
        B = norm_pose.shape[0]
        total_samples = B

    # 时间步采样
    ts = self._sample_timesteps(B)

    # 噪声生成和加噪
    noise = torch.randn_like(norm_pose, device=self.device)
    x_t = self.q_sample(x0=norm_pose, t=ts, noise=noise)

    # 条件编码
    condition_dict = self.eps_model.condition(batch)
    batch.update(condition_dict)

    # 模型前向传播
    output = self.eps_model(x_t, ts, batch)

    # 构建预测字典
    if self.pred_x0:
        pred_dict = {
            "pred_pose_norm": output,
            "noise": noise
        }
    else:
        # 计算pred_x0（需要适配多抓取格式）
        pred_x0 = self._compute_pred_x0_multi_grasp(x_t, ts, output)
        pred_dict = {
            "pred_noise": output,
            "pred_pose_norm": pred_x0,
            "noise": noise,
        }

    # 添加负向条件（如果存在）
    if 'neg_pred' in condition_dict and condition_dict['neg_pred'] is not None:
        pred_dict['neg_pred'] = condition_dict['neg_pred']
        pred_dict['neg_text_features'] = condition_dict['neg_text_features']

    # 损失计算
    loss_dict = self.criterion(pred_dict, batch, mode='train')
    loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

    # 日志记录（使用总样本数）
    self.log_dict(loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=False, batch_size=total_samples)
    self.log("train/lr", self.optimizers().param_groups[0]['lr'], batch_size=total_samples)

    return loss
```

### 任务3.5: 验证步骤重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改验证步骤支持多抓取评估

**涉及文件**:
- `models/diffuser_lightning.py` (行318-342)

**具体修改**:
```python
def validation_step(self, batch, batch_idx):
    # 处理多抓取测试数据
    batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

    # 采样生成
    pred_x0 = self.sample(batch)  # [B, k, timesteps+1, num_grasps, pose_dim]
    pred_x0 = pred_x0[:,0,-1]     # 取第一个采样的最后时间步: [B, num_grasps, pose_dim]

    # 构建预测字典
    if pred_x0.dim() == 3:
        # 多抓取格式
        B, num_grasps, pose_dim = pred_x0.shape
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 25:] if pose_dim > 25 else pred_x0[..., 23:],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 6:25] if pose_dim > 25 else pred_x0[..., 6:23],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
        batch_size = B * num_grasps
    else:
        # 单抓取格式（向后兼容）
        pred_dict = self._build_single_grasp_pred_dict(pred_x0)
        batch_size = self.batch_size

    # 损失计算
    loss_dict = self.criterion(pred_dict, batch, mode='val')
    loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

    self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
    self.validation_step_outputs.append({
        "loss": loss.item(),
        "loss_dict": {k: v.item() for k, v in loss_dict.items()}
    })

    return {"loss": loss, "loss_dict": loss_dict}

### 任务3.6: CFG支持重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改分类器自由引导支持多抓取

**涉及文件**:
- `models/diffuser_lightning.py` (行76-154)

**具体修改**:
```python
def _prepare_cfg_data(self, data: Dict, B: int) -> Dict:
    cfg_data = {}

    # 处理张量数据
    for key in ['scene_pc', 'norm_pose', 'scene_cond']:
        if key in data and isinstance(data[key], torch.Tensor):
            if data[key].dim() == 2:
                cfg_data[key] = data[key].repeat(3, 1)
            elif data[key].dim() == 3:
                if key == 'norm_pose':
                    # 多抓取格式: [B, num_grasps, pose_dim] → [3*B, num_grasps, pose_dim]
                    cfg_data[key] = data[key].repeat(3, 1, 1)
                else:
                    cfg_data[key] = data[key].repeat(3, 1, 1)
            elif data[key].dim() == 4:
                cfg_data[key] = data[key].repeat(3, 1, 1, 1)

    # 文本条件处理保持不变
    if 'text_cond' in data and data['text_cond'] is not None:
        text_cond = data['text_cond']
        uncond_text = torch.zeros_like(text_cond)
        pos_text = text_cond
        neg_text = data.get('neg_pred', torch.zeros_like(text_cond))
        cfg_data['text_cond'] = torch.cat([uncond_text, pos_text, neg_text], dim=0)

    return cfg_data

def p_mean_variance_cfg(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict):
    # 支持多抓取的CFG
    if x_t.dim() == 2:
        # 单抓取格式
        B, pose_dim = x_t.shape
        x_t_expanded = x_t.repeat(3, 1)
    elif x_t.dim() == 3:
        # 多抓取格式
        B, num_grasps, pose_dim = x_t.shape
        x_t_expanded = x_t.repeat(3, 1, 1)

    t_expanded = t.repeat(3)
    data_expanded = self._prepare_cfg_data(data, B)

    # 模型预测
    pred_noise_all, pred_x0_all = self.model_predict(x_t_expanded, t_expanded, data_expanded)

    # 分离无条件、正向、负向预测
    pred_noise_uncond = pred_noise_all[:B]
    pred_noise_pos = pred_noise_all[B:2*B]
    pred_noise_neg = pred_noise_all[2*B:3*B]

    pred_x0_uncond = pred_x0_all[:B]
    pred_x0_pos = pred_x0_all[B:2*B]
    pred_x0_neg = pred_x0_all[2*B:3*B]

    # CFG计算
    guidance_scale = getattr(self, 'guidance_scale', 7.5)
    negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

    if hasattr(self, 'use_negative_guidance') and self.use_negative_guidance:
        guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                      negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
        guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                    negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
    else:
        guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond)
        guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond)

    # 计算后验分布参数（需要适配多抓取维度）
    return self._compute_posterior_multi_grasp(guided_x0, x_t, t)
```

### 任务3.7: 模型架构验证脚本
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 验证模型架构重构的正确性

**涉及文件**:
- `tests/test_multi_grasp_model.py` (新建)

**具体内容**:
```python
def test_multi_grasp_model_architecture():
    """测试多抓取模型架构"""

    # 1. 测试UNet前向传播
    cfg = load_test_config()
    model = DDPMLightning(cfg.model)

    B, num_grasps, pose_dim = 4, 8, 25
    x_t = torch.randn(B, num_grasps, pose_dim)
    ts = torch.randint(0, 100, (B,))

    data = {
        'scene_pc': torch.randn(B, 10000, 6),
        'scene_cond': torch.randn(B, 128, 512),
        'text_cond': torch.randn(B, 512),
    }

    output = model.eps_model(x_t, ts, data)
    assert output.shape == (B, num_grasps, pose_dim)

    # 2. 测试扩散过程
    noise = torch.randn_like(x_t)
    x_noisy = model.q_sample(x_t, ts, noise)
    assert x_noisy.shape == x_t.shape

    pred_noise, pred_x0 = model.model_predict(x_noisy, ts, data)
    assert pred_noise.shape == x_t.shape
    assert pred_x0.shape == x_t.shape

    # 3. 测试采样
    sample_data = {
        'norm_pose': x_t,
        'scene_pc': data['scene_pc'],
    }
    samples = model.sample(sample_data, k=2)
    assert samples.shape[0] == B  # 批次维度
    assert samples.shape[1] == 2  # k个采样

    # 4. 测试向后兼容性
    single_x_t = torch.randn(B, pose_dim)
    single_output = model.eps_model(single_x_t, ts, {
        'scene_cond': data['scene_cond'],
        'text_cond': data['text_cond']
    })
    assert single_output.shape == (B, pose_dim)
```

---

## 阶段4: 损失计算更新

### 任务4.1: 扩散损失重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改扩散损失计算支持多抓取

**涉及文件**:
- `models/loss/grasp_loss_pose.py`

**具体修改**:
```python
class GraspLossPose(nn.Module):
    def forward(self, pred_dict, batch, mode='train'):
        loss_dict = {}

        # 扩散损失计算
        if self.pred_x0:
            target = batch['norm_pose']           # [B, num_grasps, pose_dim] 或 [B, pose_dim]
            prediction = pred_dict['pred_pose_norm']  # 同上
        else:
            target = pred_dict['noise']
            prediction = pred_dict['pred_noise']

        # 计算MSE损失
        if target.dim() == 3:
            # 多抓取格式: 计算所有抓取的平均损失
            B, num_grasps, pose_dim = target.shape
            mse_loss = F.mse_loss(prediction, target, reduction='none')  # [B, num_grasps, pose_dim]

            # 可选择不同的聚合策略
            if self.loss_aggregation == 'mean':
                diffusion_loss = mse_loss.mean()
            elif self.loss_aggregation == 'sum':
                diffusion_loss = mse_loss.sum() / B  # 按批次归一化
            elif self.loss_aggregation == 'weighted':
                # 根据抓取质量加权（需要额外的质量分数）
                weights = batch.get('grasp_weights', torch.ones(B, num_grasps, device=target.device))
                weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
                diffusion_loss = weighted_loss.sum() / weights.sum()
        else:
            # 单抓取格式（向后兼容）
            diffusion_loss = F.mse_loss(prediction, target)

        loss_dict['diffusion_loss'] = diffusion_loss

        # 负向提示词损失（保持不变，因为是场景级别的）
        neg_loss = 0
        if 'neg_pred' in pred_dict and pred_dict['neg_pred'] is not None:
            neg_target = batch.get('neg_text_features')
            neg_pred = pred_dict['neg_pred']
            neg_loss = self.compute_negative_loss(neg_pred, neg_target)

        loss_dict['neg_loss'] = neg_loss

        # 可选：添加抓取间一致性损失
        if target.dim() == 3 and self.use_consistency_loss:
            consistency_loss = self._compute_grasp_consistency_loss(prediction, batch)
            loss_dict['consistency_loss'] = consistency_loss

        # 总损失
        total_loss = diffusion_loss + self.neg_loss_weight * neg_loss
        if 'consistency_loss' in loss_dict:
            total_loss += self.consistency_loss_weight * loss_dict['consistency_loss']

        loss_dict['total_loss'] = total_loss

        return loss_dict

    def _compute_grasp_consistency_loss(self, predictions, batch):
        """计算抓取间一致性损失，鼓励相似场景区域的抓取保持一致"""
        # predictions: [B, num_grasps, pose_dim]
        B, num_grasps, pose_dim = predictions.shape

        # 计算抓取间的相似性矩阵
        pred_flat = predictions.view(B, num_grasps, -1)
        similarity_matrix = torch.bmm(pred_flat, pred_flat.transpose(1, 2))  # [B, num_grasps, num_grasps]

        # 计算一致性损失（可以基于空间距离、语义相似性等）
        consistency_loss = torch.var(similarity_matrix, dim=[1, 2]).mean()

        return consistency_loss

### 任务4.2: 评估指标更新
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 更新评估指标以支持多抓取评估

**涉及文件**:
- `models/loss/grasp_loss_pose.py`

**具体修改**:
```python
def forward_metric(self, pred_dict, batch):
    """计算多抓取评估指标"""

    pred_pose_norm = pred_dict['pred_pose_norm']
    target_pose_norm = batch['norm_pose']

    if pred_pose_norm.dim() == 3:
        # 多抓取格式评估
        B, num_grasps, pose_dim = pred_pose_norm.shape

        # 1. 整体准确性指标
        mse_per_grasp = F.mse_loss(pred_pose_norm, target_pose_norm, reduction='none')
        mse_per_grasp = mse_per_grasp.mean(dim=-1)  # [B, num_grasps]

        metrics = {
            'mse_mean': mse_per_grasp.mean().item(),
            'mse_std': mse_per_grasp.std().item(),
            'mse_min': mse_per_grasp.min().item(),
            'mse_max': mse_per_grasp.max().item(),
        }

        # 2. 最佳抓取指标（每个场景的最佳抓取）
        best_grasp_indices = mse_per_grasp.argmin(dim=1)  # [B]
        best_mse = mse_per_grasp.gather(1, best_grasp_indices.unsqueeze(1)).squeeze(1)
        metrics['best_grasp_mse'] = best_mse.mean().item()

        # 3. Top-K准确性
        for k in [1, 3, 5]:
            if k <= num_grasps:
                topk_values, topk_indices = torch.topk(mse_per_grasp, k, dim=1, largest=False)
                metrics[f'top{k}_mse'] = topk_values.mean().item()

        # 4. 多样性指标
        diversity_score = self._compute_diversity_score(pred_pose_norm)
        metrics['diversity_score'] = diversity_score

        # 5. 覆盖率指标（如果有ground truth多抓取）
        if hasattr(self, 'compute_coverage'):
            coverage_score = self.compute_coverage(pred_pose_norm, target_pose_norm)
            metrics['coverage_score'] = coverage_score

    else:
        # 单抓取格式（向后兼容）
        mse = F.mse_loss(pred_pose_norm, target_pose_norm)
        metrics = {'mse': mse.item()}

    return metrics, metrics  # 返回格式保持兼容

def _compute_diversity_score(self, predictions):
    """计算预测抓取的多样性分数"""
    # predictions: [B, num_grasps, pose_dim]
    B, num_grasps, pose_dim = predictions.shape

    diversity_scores = []
    for b in range(B):
        pred_b = predictions[b]  # [num_grasps, pose_dim]

        # 计算两两距离
        distances = torch.cdist(pred_b, pred_b, p=2)  # [num_grasps, num_grasps]

        # 排除对角线元素
        mask = ~torch.eye(num_grasps, dtype=torch.bool, device=distances.device)
        distances_masked = distances[mask]

        # 多样性分数为平均距离
        diversity_scores.append(distances_masked.mean())

    return torch.stack(diversity_scores).mean().item()
```

### 任务4.3: 损失计算验证脚本
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 验证损失计算更新的正确性

**涉及文件**:
- `tests/test_multi_grasp_loss.py` (新建)

**具体内容**:
```python
def test_multi_grasp_loss_computation():
    """测试多抓取损失计算"""

    # 1. 测试扩散损失
    criterion = GraspLossPose(loss_config)

    B, num_grasps, pose_dim = 4, 8, 25
    pred_dict = {
        'pred_pose_norm': torch.randn(B, num_grasps, pose_dim),
        'noise': torch.randn(B, num_grasps, pose_dim),
    }
    batch = {
        'norm_pose': torch.randn(B, num_grasps, pose_dim),
    }

    loss_dict = criterion(pred_dict, batch, mode='train')

    assert 'diffusion_loss' in loss_dict
    assert 'total_loss' in loss_dict
    assert loss_dict['diffusion_loss'].item() >= 0

    # 2. 测试评估指标
    metrics, _ = criterion.forward_metric(pred_dict, batch)

    expected_metrics = ['mse_mean', 'mse_std', 'best_grasp_mse', 'diversity_score']
    for metric in expected_metrics:
        assert metric in metrics

    # 3. 测试向后兼容性
    single_pred_dict = {
        'pred_pose_norm': torch.randn(B, pose_dim),
        'noise': torch.randn(B, pose_dim),
    }
    single_batch = {
        'norm_pose': torch.randn(B, pose_dim),
    }

    single_loss_dict = criterion(single_pred_dict, single_batch, mode='train')
    assert 'diffusion_loss' in single_loss_dict

    single_metrics, _ = criterion.forward_metric(single_pred_dict, single_batch)
    assert 'mse' in single_metrics
```

---

## 阶段5: 配置和验证

### 任务5.1: 配置文件更新
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 更新配置文件以支持多抓取训练

**涉及文件**:
- `config/model/diffuser/diffuser.yaml`
- `config/model/diffuser/decoder/unet.yaml`
- `config/model/diffuser/criterion/loss.yaml`

**具体修改**:

1. **更新扩散模型配置** (`config/model/diffuser/diffuser.yaml`):
   ```yaml
   # 多抓取训练配置
   multi_grasp:
     enabled: true
     num_grasps: 8  # 每个场景的抓取数量
     loss_aggregation: "mean"  # mean, sum, weighted
     use_consistency_loss: true
     consistency_loss_weight: 0.1

   # 现有配置保持不变
   name: GraspDiffuser
   steps: 100
   pred_x0: true
   # ...
   ```

2. **更新UNet配置** (`config/model/diffuser/decoder/unet.yaml`):
   ```yaml
   # 多抓取架构配置
   multi_grasp:
     grasp_self_attention: true  # 抓取间自注意力
     grasp_attention_heads: 8
     grasp_attention_dropout: 0.1

   # 现有配置
   d_model: 512
   nblocks: 4
   # ...
   ```

3. **更新损失配置** (`config/model/diffuser/criterion/loss.yaml`):
   ```yaml
   # 多抓取损失配置
   multi_grasp:
     loss_aggregation: "mean"
     use_consistency_loss: true
     consistency_loss_weight: 0.1
     diversity_loss_weight: 0.05

   # 评估指标配置
   evaluation:
     compute_topk: [1, 3, 5]
     compute_diversity: true
     compute_coverage: false

   # 现有配置
   loss_weights:
     diffusion_loss: 1.0
     neg_loss: 0.1
   ```

### 任务5.2: 端到端集成测试
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 创建端到端集成测试验证整个重构

**涉及文件**:
- `tests/test_multi_grasp_integration.py` (新建)

**具体内容**:
```python
def test_multi_grasp_end_to_end():
    """端到端多抓取训练测试"""

    # 1. 配置加载
    cfg = load_multi_grasp_config()
    assert cfg.model.multi_grasp.enabled == True

    # 2. 数据模块初始化
    #datamodule = SceneLeapDataModule(cfg.data)
    #datamodule.setup(stage="fit")
    
    #TODO: 虚构数据进行测试

    train_loader = datamodule.train_dataloader()
    batch = next(iter(train_loader))

    # 验证数据格式
    assert batch['hand_model_pose'].dim() == 3  # [B, num_grasps, pose_dim]
    assert batch['se3'].dim() == 4              # [B, num_grasps, 4, 4]

    # 3. 模型初始化
    model = DDPMLightning(cfg.model)

    # 4. 训练步骤测试
    loss = model.training_step(batch, 0)
    assert isinstance(loss, torch.Tensor)
    assert loss.item() >= 0

    # 5. 验证步骤测试
    val_batch = next(iter(datamodule.val_dataloader()))
    val_result = model.validation_step(val_batch, 0)
    assert 'loss' in val_result

    # 6. 采样测试
    with torch.no_grad():
        samples = model.sample(batch, k=2)
        expected_shape = (batch['norm_pose'].shape[0], 2, model.timesteps + 1)
        if batch['norm_pose'].dim() == 3:
            expected_shape += batch['norm_pose'].shape[1:]
        else:
            expected_shape += batch['norm_pose'].shape[1:]
        # 验证采样形状正确

    # 7. 推理测试
    pred_poses, target_poses = model.forward_infer(batch, k=4)
    assert pred_poses.shape[1] == 4  # k个采样

    print("✅ 端到端多抓取训练测试通过")

def test_backward_compatibility():
    """测试向后兼容性"""

    # 使用单抓取配置
    cfg = load_single_grasp_config()
    cfg.model.multi_grasp.enabled = False

    model = DDPMLightning(cfg.model)

    # 单抓取数据
    single_batch = create_single_grasp_batch()

    # 训练步骤应该正常工作
    loss = model.training_step(single_batch, 0)
    assert isinstance(loss, torch.Tensor)

    # 采样应该正常工作
    samples = model.sample(single_batch, k=2)
    assert samples.dim() == 3  # [B, k, pose_dim]

    print("✅ 向后兼容性测试通过")

### 任务5.3: 性能基准测试
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 创建性能基准测试比较单抓取vs多抓取

**涉及文件**:
- `tests/benchmark_multi_grasp.py` (新建)

**具体内容**:
```python
def benchmark_training_performance():
    """基准测试训练性能"""

    import time

    # 配置
    single_cfg = load_single_grasp_config()
    multi_cfg = load_multi_grasp_config()

    # 模型
    single_model = DDPMLightning(single_cfg.model)
    multi_model = DDPMLightning(multi_cfg.model)

    # 数据
    single_batch = create_single_grasp_batch(batch_size=32)
    multi_batch = create_multi_grasp_batch(batch_size=32, num_grasps=8)

    # 基准测试
    results = {}

    # 1. 单抓取训练时间
    start_time = time.time()
    for _ in range(10):
        loss = single_model.training_step(single_batch, 0)
        loss.backward()
    single_time = (time.time() - start_time) / 10
    results['single_grasp_time'] = single_time

    # 2. 多抓取训练时间
    start_time = time.time()
    for _ in range(10):
        loss = multi_model.training_step(multi_batch, 0)
        loss.backward()
    multi_time = (time.time() - start_time) / 10
    results['multi_grasp_time'] = multi_time

    # 3. 效率比较
    # 多抓取处理的样本数是单抓取的8倍
    effective_speedup = (single_time * 8) / multi_time
    results['effective_speedup'] = effective_speedup

    # 4. 内存使用
    results['single_memory'] = torch.cuda.max_memory_allocated() / 1024**3  # GB
    torch.cuda.reset_peak_memory_stats()

    multi_model.training_step(multi_batch, 0)
    results['multi_memory'] = torch.cuda.max_memory_allocated() / 1024**3  # GB

    # 5. 输出结果
    print("性能基准测试结果:")
    print(f"单抓取训练时间: {results['single_grasp_time']:.4f}s")
    print(f"多抓取训练时间: {results['multi_grasp_time']:.4f}s")
    print(f"有效加速比: {results['effective_speedup']:.2f}x")
    print(f"单抓取内存使用: {results['single_memory']:.2f}GB")
    print(f"多抓取内存使用: {results['multi_memory']:.2f}GB")

    return results

def benchmark_inference_quality():
    """基准测试推理质量"""

    # 加载预训练模型
    single_model = load_pretrained_single_grasp_model()
    multi_model = load_pretrained_multi_grasp_model()

    # 测试数据
    test_data = load_test_dataset()

    results = {}

    for model_name, model in [('single', single_model), ('multi', multi_model)]:
        metrics_list = []

        for batch in test_data:
            with torch.no_grad():
                if model_name == 'single':
                    # 单抓取：多次采样
                    samples = model.sample(batch, k=8)
                    # 评估最佳样本
                    metrics = evaluate_best_sample(samples, batch)
                else:
                    # 多抓取：一次采样多个
                    samples = model.sample(batch, k=1)
                    # 评估多抓取分布
                    metrics = evaluate_multi_grasp_distribution(samples, batch)

                metrics_list.append(metrics)

        # 聚合指标
        results[model_name] = aggregate_metrics(metrics_list)

    # 比较结果
    print("推理质量比较:")
    for metric in ['success_rate', 'diversity_score', 'coverage_score']:
        single_val = results['single'].get(metric, 0)
        multi_val = results['multi'].get(metric, 0)
        improvement = (multi_val - single_val) / single_val * 100
        print(f"{metric}: 单抓取={single_val:.3f}, 多抓取={multi_val:.3f}, 提升={improvement:.1f}%")

    return results
```

---

## 实施进度跟踪

### 完成状态统计
- **阶段1 (数据预处理)**: 0/4 完成
- **阶段2 (条件编码)**: 0/4 完成
- **阶段3 (模型架构)**: 0/7 完成
- **阶段4 (损失计算)**: 0/3 完成
- **阶段5 (配置验证)**: 0/3 完成

**总进度**: 0/21 (0%) 完成

### 关键里程碑
- [ ] 阶段1完成：数据预处理支持多抓取格式
- [ ] 阶段2完成：条件编码适配多抓取输入
- [ ] 阶段3完成：模型架构支持并行多抓取处理
- [ ] 阶段4完成：损失计算和评估指标更新
- [ ] 阶段5完成：端到端集成测试通过

### 风险评估
1. **高风险**: 模型架构重构（任务3.1-3.6）- 涉及核心扩散过程
2. **中风险**: 数据预处理重构（任务1.1-1.6）- 影响数据流
3. **低风险**: 配置更新和验证（任务5.1-5.3）- 主要是配置和测试

### 建议实施顺序
1. 先完成数据预处理重构（阶段1），确保数据格式正确
2. 再进行条件编码适配（阶段2），保证特征提取正确
3. 然后重构模型架构（阶段3），这是最核心的部分
4. 接着更新损失计算（阶段4），确保训练目标正确
5. 最后进行配置和验证（阶段5），验证整体系统

### 预期收益
1. **训练效率提升**: 并行处理多个抓取，减少训练时间
2. **建模能力增强**: 学习完整抓取分布而非孤立点
3. **推理质量改善**: One-shot生成多个候选抓取
4. **系统鲁棒性**: 多样性约束提高泛化能力

---

## 总结

这个详细的实施计划提供了从单抓取点学习到并行多抓取分布学习的完整重构路径。每个子任务都有明确的目标、具体的代码修改指导和验证方法，确保重构过程的可控性和正确性。

**核心技术创新**：
- 并行加噪去噪处理
- One-shot Parallel Decoding
- 抓取间自注意力交互
- 多抓取分布级损失计算
- 一致性和多样性约束

**实施保障**：
- 向后兼容性设计
- 分阶段渐进式重构
- 充分的测试验证
- 性能基准对比

通过这个系统性的重构，SceneLeapPlus将实现从学习孤立抓取点到学习完整抓取姿态分布的重大升级，显著提升训练效率和建模能力。
```
```
```
```
```
