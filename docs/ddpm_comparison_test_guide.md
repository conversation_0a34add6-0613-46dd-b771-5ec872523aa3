# DDPM Lightning 模型对比测试指南

## 概述

本测试脚本用于验证重构后的 DDPM Lightning 模型与原始版本的功能一致性。通过全面的对比测试，确保重构版本可以安全地替换原始版本。

## 测试目标

- **重构版本**: `models/diffuser_lightning.py` 中的 DDPMLightning 模型
- **原始版本**: `models/diffuser_lightning_copy.py` 中的 DDPMLightning 模型

## 测试内容

### 1. 模型初始化对比
- 参数数量一致性
- 可训练参数数量一致性
- 模型结构对比

### 2. 前向传播一致性
- 输出形状对比
- 数值结果对比
- 容差范围内的差异检查

### 3. 训练步骤一致性
- 损失计算对比
- 梯度计算对比
- 训练数据流对比

### 4. 采样过程一致性
- 不同采样配置下的结果对比
- CFG（分类器自由引导）一致性
- 多次采样的稳定性

### 5. 梯度计算一致性
- 反向传播梯度对比
- 梯度匹配率统计
- 梯度数值差异分析

### 6. 中间层输出一致性
- 扩散过程中间步骤对比
- 噪声预测一致性
- 干净数据预测一致性

## 快速开始

### 1. 运行快速测试

```bash
# 基础快速测试
python tests/run_ddpm_comparison.py --mode quick

# 指定设备和容差
python tests/run_ddpm_comparison.py --mode quick --device cuda --tolerance 1e-4
```

### 2. 运行完整测试

```bash
# 完整测试（更严格的容差）
python tests/run_ddpm_comparison.py --mode full

# 使用自定义配置
python tests/test_ddpm_lightning_comparison.py --config tests/test_config.yaml
```

### 3. 运行示例

```bash
# 查看测试示例
python examples/example_ddpm_comparison.py
```

## 配置选项

### 命令行参数

- `--mode`: 测试模式 (`quick` 或 `full`)
- `--tolerance`: 数值比较容差 (默认: 1e-5)
- `--device`: 计算设备 (`auto`, `cpu`, `cuda`)
- `--config`: 配置文件路径
- `--log-file`: 日志文件路径

### 配置文件

测试配置文件 `tests/test_config.yaml` 包含：

```yaml
# 模型配置
decoder:
  type: unet
  model_channels: 64  # 小模型用于快速测试
  
# 扩散配置
steps: 50  # 减少步数加快测试

# 测试配置
test:
  tolerance: 1e-5
  batch_sizes: [1, 2, 4]
  num_grasps_list: [1, 2, 4, 8]
```

## 测试结果解读

### 成功输出示例

```
✅ 模型初始化测试通过
✅ 前向传播一致性测试通过
✅ 训练步骤一致性测试通过
✅ 采样过程一致性测试通过
✅ 梯度计算一致性测试通过
✅ 中间层输出一致性测试通过

🎉 所有测试通过！重构版本与原始版本功能一致
✅ 可以安全地使用重构版本替换原始版本
```

### 失败输出示例

```
❌ 前向传播一致性测试失败
  最大数值差异: 0.001234 (超过容差 1e-5)
  
⚠️ 部分测试失败，需要进一步调试
❌ 建议在修复问题后再进行替换
```

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 使用 CPU 或减小批次大小
   python tests/run_ddpm_comparison.py --device cpu
   ```

2. **数值差异过大**
   ```bash
   # 放宽容差
   python tests/run_ddpm_comparison.py --tolerance 1e-3
   ```

3. **模型加载失败**
   - 检查依赖模块是否正确导入
   - 确认配置文件格式正确

### 调试技巧

1. **查看详细日志**
   ```bash
   # 日志会保存到文件
   tail -f ddpm_comparison_test.log
   ```

2. **单独运行测试**
   ```python
   from tests.test_ddpm_lightning_comparison import DDPMComparisonTester
   
   tester = DDPMComparisonTester()
   tester.initialize_models()
   result = tester.test_forward_pass_consistency()
   ```

3. **检查中间结果**
   ```python
   # 访问详细测试结果
   print(tester.test_results)
   ```

## 性能基准

### 预期性能

- **快速测试**: ~2-5 分钟
- **完整测试**: ~10-20 分钟
- **内存使用**: ~2-4GB GPU 内存

### 优化建议

1. 使用 GPU 加速测试
2. 减小模型大小用于测试
3. 并行运行多个测试

## 扩展测试

### 添加自定义测试

```python
class CustomTester(DDPMComparisonTester):
    def test_custom_functionality(self):
        # 自定义测试逻辑
        pass
    
    def run_all_tests(self):
        # 添加自定义测试到测试套件
        results = super().run_all_tests()
        results['custom'] = self.test_custom_functionality()
        return results
```

### 批量测试

```bash
# 测试多种配置
for config in config1.yaml config2.yaml config3.yaml; do
    python tests/test_ddpm_lightning_comparison.py --config $config
done
```

## 最佳实践

1. **在重构后立即运行测试**
2. **使用版本控制跟踪测试结果**
3. **定期运行回归测试**
4. **记录和分析失败案例**
5. **保持测试配置的一致性**

## 联系支持

如果遇到问题或需要帮助：

1. 检查日志文件中的详细错误信息
2. 确认环境配置正确
3. 尝试使用不同的测试参数
4. 查看示例代码了解正确用法
