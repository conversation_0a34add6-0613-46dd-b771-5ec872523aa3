# 多抓取架构补充重构计划

## 概述

将SceneLeapPlus从单抓取点学习重构为并行多抓取分布学习，实现以下核心变化：

**数据格式变化**：
- `hand_model_pose`: `[B, pose_dim]` → `[B, num_grasps, pose_dim]`
- `se3`: `[B, 4, 4]` → `[B, num_grasps, 4, 4]`
- `scene_pc`: 保持 `[B, max_points, 6]` 不变
- 文本条件: 保持不变

**核心理念**：
- 从学习孤立抓取点转向学习完整抓取姿态分布
- 实现并行加噪去噪和One-shot Parallel Decoding
- 提升训练效率和建模能力

本计划针对多抓取架构升级中需要额外适配的核心组件，将单抓取格式 `[B, pose_dim]` 升级为多抓取格式 `[B, num_grasps, pose_dim]`。

**重构范围**：8个关键文件，18个具体任务
**执行原则**：保持向后兼容，支持单抓取和多抓取两种格式

---

## 任务分组

### 🔴 **阶段A: 核心组件适配** (6个任务)
解决阻塞性问题，必须优先完成

### 🟡 **阶段B: 功能组件重构** (8个任务)
完善功能完整性

### 🟢 **阶段C: 辅助组件优化** (4个任务)
系统优化和完善

---

## 🔴 阶段A: 核心组件适配

### 任务A.1: 手部模型核心接口重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `HandModel.__call__` 方法支持多抓取输入格式

**涉及文件**:
- `utils/hand_model.py` (行1346-1512)

**重构分析**:
经过深入分析，`HandModel.__call__` 方法需要进行全面重构，不仅仅是简单的维度处理。该方法涉及以下核心组件：
1. 手部参数设置 (`set_parameters`)
2. 表面点获取 (`get_surface_points`)
3. 穿透关键点获取 (`get_penetration_keypoints`)
4. 接触候选点获取 (`get_contact_candidates`)
5. 场景穿透计算 (`_compute_object_penetration`)
6. 网格顶点和面处理
7. 指尖关键点计算

**具体子任务**:

#### 子任务A.1.1: 重构主调用接口
**修改位置**: 行1346-1376
**修改内容**:
- 添加多抓取格式检测逻辑
- 实现格式分发机制
- 更新文档字符串支持多抓取描述

#### 子任务A.1.2: 实现多抓取处理核心逻辑
**修改位置**: 新增方法 `_call_multi_grasp`
**修改内容**:
- 实现 `[B, num_grasps, pose_dim]` 到 `[B*num_grasps, pose_dim]` 的重塑
- 处理场景点云的维度扩展和广播
- 调用现有单抓取逻辑进行批量处理
- 实现输出结果的维度重塑回多抓取格式

#### 子任务A.1.3: 重构场景点云处理逻辑
**修改位置**: 行1386-1418 (contact_candidates处理)
**修改内容**:
- 支持场景点云从 `[B, N, 4]` 扩展到 `[B*num_grasps, N, 4]`
- 处理批次大小不匹配的边界情况
- 确保KNN计算的正确性

#### 子任务A.1.4: 重构穿透和距离计算
**修改位置**: 行1419-1454 (penetration/distance处理)
**修改内容**:
- 适配 `_compute_object_penetration` 方法的多抓取调用
- 处理场景点云维度验证逻辑
- 确保错误处理的一致性

#### 子任务A.1.5: 重构网格数据处理
**修改位置**: 行1456-1486 (meshes处理)
**修改内容**:
- 处理顶点数据的多抓取维度重塑
- 确保面数据的正确索引
- 处理空网格的边界情况

#### 子任务A.1.6: 重构指尖关键点计算
**修改位置**: 行1488-1510 (fingertip_keypoints处理)
**修改内容**:
- 适配指尖变换矩阵的批量计算
- 处理全局旋转和平移的多抓取广播
- 确保输出维度的正确性

#### 子任务A.1.7: 实现输出重塑辅助方法
**修改位置**: 新增方法 `_reshape_output_to_multi_grasp`
**修改内容**:
- 实现通用的输出维度重塑逻辑
- 处理不同类型张量的维度转换
- 支持嵌套字典结构的递归处理

**验证方法**:
```python
def test_hand_model_multi_grasp_comprehensive():
    hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')

    # 测试数据准备
    B, num_grasps, pose_dim = 2, 4, 25
    hand_pose_multi = torch.randn(B, num_grasps, pose_dim)
    scene_pc = torch.randn(B, 1000, 6)

    # 测试所有功能组合
    result_multi = hand_model(
        hand_pose_multi,
        scene_pc=scene_pc,
        with_surface_points=True,
        with_contact_candidates=True,
        with_penetration_keypoints=True,
        with_penetration=True,
        with_distance=True,
        with_meshes=True,
        with_fingertip_keypoints=True
    )

    # 验证所有输出的维度正确性
    assert result_multi['surface_points'].shape[:2] == (B, num_grasps)
    assert result_multi['contact_candidates_dis'].shape == (B, num_grasps, 1000)
    assert result_multi['penetration_keypoints'].shape[:2] == (B, num_grasps)
    assert result_multi['penetration'].shape == (B, num_grasps, 1000)
    assert result_multi['distance'].shape == (B, num_grasps, 1000)
    assert result_multi['vertices'].shape[:2] == (B, num_grasps)
    assert result_multi['fingertip_keypoints'].shape[:2] == (B, num_grasps)

    # 测试向后兼容性
    hand_pose_single = torch.randn(B, pose_dim)
    result_single = hand_model(hand_pose_single, scene_pc=scene_pc[:, :, :4],
                              with_surface_points=True, with_penetration=True)
    assert result_single['surface_points'].shape[0] == B
    assert len(result_single['surface_points'].shape) == 3  # [B, N_points, 3]

    # 测试边界情况
    # 1. 空场景点云
    result_empty = hand_model(hand_pose_multi, scene_pc=None, with_surface_points=True)
    assert 'penetration' not in result_empty

    # 2. 单个抓取的多抓取格式
    single_grasp_multi_format = hand_pose_multi[:, :1, :]  # [B, 1, pose_dim]
    result_single_multi = hand_model(single_grasp_multi_format, with_surface_points=True)
    assert result_single_multi['surface_points'].shape[:2] == (B, 1)
```

### 任务A.2: 手部模型参数设置重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `set_parameters` 方法支持多抓取格式

**涉及文件**:
- `utils/hand_model.py` (行528-670)

**重构分析**:
`set_parameters` 方法是手部模型的核心参数设置方法，涉及复杂的几何变换和运动学计算。该方法需要处理：
1. 手部姿态张量的维度验证和格式检测
2. 旋转参数的类型识别和转换 (r6d, quat, axis, euler)
3. 全局平移和旋转的设置
4. 关节角度的前向运动学计算
5. 接触点的变换和世界坐标转换
6. 批次大小的动态管理

**具体子任务**:

#### 子任务A.2.1: 重构主参数设置接口
**修改位置**: 行528-546
**修改内容**:
- 添加多抓取格式检测 (`hand_pose.dim() == 3`)
- 实现格式分发到专门的处理方法
- 保持向后兼容性

#### 子任务A.2.2: 重构维度验证逻辑
**修改位置**: 行547-567
**修改内容**:
- 支持3D张量的维度验证: `[B, num_grasps, pose_dim]`
- 更新错误消息以包含多抓取格式信息
- 确保旋转类型检测的正确性

#### 子任务A.2.3: 实现多抓取状态管理
**修改位置**: 新增属性和方法
**修改内容**:
- 添加 `_is_multi_grasp` 标志位
- 添加 `_multi_grasp_shape` 存储原始形状
- 添加 `_original_batch_size` 和 `_num_grasps` 属性
- 实现状态重置方法

#### 子任务A.2.4: 重构设备和梯度处理
**修改位置**: 行568-576
**修改内容**:
- 支持多抓取张量的设备转换
- 处理梯度保留的批量操作
- 确保内存效率

#### 子任务A.2.5: 重构姿态分解逻辑
**修改位置**: 行577-588
**修改内容**:
- 支持多抓取格式的平移提取: `hand_pose[:, :, :3]` → `hand_pose_flat[:, :3]`
- 支持多抓取格式的关节角度提取
- 支持多抓取格式的旋转参数提取

#### 子任务A.2.6: 重构旋转矩阵计算
**修改位置**: 行590-604
**修改内容**:
- 适配批量旋转参数转换 (支持 `[B*num_grasps, rot_dim]` 输入)
- 确保所有旋转类型的正确处理
- 处理PyTorch3D变换的批量操作

#### 子任务A.2.7: 重构前向运动学计算
**修改位置**: 行605-606
**修改内容**:
- 适配 `pytorch_kinematics` 的批量关节角度输入
- 确保运动学链状态的正确更新
- 处理批次大小变化的影响

#### 子任务A.2.8: 重构接触点处理逻辑
**修改位置**: 行608-669
**修改内容**:
- 支持多抓取接触点索引的批量处理
- 适配接触点变换的批量计算
- 处理链接变换矩阵的批量操作
- 确保世界坐标转换的正确性

#### 子任务A.2.9: 实现多抓取专用方法
**修改位置**: 新增方法
**修改内容**:
```python
def _set_parameters_multi(self, hand_pose: torch.Tensor, contact_point_indices: Optional[torch.Tensor] = None):
    """处理多抓取格式的参数设置"""
    B, num_grasps, pose_dim = hand_pose.shape

    # 存储多抓取状态信息
    self._is_multi_grasp = True
    self._multi_grasp_shape = (B, num_grasps)
    self._original_batch_size = B
    self._num_grasps = num_grasps

    # 重塑为批次处理格式
    hand_pose_flat = hand_pose.view(B * num_grasps, pose_dim)

    # 处理接触点索引
    contact_indices_flat = None
    if contact_point_indices is not None:
        if contact_point_indices.dim() == 3:  # [B, num_grasps, n_contact]
            contact_indices_flat = contact_point_indices.view(B * num_grasps, -1)
        else:
            raise ValueError(f"contact_point_indices must be 3D for multi-grasp, got {contact_point_indices.dim()}D")

    # 调用重构后的单抓取逻辑
    self._set_parameters_single_internal(hand_pose_flat, contact_indices_flat)

def _set_parameters_single_internal(self, hand_pose: torch.Tensor, contact_point_indices: Optional[torch.Tensor] = None):
    """内部单抓取参数设置方法，支持被多抓取方法调用"""
    # 原有的set_parameters逻辑，但移除维度检查部分
    # ... (原有逻辑)
```

#### 子任务A.2.10: 更新batch_size属性
**修改位置**: 行1299-1302
**修改内容**:
- 适配多抓取模式下的批次大小计算
- 返回 `B * num_grasps` 而不是原始的 `B`
- 添加获取原始批次大小的方法

**验证方法**:
```python
def test_set_parameters_multi_grasp():
    hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')

    # 测试多抓取参数设置
    B, num_grasps, pose_dim = 2, 4, 25
    hand_pose_multi = torch.randn(B, num_grasps, pose_dim)

    # 测试基本参数设置
    hand_model.set_parameters(hand_pose_multi)
    assert hand_model._is_multi_grasp == True
    assert hand_model._multi_grasp_shape == (B, num_grasps)
    assert hand_model.batch_size == B * num_grasps

    # 测试不同旋转类型
    for rot_type in ['r6d', 'quat', 'axis', 'euler']:
        hand_model_rot = HandModel(hand_model_type=HandModelType.LEAP, rot_type=rot_type, device='cpu')
        if rot_type == 'r6d':
            pose_dim = 25  # 3 + 16 + 6
        elif rot_type == 'quat':
            pose_dim = 23  # 3 + 16 + 4
        else:  # axis, euler
            pose_dim = 22  # 3 + 16 + 3

        hand_pose_rot = torch.randn(B, num_grasps, pose_dim)
        hand_model_rot.set_parameters(hand_pose_rot)
        assert hand_model_rot.batch_size == B * num_grasps

    # 测试接触点处理
    n_contact = 10
    contact_indices = torch.randint(0, 100, (B, num_grasps, n_contact))
    hand_model.set_parameters(hand_pose_multi, contact_indices)
    assert hand_model.contact_points.shape == (B * num_grasps, n_contact, 3)

    # 测试向后兼容性
    hand_pose_single = torch.randn(B, 25)
    hand_model.set_parameters(hand_pose_single)
    assert hand_model._is_multi_grasp == False
    assert hand_model.batch_size == B
```

### 任务A.3: 推理接口输出处理重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `forward_infer` 方法支持多抓取输出格式

**涉及文件**:
- `models/diffuser_lightning.py` (行737-753)

**重构分析**:
`forward_infer` 方法是模型推理的核心接口，需要处理从扩散采样到最终输出的完整流程。该方法的重构涉及：
1. 多抓取采样结果的维度检测和处理
2. 姿态分量的正确提取 (translation, rotation, qpos)
3. 不同旋转表示格式的适配
4. 损失计算接口的兼容性
5. 输出格式的一致性保证

**具体子任务**:

#### 子任务A.3.1: 重构采样结果处理
**修改位置**: 行737-740
**修改内容**:
- 检测 `pred_x0` 的维度格式 (`[B, k, timesteps, pose_dim]` vs `[B, k, timesteps, num_grasps, pose_dim]`)
- 正确提取最终时间步的预测结果
- 处理多采样 (k) 和多抓取的维度组合

#### 子任务A.3.2: 实现智能格式检测
**修改位置**: 新增方法 `_detect_prediction_format`
**修改内容**:
```python
def _detect_prediction_format(self, pred_x0: torch.Tensor) -> Dict[str, Any]:
    """检测预测结果的格式并返回解析信息"""
    format_info = {
        'is_multi_grasp': False,
        'batch_size': pred_x0.shape[0],
        'num_grasps': 1,
        'pose_dim': pred_x0.shape[-1],
        'format_type': 'single_grasp'
    }

    if pred_x0.dim() == 3:
        # 可能是多抓取格式: [B, num_grasps, pose_dim]
        if pred_x0.shape[-1] in [22, 23, 25]:  # 有效的pose_dim
            format_info.update({
                'is_multi_grasp': True,
                'num_grasps': pred_x0.shape[1],
                'format_type': 'multi_grasp'
            })
    elif pred_x0.dim() == 2:
        # 单抓取格式: [B, pose_dim]
        format_info['format_type'] = 'single_grasp'
    else:
        raise ValueError(f"Unsupported prediction dimension: {pred_x0.dim()}")

    return format_info
```

#### 子任务A.3.3: 重构姿态分量提取逻辑
**修改位置**: 新增方法 `_extract_pose_components`
**修改内容**:
- 根据旋转类型 (`self.rot_type`) 动态确定分量索引
- 支持多抓取格式的批量提取
- 处理不同pose_dim的兼容性

#### 子任务A.3.4: 重构预测字典构建
**修改位置**: 行741-748
**修改内容**:
- 统一单抓取和多抓取的字典构建逻辑
- 确保所有必需字段的正确填充
- 处理噪声张量的设备和形状一致性

#### 子任务A.3.5: 适配损失计算接口
**修改位置**: 行749-750
**修改内容**:
- 确保 `criterion.infer_norm_process_dict` 能正确处理多抓取格式
- 验证输入数据的格式一致性
- 处理可能的维度不匹配问题

**完整重构代码**:
```python
def forward_infer(self, data: Dict, k=4, timestep=-1):
    """
    推理接口，支持单抓取和多抓取格式

    Args:
        data: 输入数据字典
        k: 采样数量
        timestep: 时间步选择

    Returns:
        preds_hand: 预测的手部数据
        targets_hand: 目标手部数据
    """
    # 预处理数据
    data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)

    # 执行采样
    pred_x0 = self.sample(data, k=k)  # [B, k, timesteps, ...]
    pred_x0 = pred_x0[:, 0, timestep]  # 取第一个采样的指定时间步

    # 检测预测格式
    format_info = self._detect_prediction_format(pred_x0)

    # 构建预测字典
    pred_dict = self._build_prediction_dict(pred_x0, format_info)

    # 调用损失计算接口
    try:
        preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
    except Exception as e:
        print(f"Warning: infer_norm_process_dict failed with multi-grasp format: {e}")
        # 降级处理：如果是多抓取，尝试只处理第一个抓取
        if format_info['is_multi_grasp']:
            pred_dict_single = self._convert_to_single_grasp_dict(pred_dict)
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict_single, data)
        else:
            raise

    return preds_hand, targets_hand

def _build_prediction_dict(self, pred_x0: torch.Tensor, format_info: Dict[str, Any]) -> Dict[str, torch.Tensor]:
    """构建预测字典，支持多抓取格式"""
    pose_dim = format_info['pose_dim']

    # 提取姿态分量
    components = self._extract_pose_components(pred_x0, format_info)

    pred_dict = {
        "pred_pose_norm": pred_x0,
        "translation_norm": components['translation'],
        "rotation": components['rotation'],
        "qpos_norm": components['qpos'],
        "pred_noise": torch.tensor([1.0], device=self.device),
        "noise": torch.tensor([1.0], device=self.device)
    }

    return pred_dict
```

**验证方法**:
```python
def test_forward_infer_multi_grasp():
    model = DDPMLightning(cfg.model)

    # 准备多抓取测试数据
    B, num_grasps = 2, 4
    data_multi = {
        'norm_pose': torch.randn(B, num_grasps, 25),
        'scene_pc': torch.randn(B, 1000, 6),
        'positive_prompt': ['test'] * B
    }

    # 测试多抓取推理
    preds_multi, targets_multi = model.forward_infer(data_multi, k=2)
    assert preds_multi is not None
    assert targets_multi is not None

    # 测试单抓取兼容性
    data_single = {
        'norm_pose': torch.randn(B, 25),
        'scene_pc': torch.randn(B, 1000, 6),
        'positive_prompt': ['test'] * B
    }

    preds_single, targets_single = model.forward_infer(data_single, k=2)
    assert preds_single is not None
    assert targets_single is not None
```

### 任务A.4: 推理步骤接口重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `forward_infer_step` 方法支持多抓取输出格式

**涉及文件**:
- `models/diffuser_lightning.py` (行755-774)

**重构分析**:
`forward_infer_step` 方法用于分析扩散过程中不同时间步的预测质量，是调试和分析的重要工具。该方法需要：
1. 处理多个时间步的批量预测结果
2. 支持多抓取格式的时间步分析
3. 确保每个时间步的预测字典格式一致
4. 优化内存使用和计算效率
5. 提供详细的时间步分析信息

**具体子任务**:

#### 子任务A.4.1: 重构时间步循环处理
**修改位置**: 行755-774
**修改内容**:
- 复用任务A.3中的格式检测和字典构建逻辑
- 优化循环中的重复计算
- 添加时间步处理的错误恢复机制

#### 子任务A.4.2: 实现批量时间步处理
**修改位置**: 新增方法 `_process_timesteps_batch`
**修改内容**:
- 支持批量处理多个时间步，减少循环开销
- 实现内存高效的时间步分析
- 提供可选的并行处理支持

#### 子任务A.4.3: 增强结果分析功能
**修改位置**: 新增分析方法
**修改内容**:
- 添加时间步质量评估指标
- 支持多抓取的时间步收敛分析
- 提供可视化友好的输出格式

**完整重构代码**:
```python
def forward_infer_step(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
    """
    多时间步推理分析，支持多抓取格式

    Args:
        data: 输入数据字典
        k: 采样数量
        timesteps: 要分析的时间步列表

    Returns:
        List[Dict]: 每个时间步的预测结果列表
    """
    # 预处理数据
    data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)

    # 执行采样
    pred_x0 = self.sample(data, k=k)  # [B, k, timesteps+1, ...]

    # 检测第一个时间步的格式（假设所有时间步格式一致）
    first_timestep_pred = pred_x0[:, 0, timesteps[0] if timesteps[0] >= 0 else -1]
    format_info = self._detect_prediction_format(first_timestep_pred)

    results = []
    failed_timesteps = []

    for timestep in timesteps:
        try:
            # 提取当前时间步的预测
            pred_x0_t = pred_x0[:, 0, timestep]  # 取第一个采样

            # 构建预测字典
            pred_dict = self._build_prediction_dict(pred_x0_t, format_info)

            # 添加时间步信息
            pred_dict['timestep'] = timestep

            # 处理预测
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)

            # 构建结果字典
            result = {
                'timestep': timestep,
                'preds_hand': preds_hand,
                'targets_hand': targets_hand,
                'pred_dict': pred_dict,
                'format_info': format_info
            }

            results.append(result)

        except Exception as e:
            print(f"Warning: Failed to process timestep {timestep}: {e}")
            failed_timesteps.append(timestep)

            # 添加空结果以保持索引一致性
            results.append({
                'timestep': timestep,
                'preds_hand': None,
                'targets_hand': None,
                'error': str(e)
            })

    # 记录失败的时间步
    if failed_timesteps:
        print(f"Failed timesteps: {failed_timesteps}")

    return results

def _analyze_timestep_convergence(self, results: List[Dict]) -> Dict[str, Any]:
    """分析时间步收敛情况"""
    if not results or not any(r.get('preds_hand') is not None for r in results):
        return {'error': 'No valid results to analyze'}

    analysis = {
        'total_timesteps': len(results),
        'successful_timesteps': sum(1 for r in results if r.get('preds_hand') is not None),
        'failed_timesteps': [r['timestep'] for r in results if r.get('preds_hand') is None],
        'convergence_metrics': {}
    }

    # 计算收敛指标
    valid_results = [r for r in results if r.get('preds_hand') is not None]
    if len(valid_results) > 1:
        # 计算相邻时间步的变化
        changes = []
        for i in range(1, len(valid_results)):
            prev_pred = valid_results[i-1]['pred_dict']['pred_pose_norm']
            curr_pred = valid_results[i]['pred_dict']['pred_pose_norm']
            change = torch.norm(curr_pred - prev_pred, dim=-1).mean().item()
            changes.append(change)

        analysis['convergence_metrics'] = {
            'mean_change': np.mean(changes) if changes else 0,
            'std_change': np.std(changes) if changes else 0,
            'max_change': np.max(changes) if changes else 0,
            'min_change': np.min(changes) if changes else 0
        }

    return analysis

def forward_infer_step_with_analysis(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
    """带分析功能的多时间步推理"""
    results = self.forward_infer_step(data, k, timesteps)
    analysis = self._analyze_timestep_convergence(results)

    return {
        'results': results,
        'analysis': analysis,
        'summary': {
            'format': results[0].get('format_info', {}).get('format_type', 'unknown') if results else 'unknown',
            'success_rate': analysis.get('successful_timesteps', 0) / analysis.get('total_timesteps', 1),
            'convergence_score': 1.0 / (1.0 + analysis.get('convergence_metrics', {}).get('mean_change', 1.0))
        }
    }
```

**验证方法**:
```python
def test_forward_infer_step_multi_grasp():
    model = DDPMLightning(cfg.model)

    # 准备测试数据
    B, num_grasps = 2, 4
    data_multi = {
        'norm_pose': torch.randn(B, num_grasps, 25),
        'scene_pc': torch.randn(B, 1000, 6),
        'positive_prompt': ['test'] * B
    }

    # 测试基本时间步分析
    timesteps = [1, 5, 10, -1]
    results = model.forward_infer_step(data_multi, k=2, timesteps=timesteps)

    assert len(results) == len(timesteps)
    for i, result in enumerate(results):
        assert result['timestep'] == timesteps[i]
        if result.get('preds_hand') is not None:
            assert result['format_info']['is_multi_grasp'] == True

    # 测试带分析的版本
    analysis_result = model.forward_infer_step_with_analysis(data_multi, k=2, timesteps=timesteps)

    assert 'results' in analysis_result
    assert 'analysis' in analysis_result
    assert 'summary' in analysis_result
    assert analysis_result['summary']['format'] == 'multi_grasp'

    # 测试单抓取兼容性
    data_single = {
        'norm_pose': torch.randn(B, 25),
        'scene_pc': torch.randn(B, 1000, 6)
    }

    results_single = model.forward_infer_step(data_single, k=2, timesteps=timesteps)
    assert len(results_single) == len(timesteps)

    # 测试错误恢复
    invalid_timesteps = [1000, -1000, 50]  # 一些可能无效的时间步
    results_with_errors = model.forward_infer_step(data_multi, k=2, timesteps=invalid_timesteps)
    assert len(results_with_errors) == len(invalid_timesteps)
```

### 任务A.5: 评估工具碰撞检测重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `cal_pen` 函数支持多抓取批量处理

**涉及文件**:
- `utils/evaluate_utils.py` (行172-186)

**重构分析**:
`cal_pen` 函数是评估抓取质量的核心工具，用于计算手部与物体的穿透深度。该函数的重构需要考虑：
1. 多抓取格式的批量碰撞检测
2. 表面点采样的优化和复用
3. 内存效率和计算性能
4. 不同物体几何形状的适配
5. 错误处理和边界情况

**具体子任务**:

#### 子任务A.5.1: 重构主接口函数
**修改位置**: 行172-186
**修改内容**:
- 添加多抓取格式检测
- 实现格式分发机制
- 更新返回类型注解

#### 子任务A.5.2: 实现高效的多抓取碰撞检测
**修改位置**: 新增方法 `_cal_pen_multi_optimized`
**修改内容**:
- 优化表面点采样，避免重复计算
- 实现批量手部模型设置
- 使用向量化操作提升性能

#### 子任务A.5.3: 重构表面点采样逻辑
**修改位置**: 新增方法 `_sample_surface_points_cached`
**修改内容**:
- 实现表面点采样的缓存机制
- 支持不同采样策略
- 处理退化几何的边界情况

#### 子任务A.5.4: 实现批量穿透计算
**修改位置**: 新增方法 `_compute_penetration_batch`
**修改内容**:
- 批量处理多个抓取的穿透计算
- 优化内存使用模式
- 支持并行计算

**完整重构代码**:
```python
def cal_pen(
    cfg: Dict,
    hand_model,
    vertices: Tensor,  # dtype=torch.float32
    faces: Tensor,     # dtype=torch.long
    scale: float,
    hand_pose: Tensor,
    num_samples: int = 2000,
    skip_links: list = [],
    use_cache: bool = True,
    batch_size: Optional[int] = None
) -> Union[float, torch.Tensor]:
    """
    Calculate penetration depth between hand and object.
    Now supports both single and multi-grasp formats with optimizations.

    Args:
        cfg: Configuration dictionary
        hand_model: Hand model instance
        vertices: Object vertices [N_verts, 3]
        faces: Object faces [N_faces, 3]
        scale: Object scale factor
        hand_pose: (B, pose_dim) or (B, num_grasps, pose_dim)
        num_samples: Number of surface points to sample
        skip_links: Links to skip in penetration calculation
        use_cache: Whether to cache surface points
        batch_size: Batch size for processing (None for auto)

    Returns:
        float: For single grasp, returns the penetration depth
        torch.Tensor: For multi-grasp, returns (B, num_grasps) penetration depths
    """
    # 输入验证
    if not isinstance(hand_pose, torch.Tensor):
        raise TypeError(f"hand_pose must be torch.Tensor, got {type(hand_pose)}")

    if hand_pose.dim() == 2:
        # 单抓取格式处理 (向后兼容)
        return _cal_pen_single(cfg, hand_model, vertices, faces, scale,
                              hand_pose, num_samples, skip_links)
    elif hand_pose.dim() == 3:
        # 多抓取格式处理
        return _cal_pen_multi_optimized(cfg, hand_model, vertices, faces, scale,
                                       hand_pose, num_samples, skip_links,
                                       use_cache, batch_size)
    else:
        raise ValueError(f"Unsupported hand_pose dimension: {hand_pose.dim()}")

def _cal_pen_multi_optimized(cfg, hand_model, vertices, faces, scale, hand_pose,
                           num_samples, skip_links, use_cache=True, batch_size=None):
    """优化的多抓取碰撞检测"""
    B, num_grasps, pose_dim = hand_pose.shape
    device = hand_pose.device

    # 缓存表面点采样结果
    if use_cache:
        surface_points = _sample_surface_points_cached(vertices, faces, num_samples, scale)
    else:
        surface_points = _sample_surface_points(vertices, faces, num_samples, scale)

    # 确定批处理大小
    if batch_size is None:
        batch_size = min(B * num_grasps, 32)  # 自动选择合适的批大小

    # 重塑手部姿态为批处理格式
    hand_pose_flat = hand_pose.view(B * num_grasps, pose_dim)

    # 批量计算穿透深度
    penetration_depths = _compute_penetration_batch(
        cfg, hand_model, surface_points, hand_pose_flat,
        skip_links, batch_size, device
    )

    # 重塑回多抓取格式
    penetration_depths = penetration_depths.view(B, num_grasps)

    return penetration_depths

def _sample_surface_points_cached(vertices, faces, num_samples, scale):
    """带缓存的表面点采样"""
    # 创建缓存键
    cache_key = (vertices.shape[0], faces.shape[0], num_samples, scale)

    # 检查缓存 (简化版本，实际可以使用更复杂的缓存机制)
    if hasattr(_sample_surface_points_cached, '_cache'):
        if cache_key in _sample_surface_points_cached._cache:
            return _sample_surface_points_cached._cache[cache_key]
    else:
        _sample_surface_points_cached._cache = {}

    # 计算表面点
    surface_points = _sample_surface_points(vertices, faces, num_samples, scale)

    # 缓存结果
    _sample_surface_points_cached._cache[cache_key] = surface_points

    return surface_points

def _compute_penetration_batch(cfg, hand_model, surface_points, hand_pose_flat,
                             skip_links, batch_size, device):
    """批量计算穿透深度"""
    total_grasps = hand_pose_flat.shape[0]
    penetration_depths = []

    for start_idx in range(0, total_grasps, batch_size):
        end_idx = min(start_idx + batch_size, total_grasps)
        batch_poses = hand_pose_flat[start_idx:end_idx]

        # 批量设置手部参数
        hand_model.set_parameters(batch_poses)

        # 计算当前批次的穿透深度
        batch_depths = []
        for i in range(batch_poses.shape[0]):
            # 获取单个手部的穿透关键点
            penetration_keypoints = hand_model.get_penetration_keypoints()[i:i+1]

            # 计算与表面点的最小距离
            if len(skip_links) > 0:
                # 过滤跳过的链接
                filtered_keypoints = _filter_keypoints_by_links(
                    penetration_keypoints, hand_model, skip_links
                )
            else:
                filtered_keypoints = penetration_keypoints

            # 计算穿透深度
            if filtered_keypoints.shape[1] > 0:
                distances = torch.cdist(filtered_keypoints.squeeze(0), surface_points)
                min_distance = distances.min().item()
                penetration_depth = max(0, -min_distance)  # 负距离表示穿透
            else:
                penetration_depth = 0.0

            batch_depths.append(penetration_depth)

        penetration_depths.extend(batch_depths)

    return torch.tensor(penetration_depths, device=device)

def _filter_keypoints_by_links(keypoints, hand_model, skip_links):
    """根据跳过的链接过滤关键点"""
    # 这里需要根据hand_model的具体实现来过滤关键点
    # 简化版本：假设所有关键点都保留
    return keypoints
```

**验证方法**:
```python
def test_cal_pen_multi_grasp():
    from utils.hand_model import HandModel, HandModelType

    # 初始化手部模型
    hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')

    # 创建测试物体
    vertices = torch.randn(100, 3)
    faces = torch.randint(0, 100, (50, 3))
    scale = 1.0

    # 测试多抓取碰撞检测
    B, num_grasps = 2, 4
    hand_pose_multi = torch.randn(B, num_grasps, 25)

    cfg = {'test': True}
    penetration_multi = cal_pen(cfg, hand_model, vertices, faces, scale,
                               hand_pose_multi, num_samples=100)

    assert penetration_multi.shape == (B, num_grasps)
    assert torch.all(penetration_multi >= 0)  # 穿透深度应该非负

    # 测试单抓取兼容性
    hand_pose_single = torch.randn(B, 25)
    penetration_single = cal_pen(cfg, hand_model, vertices, faces, scale,
                                hand_pose_single, num_samples=100)

    assert isinstance(penetration_single, (float, torch.Tensor))

    # 测试缓存功能
    penetration_cached = cal_pen(cfg, hand_model, vertices, faces, scale,
                                hand_pose_multi, num_samples=100, use_cache=True)

    assert torch.allclose(penetration_multi, penetration_cached, atol=1e-6)

    # 测试批处理大小
    penetration_batch = cal_pen(cfg, hand_model, vertices, faces, scale,
                               hand_pose_multi, num_samples=100, batch_size=2)

    assert torch.allclose(penetration_multi, penetration_batch, atol=1e-6)
```

### 任务A.6: 损失计算手部数据准备重构
**状态**: [ ] 未开始 / [ ] 进行中 / [ ] 已完成

**目标**: 修改 `_prepare_hand_data` 方法适配多抓取手部模型调用

**涉及文件**:
- `models/loss/grasp_loss_pose.py` (行369-390)

**重构分析**:
`_prepare_hand_data` 方法是损失计算中的关键数据准备步骤，负责调用手部模型生成用于损失计算的手部几何信息。该方法的重构需要考虑：
1. 多抓取格式的手部模型调用
2. 场景点云的正确传递和格式适配
3. 手部模型输出的维度一致性验证
4. 内存效率和计算性能优化
5. 错误处理和降级策略

**具体子任务**:

#### 子任务A.6.1: 重构数据格式检测逻辑
**修改位置**: 行369-390
**修改内容**:
- 实现更鲁棒的多抓取格式检测
- 添加维度一致性验证
- 处理混合格式的边界情况

#### 子任务A.6.2: 重构场景点云处理
**修改位置**: 新增方法 `_prepare_scene_pc_for_hand_model`
**修改内容**:
- 统一场景点云的格式处理
- 支持多抓取的场景点云广播
- 处理缺失场景点云的情况

#### 子任务A.6.3: 实现批量手部模型调用
**修改位置**: 新增方法 `_call_hand_model_batch`
**修改内容**:
- 优化多抓取的手部模型调用
- 实现内存高效的批处理
- 添加调用失败的恢复机制

#### 子任务A.6.4: 重构输出验证和后处理
**修改位置**: 新增方法 `_validate_hand_model_outputs`
**修改内容**:
- 验证手部模型输出的格式正确性
- 处理输出维度不匹配的情况
- 添加调试信息和错误报告

**完整重构代码**:
```python
def _prepare_hand_data(self, outputs, targets, hand_model_kwargs: Dict[str, bool]):
    """
    准备手部数据用于损失计算，支持多抓取格式

    Args:
        outputs: 模型输出字典
        targets: 目标数据字典
        hand_model_kwargs: 手部模型调用参数

    Returns:
        Tuple[Dict, Dict]: 更新后的outputs和targets
    """
    try:
        # 准备场景点云数据
        scene_pc_for_targets, scene_pc_for_outputs = self._prepare_scene_pc_for_hand_model(outputs, targets)

        # 获取手部姿态数据
        target_hand_pose = targets['matched']['hand_model_pose']
        output_hand_pose = outputs['matched']['hand_model_pose']

        # 验证数据格式一致性
        self._validate_pose_format_consistency(target_hand_pose, output_hand_pose)

        # 调用手部模型
        targets['hand'] = self._call_hand_model_safe(
            target_hand_pose, scene_pc_for_targets, hand_model_kwargs, 'targets'
        )
        outputs['hand'] = self._call_hand_model_safe(
            output_hand_pose, scene_pc_for_outputs, hand_model_kwargs, 'outputs'
        )

        # 验证输出格式
        self._validate_hand_model_outputs(targets['hand'], outputs['hand'], target_hand_pose)

        # 设置旋转类型
        outputs['rot_type'] = self.rot_type

        return outputs, targets

    except Exception as e:
        print(f"Error in _prepare_hand_data: {e}")
        # 降级处理：返回空的手部数据
        empty_hand_data = self._create_empty_hand_data(target_hand_pose)
        targets['hand'] = empty_hand_data
        outputs['hand'] = empty_hand_data
        outputs['rot_type'] = self.rot_type
        return outputs, targets

def _prepare_scene_pc_for_hand_model(self, outputs, targets):
    """准备场景点云数据"""
    # 获取目标场景点云
    scene_pc_for_targets = targets.get('scene_pc')
    if 'matched' in targets and 'scene_pc' in targets['matched']:
        scene_pc_for_targets = targets['matched']['scene_pc']

    # 输出使用相同的场景点云
    scene_pc_for_outputs = scene_pc_for_targets

    # 验证场景点云格式
    if scene_pc_for_targets is not None:
        if not isinstance(scene_pc_for_targets, torch.Tensor):
            print(f"Warning: scene_pc is not a tensor: {type(scene_pc_for_targets)}")
            scene_pc_for_targets = None
            scene_pc_for_outputs = None
        elif scene_pc_for_targets.dim() not in [2, 3]:
            print(f"Warning: scene_pc has invalid dimensions: {scene_pc_for_targets.dim()}")
            scene_pc_for_targets = None
            scene_pc_for_outputs = None

    return scene_pc_for_targets, scene_pc_for_outputs

def _validate_pose_format_consistency(self, target_pose, output_pose):
    """验证目标和输出姿态格式的一致性"""
    if target_pose.dim() != output_pose.dim():
        raise ValueError(f"Pose dimension mismatch: target {target_pose.dim()}D vs output {output_pose.dim()}D")

    if target_pose.shape != output_pose.shape:
        raise ValueError(f"Pose shape mismatch: target {target_pose.shape} vs output {output_pose.shape}")

    # 验证多抓取格式的合理性
    if target_pose.dim() == 3:
        B, num_grasps, pose_dim = target_pose.shape
        if num_grasps <= 0 or pose_dim <= 0:
            raise ValueError(f"Invalid multi-grasp shape: {target_pose.shape}")
        if pose_dim not in [22, 23, 25]:  # 常见的pose维度
            print(f"Warning: Unusual pose dimension: {pose_dim}")

def _call_hand_model_safe(self, hand_pose, scene_pc, hand_model_kwargs, data_type):
    """安全调用手部模型"""
    try:
        # 检测格式并调用
        if hand_pose.dim() == 3:
            # 多抓取格式
            return self._call_hand_model_multi_grasp(hand_pose, scene_pc, hand_model_kwargs)
        else:
            # 单抓取格式
            return self._call_hand_model_single_grasp(hand_pose, scene_pc, hand_model_kwargs)

    except Exception as e:
        print(f"Hand model call failed for {data_type}: {e}")
        # 返回空的手部数据
        return self._create_empty_hand_data(hand_pose)

def _call_hand_model_multi_grasp(self, hand_pose, scene_pc, hand_model_kwargs):
    """多抓取格式的手部模型调用"""
    B, num_grasps, pose_dim = hand_pose.shape

    # 检查是否需要批处理
    total_grasps = B * num_grasps
    max_batch_size = getattr(self, 'max_hand_model_batch_size', 64)

    if total_grasps <= max_batch_size:
        # 直接调用
        return self.hand_model(hand_pose, scene_pc=scene_pc, **hand_model_kwargs)
    else:
        # 分批处理
        return self._call_hand_model_in_batches(hand_pose, scene_pc, hand_model_kwargs, max_batch_size)

def _call_hand_model_single_grasp(self, hand_pose, scene_pc, hand_model_kwargs):
    """单抓取格式的手部模型调用"""
    return self.hand_model(hand_pose, scene_pc=scene_pc, **hand_model_kwargs)

def _call_hand_model_in_batches(self, hand_pose, scene_pc, hand_model_kwargs, max_batch_size):
    """分批调用手部模型"""
    B, num_grasps, pose_dim = hand_pose.shape

    # 重塑为平坦格式
    hand_pose_flat = hand_pose.view(B * num_grasps, pose_dim)

    # 处理场景点云
    if scene_pc is not None and scene_pc.dim() == 3:
        scene_pc_expanded = scene_pc.unsqueeze(1).expand(-1, num_grasps, -1, -1)
        scene_pc_flat = scene_pc_expanded.contiguous().view(B * num_grasps, -1, scene_pc.shape[-1])
    else:
        scene_pc_flat = scene_pc

    # 分批处理
    results = []
    for start_idx in range(0, B * num_grasps, max_batch_size):
        end_idx = min(start_idx + max_batch_size, B * num_grasps)

        batch_pose = hand_pose_flat[start_idx:end_idx]
        batch_scene_pc = scene_pc_flat[start_idx:end_idx] if scene_pc_flat is not None else None

        batch_result = self.hand_model(batch_pose, scene_pc=batch_scene_pc, **hand_model_kwargs)
        results.append(batch_result)

    # 合并结果
    return self._merge_hand_model_results(results, B, num_grasps)

def _validate_hand_model_outputs(self, targets_hand, outputs_hand, reference_pose):
    """验证手部模型输出"""
    if targets_hand is None or outputs_hand is None:
        print("Warning: Hand model returned None outputs")
        return

    # 验证输出类型
    if not isinstance(targets_hand, dict) or not isinstance(outputs_hand, dict):
        print("Warning: Hand model outputs are not dictionaries")
        return

    # 验证关键字段
    expected_keys = ['surface_points'] if any(k.startswith('surface') for k in targets_hand.keys()) else []

    for key in expected_keys:
        if key not in targets_hand or key not in outputs_hand:
            print(f"Warning: Missing key '{key}' in hand model outputs")
            continue

        target_tensor = targets_hand[key]
        output_tensor = outputs_hand[key]

        if target_tensor.shape != output_tensor.shape:
            print(f"Warning: Shape mismatch for '{key}': {target_tensor.shape} vs {output_tensor.shape}")

def _create_empty_hand_data(self, reference_pose):
    """创建空的手部数据"""
    device = reference_pose.device

    if reference_pose.dim() == 3:
        B, num_grasps, _ = reference_pose.shape
        return {
            'surface_points': torch.empty(B, num_grasps, 0, 3, device=device),
            'vertices': torch.empty(B, num_grasps, 0, 3, device=device),
            'faces': torch.empty(0, 3, dtype=torch.long, device=device)
        }
    else:
        B = reference_pose.shape[0]
        return {
            'surface_points': torch.empty(B, 0, 3, device=device),
            'vertices': torch.empty(B, 0, 3, device=device),
            'faces': torch.empty(0, 3, dtype=torch.long, device=device)
        }
```

**验证方法**:
```python
def test_prepare_hand_data_multi_grasp():
    from models.loss.grasp_loss_pose import GraspLossPose

    # 初始化损失计算器
    loss_fn = GraspLossPose(cfg.loss)

    # 准备测试数据
    B, num_grasps = 2, 4

    # 多抓取格式数据
    targets_multi = {
        'matched': {
            'hand_model_pose': torch.randn(B, num_grasps, 25)
        },
        'scene_pc': torch.randn(B, 1000, 6)
    }

    outputs_multi = {
        'matched': {
            'hand_model_pose': torch.randn(B, num_grasps, 25)
        }
    }

    hand_model_kwargs = {
        'with_surface_points': True,
        'with_meshes': True
    }

    # 测试多抓取数据准备
    outputs_result, targets_result = loss_fn._prepare_hand_data(
        outputs_multi, targets_multi, hand_model_kwargs
    )

    assert 'hand' in targets_result
    assert 'hand' in outputs_result
    assert 'rot_type' in outputs_result

    # 验证手部数据格式
    if targets_result['hand']['surface_points'] is not None:
        assert targets_result['hand']['surface_points'].shape[:2] == (B, num_grasps)

    # 测试单抓取兼容性
    targets_single = {
        'matched': {
            'hand_model_pose': torch.randn(B, 25)
        },
        'scene_pc': torch.randn(B, 1000, 6)
    }

    outputs_single = {
        'matched': {
            'hand_model_pose': torch.randn(B, 25)
        }
    }

    outputs_single_result, targets_single_result = loss_fn._prepare_hand_data(
        outputs_single, targets_single, hand_model_kwargs
    )

    assert 'hand' in targets_single_result
    assert 'hand' in outputs_single_result

    # 测试错误恢复
    invalid_targets = {
        'matched': {
            'hand_model_pose': torch.randn(B, num_grasps, 25)
        }
        # 缺少scene_pc
    }

    invalid_outputs = {
        'matched': {
            'hand_model_pose': torch.randn(B, num_grasps, 30)  # 错误的pose_dim
        }
    }

    # 应该不会抛出异常，而是返回空数据
    outputs_invalid, targets_invalid = loss_fn._prepare_hand_data(
        invalid_outputs, invalid_targets, hand_model_kwargs
    )

    assert 'hand' in targets_invalid
    assert 'hand' in outputs_invalid
```

---