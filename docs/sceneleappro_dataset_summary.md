# SceneLeapPro Datasets Summary

This document summarizes the data fields and their shapes returned by the `collate_fn` of the two main dataset classes in `datasets/sceneleappro_dataset.py`: `SceneLeapProDataset` and `ForMatchSceneLeapProDataset`.

---

## 1. `SceneLeapProDataset`

This dataset returns a **single grasp pose per item**. Its `collate_fn` points to the `collate_batch_data` function, which performs standard batch stacking.

Assuming a `batch_size` of `B`, `M` points in the downsampled point cloud (`M <= max_points`), `V` vertices, and `F` faces for an object mesh, the batched data is a dictionary with the following structure:

| Field Name                      | Shape / Type                                   | Description                                                        |
| ------------------------------- | ---------------------------------------------- | ------------------------------------------------------------------ |
| `obj_code`                      | `List[str]` (length `B`)                       | Unique identifier for the object instance (name + UID).            |
| `scene_pc`                      | `torch.Tensor`, `(B, M, 6)`                    | Scene point cloud with XYZ coordinates and RGB colors.             |
| `object_mask`                   | `torch.Tensor`, `(B, M)` (dtype: bool)         | Mask for the points in `scene_pc` that belong to the target object.|
| `hand_model_pose`               | `torch.Tensor`, `(B, 23)`                      | Hand model pose parameters: `[P(3), Joints(16), Q_wxyz(4)]`.        |
| `se3`                           | `torch.Tensor`, `(B, 4, 4)`                    | SE(3) transformation matrix for the hand pose.                     |
| `scene_id`                      | `List[str]` (length `B`)                       | Scene identifier.                                                  |
| `category_id_from_object_index` | `torch.Tensor`, `(B,)`                         | Category ID of the object within the scene.                        |
| `depth_view_index`              | `torch.Tensor`, `(B,)`                         | Index of the depth view.                                           |
| `obj_verts`                     | `List[torch.Tensor]` (length `B`)              | List of object mesh vertices, each of shape `(V_i, 3)`. `V_i` varies.|
| `obj_faces`                     | `List[torch.Tensor]` (length `B`)              | List of object mesh faces, each of shape `(F_i, 3)`. `F_i` varies. |
| `positive_prompt`               | `List[str]` (length `B`)                       | Positive text prompt, typically the object name.                   |
| `negative_prompts`              | `List[List[str]]` (length `B`)                 | List of negative text prompts, other object names in the scene.    |
| `grasp_npy_idx`                 | `torch.Tensor`, `(B,)`                         | Index of the grasp pose in its original `.npy` file.               |
| `error` (optional)              | `List[str]`                                    | Present if data loading fails; other fields may be placeholders.   |

---

## 2. `ForMatchSceneLeapProDataset`

This dataset returns **all collision-free grasp poses for a given object in a scene view**. Its `collate_fn` points to `collate_variable_grasps_batch`, which handles variable-length grasp data by padding them into dense tensors.

Assuming a `batch_size` of `B`, a maximum of `N_max` grasps for any object in the batch, and `M` points in the point cloud, the batched data is a dictionary with the following structure:

| Field Name                      | Shape / Type                                   | Description                                                        |
| ------------------------------- | ---------------------------------------------- | ------------------------------------------------------------------ |
| `obj_code`                      | `List[str]` (length `B`)                       | Unique identifier for the object instance.                         |
| `scene_pc`                      | `torch.Tensor`, `(B, M, 6)`                    | Scene point cloud, same as `SceneLeapProDataset`.                  |
| `object_mask`                   | `torch.Tensor`, `(B, M)` (dtype: bool)         | Mask for the target object in the point cloud.                     |
| `hand_model_pose`               | `torch.Tensor`, `(B, N_max, 23)`               | **Padded** batch of hand poses.                                    |
| `se3`                           | `torch.Tensor`, `(B, N_max, 4, 4)`             | **Padded** batch of SE(3) transformation matrices.                 |
| `grasps_count`                  | `torch.Tensor`, `(B,)`                         | The actual number of grasps for each item (before padding).        |
| `scene_id`                      | `List[str]` (length `B`)                       | Scene identifier.                                                  |
| `category_id_from_object_index` | `torch.Tensor`, `(B,)`                         | Object category ID.                                                |
| `depth_view_index`              | `torch.Tensor`, `(B,)`                         | Depth view index.                                                  |
| `obj_verts`                     | `List[torch.Tensor]` (length `B`)              | List of object vertices, structured as above.                      |
| `obj_faces`                     | `List[torch.Tensor]` (length `B`)              | List of object faces, structured as above.                         |
| `positive_prompt`               | `List[str]` (length `B`)                       | Positive text prompt.                                              |
| `negative_prompts`              | `List[List[str]]` (length `B`)                 | List of negative text prompts.                                     |
| `error` (optional)              | `List[str]`                                    | Error message if data loading fails.                               |

### Key Differences

The primary distinction lies in the `hand_model_pose` and `se3` fields:
- In `SceneLeapProDataset`, their shapes are `(B, ...)` for a single grasp per item.
- In `ForMatchSceneLeapProDataset`, their shapes are `(B, N_max, ...)` to accommodate multiple grasps per item, unified by padding. This dataset also adds the `grasps_count` field to specify the true number of grasps for each item in the batch. 