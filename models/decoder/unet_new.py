import torch
import torch.nn as nn
import logging
from typing import Dict
from einops import rearrange

from models.utils.diffusion_utils import (
    timestep_embedding,
    ResBlock,
    SpatialTransformer,
    GraspNet,
)
from models.backbone import build_backbone
from models.utils.text_encoder import TextConditionProcessor, PosNegTextEncoder

class UNetModel(nn.Module):
    """
    A U-Net-like model for predicting noise in a diffusion process for grasp synthesis.

    This model takes a noisy grasp pose, a timestep, and various conditions (scene point cloud,
    text prompts) to predict the added noise. It uses a combination of ResBlocks for time
    conditioning and Spatial Transformers for injecting spatial and semantic context.
    """

    def __init__(self, cfg) -> None:
        super().__init__()

        # --- Input Dimension Configuration ---
        if not hasattr(cfg, 'rot_type'):
            raise ValueError("'rot_type' must be specified in the config.")
        
        rot_to_dim = {'quat': 23, 'r6d': 25}
        if cfg.rot_type not in rot_to_dim:
            raise ValueError(f"Unsupported rot_type '{cfg.rot_type}'. Must be one of {list(rot_to_dim.keys())}")
        self.d_x = rot_to_dim[cfg.rot_type]

        # --- Model Architecture Configuration ---
        self.d_model = cfg.d_model
        self.nblocks = cfg.nblocks
        self.resblock_dropout = cfg.resblock_dropout
        self.use_position_embedding = cfg.use_position_embedding

        # --- Transformer Configuration ---
        self.transformer_num_heads = cfg.transformer_num_heads
        self.transformer_dim_head = cfg.transformer_dim_head
        self.transformer_dropout = cfg.transformer_dropout
        self.transformer_depth = cfg.transformer_depth
        self.transformer_mult_ff = cfg.transformer_mult_ff
        self.context_dim = cfg.context_dim

        # --- Conditioning Modules ---
        self.scene_model = build_backbone(cfg.backbone)
        self.text_encoder = None  # Lazily initialized
        self.text_processor = TextConditionProcessor(
            text_dim=512,
            context_dim=self.context_dim,
            dropout=self.transformer_dropout,
            use_negative_prompts=getattr(cfg, 'use_negative_prompts', True),
        )
        self.grasp_encoder = GraspNet(input_dim=self.d_x, output_dim=512)

        # --- Text Conditioning Configuration ---
        # self.use_text_condition = getattr(cfg, 'use_text_condition', True)
        # self.text_dropout_prob = getattr(cfg, 'text_dropout_prob', 0.1)
        self.use_text_condition = cfg.use_text_condition
        self.text_dropout_prob = cfg.text_dropout_prob
        self.use_negative_prompts = getattr(cfg, 'use_negative_prompts', True)
        # --- Grasp 数控制 ---
        self.fix_num_grasps = getattr(cfg, 'fix_num_grasps', False)
        self.fixed_num_grasps = None  # 训练阶段记录

        # --- Build Model Layers ---
        self._build_model_layers(cfg)

    def _build_model_layers(self, cfg):
        """Constructs the core layers of the network."""
        time_embed_dim = self.d_model * getattr(cfg, 'time_embed_mult', 4)
        self.time_embed = nn.Sequential(
            nn.Linear(self.d_model, time_embed_dim),
            nn.SiLU(),
            nn.Linear(time_embed_dim, time_embed_dim),
        )

        # Input layer to process the fused features
        self.in_layers = nn.Sequential(
            nn.Conv1d(self.d_model, self.d_model, kernel_size=1)
        )

        # Main processing blocks
        self.layers = nn.ModuleList()
        for _ in range(self.nblocks):
            # ResBlock for time condition injection
            self.layers.append(
                ResBlock(
                    self.d_model,
                    time_embed_dim,
                    self.resblock_dropout,
                    self.d_model,
                )
            )
            # SpatialTransformer for context injection from fused features
            self.layers.append(
                SpatialTransformer(
                    self.d_model,
                    self.transformer_num_heads,
                    self.transformer_dim_head,
                    depth=self.transformer_depth,
                    dropout=self.transformer_dropout,
                    mult_ff=self.transformer_mult_ff,
                    context_dim=self.d_model,
                )
            )

        # Output layer to predict the final noise/x0
        self.out_layers = nn.Sequential(
            nn.GroupNorm(num_groups=min(32, self.d_model), num_channels=self.d_model),
            nn.SiLU(),
            nn.Conv1d(self.d_model, self.d_x, kernel_size=1),
        )

    def forward(self, x_t: torch.Tensor, ts: torch.Tensor, data: Dict) -> torch.Tensor:
        """
        Applies the model to a batch of noisy inputs.

        Args:
            x_t (torch.Tensor): The noisy input grasp pose, shape (B, C) or (B, num_grasps, C).
            ts (torch.Tensor): The batch of timesteps, shape (B,).
            data (Dict): A dictionary containing conditional information like scene and text features.

        Returns:
            torch.Tensor: The predicted noise or denoised target.
        """
        # --- 1. Prepare Inputs and Conditions ---
        scene_cond = data["scene_cond"]  # (B, N_points, 512)
        text_cond = data.get("text_cond", None)  # (B, 512)

        # 处理输入维度
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._forward_single_grasp(x_t, ts, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._forward_multi_grasp(x_t, ts, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _forward_single_grasp(self, x_t: torch.Tensor, ts: torch.Tensor, data: Dict) -> torch.Tensor:
        """处理单抓取格式（向后兼容）"""
        scene_cond = data["scene_cond"]  # (B, N_points, 512)
        text_cond = data.get("text_cond", None)  # (B, 512)

        # Ensure input has a sequence length dimension
        x_t = x_t.unsqueeze(1)  # (B, C) -> (B, 1, C)

        # --- 2. Encode Grasp and Text Conditions ---
        grasp_input = x_t.squeeze(1)  # (B, C)
        grasp_embedding = self.grasp_encoder(grasp_input)  # (B, 512)

        if text_cond is not None and self.use_text_condition:
            grasp_text_embedding = grasp_embedding + text_cond  # (B, 512)
        else:
            grasp_text_embedding = grasp_embedding  # (B, 512)

        # --- 3. Fuse Multimodal Conditions ---
        # --- 3. 构造上下文 (场景点云 + 文本 token) ---
        if text_cond is not None and self.use_text_condition:
            text_token = text_cond.unsqueeze(1)  # (B, 1, 512)
            context_tokens = torch.cat([scene_cond, text_token], dim=1)  # (B, N_points + 1, 512)
        else:
            context_tokens = scene_cond  # (B, N_points, 512)

        # --- 4. 准备 UNet 输入 ---
        h = grasp_text_embedding.unsqueeze(1)    # (B, 1, 512)
        h = rearrange(h, 'b l c -> b c l')  # (B, 512, 1)

        # --- 5. Process Timestep Embedding ---
        t_emb = timestep_embedding(ts, self.d_model)
        t_emb = self.time_embed(t_emb)  # (B, time_embed_dim)

        # --- 6. Main U-Net Path ---
        h = self.in_layers(h)  # (B, 512, 1)

        if self.use_position_embedding:
            B, C, L = h.shape
            pos_q = torch.arange(L, dtype=h.dtype, device=h.device)
            pos_embedding_q = timestep_embedding(pos_q, C)  # (L, C)
            h = h + pos_embedding_q.permute(1, 0).unsqueeze(0)  # (B, C, L)

        for i in range(self.nblocks):
            res_block_idx, transformer_idx = i * 2, i * 2 + 1
            h = self.layers[res_block_idx](h, t_emb)
            h = self.layers[transformer_idx](h, context=context_tokens)

        # --- 7. Output ---
        h = self.out_layers(h)  # (B, d_x, 1)
        h = rearrange(h, 'b c l -> b l c')  # (B, 1, d_x)
        h = h.squeeze(1)  # Restore original shape: (B, d_x)

        return h

    def _forward_multi_grasp(self, x_t: torch.Tensor, ts: torch.Tensor, data: Dict) -> torch.Tensor:
        """处理多抓取格式"""
        scene_cond = data["scene_cond"]  # (B, N_points, 512)
        text_cond = data.get("text_cond", None)  # (B, 512)

        B, num_grasps, pose_dim = x_t.shape

        # --- 记录训练阶段的抓取数 ---
        if self.fix_num_grasps and self.training and self.fixed_num_grasps is None:
            self.fixed_num_grasps = num_grasps

        # --- 2. Encode Grasp and Text Conditions ---
        # 抓取编码: [B, num_grasps, pose_dim] -> [B, num_grasps, 512]
        grasp_embedding = self.grasp_encoder(x_t)

        # 文本条件广播
        if text_cond is not None and self.use_text_condition:
            # 广播文本条件到所有抓取: [B, 512] -> [B, num_grasps, 512]
            text_cond_expanded = text_cond.unsqueeze(1).expand(-1, num_grasps, -1)
            grasp_text_embedding = grasp_embedding + text_cond_expanded
        else:
            grasp_text_embedding = grasp_embedding

        # --- 3. 构造上下文 (场景点云 + 文本 token) ---
        if text_cond is not None and self.use_text_condition:
            text_token = text_cond.unsqueeze(1)  # [B, 1, 512]
            context_tokens = torch.cat([scene_cond, text_token], dim=1)  # [B, N_points+1, 512]
        else:
            context_tokens = scene_cond  # [B, N_points, 512]

        # --- 4. 时间嵌入 ---
        t_emb = timestep_embedding(ts, self.d_model)  # [B, 512]
        t_emb = self.time_embed(t_emb)  # [B, 1024]

        # --- 5. 并行处理多个抓取 ---
        return self._process_multi_grasp_unet(grasp_text_embedding, context_tokens, t_emb, B, num_grasps)

    def _process_multi_grasp_unet(self, grasp_features, context_tokens, t_emb, B, num_grasps):
        """并行处理多个抓取的UNet主干网络"""
        # grasp_features: [B, num_grasps, 512]
        # context_tokens: [B, context_len, 512]
        # t_emb: [B, 1024]

        context_len = context_tokens.shape[1]

        # 重塑为批次处理: [B*num_grasps, 512, 1]
        h = grasp_features.reshape(B * num_grasps, 512, 1)

        # 时间嵌入扩展: [B, 1024] -> [B*num_grasps, 1024]
        t_emb_expanded = t_emb.unsqueeze(1).expand(-1, num_grasps, -1).contiguous()
        t_emb_expanded = t_emb_expanded.view(B * num_grasps, -1)

        # 上下文扩展: [B, 1, context_len, 512] -> [B*num_grasps, context_len, 512]
        context_expanded = context_tokens.unsqueeze(1).expand(-1, num_grasps, -1, -1).contiguous()
        context_expanded = context_expanded.view(B * num_grasps, context_len, 512)

        # UNet主干网络处理
        h = self.in_layers(h)  # [B*num_grasps, 512, 1]

        if self.use_position_embedding:
            _, C, L = h.shape
            pos_q = torch.arange(L, dtype=h.dtype, device=h.device)
            pos_embedding_q = timestep_embedding(pos_q, C)  # (L, C)
            h = h + pos_embedding_q.permute(1, 0).unsqueeze(0)  # (B*num_grasps, C, L)

        for i in range(self.nblocks):
            res_block_idx, transformer_idx = i * 2, i * 2 + 1
            h = self.layers[res_block_idx](h, t_emb_expanded)
            h = self.layers[transformer_idx](h, context=context_expanded)

        # 输出层
        h = self.out_layers(h)  # [B*num_grasps, pose_dim, 1]
        h = rearrange(h, 'b c l -> b l c')  # [B*num_grasps, 1, pose_dim]
        h = h.squeeze(1)  # [B*num_grasps, pose_dim]

        # 重塑回多抓取格式
        output = h.view(B, num_grasps, -1)  # [B, num_grasps, pose_dim]

        return output

    def condition(self, data: Dict) -> Dict:
        """
        Pre-computes and processes all conditional features (scene, text).

        Args:
            data (Dict): The raw data from the dataloader.

        Returns:
            Dict: A dictionary with processed "scene_cond" and "text_cond".
        """
        # --- 1. Scene Feature Extraction ---
        pos = data['scene_pc'].to(torch.float32)
        _, scene_feat = self.scene_model(pos)
        scene_feat = scene_feat.permute(0, 2, 1).contiguous()  # (B, N, C)

        condition_dict = {
            "scene_cond": scene_feat,
            "text_cond": None,
        }
        if self.use_negative_prompts:
            condition_dict["neg_pred"] = None
            condition_dict["neg_text_features"] = None
        
        condition_dict["text_mask"] = None


        # --- 2. Text Feature Extraction (if enabled) ---
        if not (self.use_text_condition and 'positive_prompt' in data):
            return condition_dict

        try:
            self._ensure_text_encoder()
            b = scene_feat.shape[0]

            # Encode positive and negative prompts
            pos_prompts = data['positive_prompt']
            pos_text_features = self.text_encoder.encode_positive(pos_prompts)

            neg_text_features = None
            if self.use_negative_prompts and 'negative_prompts' in data:
                neg_prompts = data['negative_prompts']
                neg_text_features = self.text_encoder.encode_negative(neg_prompts)

            # Apply text dropout during training
            if self.training:
                keep_prob = 1.0 - self.text_dropout_prob
                text_mask = torch.bernoulli(torch.full((b, 1), keep_prob, device=pos_text_features.device))
            else:
                text_mask = torch.ones(b, 1, device=pos_text_features.device)
            
            # Process text features to get final conditioning and predicted negative prompts
            scene_embedding = torch.mean(scene_feat, dim=1)  # (B, C)
            pos_text_features_out, neg_pred = self.text_processor(
                pos_text_features,
                neg_text_features,
                scene_embedding
            )

            condition_dict.update({
                "text_cond": pos_text_features_out * text_mask,
                "text_mask": text_mask,
            })
            if self.use_negative_prompts:
                condition_dict.update({
                    "neg_pred": neg_pred,  # Predicted negative prompt for CFG
                    "neg_text_features": neg_text_features, # Original negative features for loss calculation
                })

        except Exception as e:
            logging.warning(f"Text encoding failed: {e}. Falling back to scene-only conditioning.")
            # Reset text-related conditions to None
            condition_dict.update({k: None for k in condition_dict if k != "scene_cond"})

        return condition_dict

    # --- Device Management for Lazy-Loaded Encoder ---

    def _get_device(self):
        """Infers the device of the model from its parameters."""
        return next(self.parameters()).device

    def _ensure_text_encoder(self):
        """Initializes the text encoder on the correct device if it doesn't exist."""
        if self.text_encoder is None:
            device = self._get_device()
            self.text_encoder = PosNegTextEncoder(device=device)
            self.text_encoder.to(device)
            logging.info(f"Text encoder lazily initialized on device: {device}")
        else:
            # Ensure existing encoder is on the correct device
            current_device = self._get_device()
            if self.text_encoder.device != current_device:
                self.text_encoder.to(current_device)
                logging.info(f"Text encoder moved to device: {current_device}")

    def to(self, *args, **kwargs):
        """Overrides `to()` to ensure the text encoder is also moved."""
        super().to(*args, **kwargs)
        if self.text_encoder is not None:
            self.text_encoder.to(*args, **kwargs)
        return self

