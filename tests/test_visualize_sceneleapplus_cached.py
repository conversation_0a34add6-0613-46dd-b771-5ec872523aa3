#!/usr/bin/env python3
"""
测试SceneLeapPlusDatasetCached可视化功能
"""

import os
import sys
import torch
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
from visualize_sceneleapplus_cached import (
    verify_paths,
    create_coordinate_frame,
    create_point_cloud_from_sample,
    create_object_mesh,
    create_hand_meshes,
    analyze_sample_data
)

def test_basic_functions():
    """测试基础函数"""
    logger.info("测试基础可视化函数...")
    
    # 测试坐标轴创建
    try:
        frame = create_coordinate_frame(size=0.1)
        logger.info("✅ 坐标轴创建成功")
    except Exception as e:
        logger.error(f"✗ 坐标轴创建失败: {e}")
    
    # 测试点云创建
    try:
        # 创建测试点云数据 (N, 6) - xyz + rgb
        N = 1000
        xyz = np.random.rand(N, 3) * 2 - 1  # [-1, 1]范围
        rgb = np.random.rand(N, 3)  # [0, 1]范围
        scene_pc = torch.from_numpy(np.concatenate([xyz, rgb], axis=1)).float()
        
        pcd = create_point_cloud_from_sample(scene_pc)
        logger.info(f"✅ 点云创建成功: {len(pcd.points)} 个点")
    except Exception as e:
        logger.error(f"✗ 点云创建失败: {e}")
    
    # 测试物体mesh创建
    try:
        # 创建简单的立方体mesh数据
        vertices = torch.tensor([
            [0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0],  # 底面
            [0, 0, 1], [1, 0, 1], [1, 1, 1], [0, 1, 1]   # 顶面
        ], dtype=torch.float32)
        
        faces = torch.tensor([
            [0, 1, 2], [0, 2, 3],  # 底面
            [4, 7, 6], [4, 6, 5],  # 顶面
            [0, 4, 5], [0, 5, 1],  # 前面
            [2, 6, 7], [2, 7, 3],  # 后面
            [0, 3, 7], [0, 7, 4],  # 左面
            [1, 5, 6], [1, 6, 2]   # 右面
        ], dtype=torch.long)
        
        mesh = create_object_mesh(vertices, faces, color=(0, 1, 0))
        if mesh is not None:
            logger.info(f"✅ 物体mesh创建成功: {len(mesh.vertices)} 个顶点")
        else:
            logger.error("✗ 物体mesh创建失败")
    except Exception as e:
        logger.error(f"✗ 物体mesh创建失败: {e}")

def test_multi_grasp_analysis():
    """测试多抓取数据分析"""
    logger.info("测试多抓取数据分析...")
    
    # 创建模拟的多抓取样本数据
    num_grasps = 4
    sample = {
        'scene_id': 'test_scene_001',
        'obj_code': 'test_object_001',
        'positive_prompt': 'test object',
        'negative_prompts': ['background', 'noise'],
        'scene_pc': torch.randn(1000, 6),  # 1000个点，6维 (xyz+rgb)
        'object_mask': torch.rand(1000) > 0.7,  # 30%的点为目标物体
        'hand_model_pose': torch.randn(num_grasps, 23),  # 多抓取姿态
        'se3': torch.randn(num_grasps, 4, 4),  # SE3矩阵
        'obj_verts': torch.randn(100, 3),  # 物体顶点
        'obj_faces': torch.randint(0, 100, (50, 3)),  # 物体面
    }
    
    try:
        analyzed_sample = analyze_sample_data(sample, 0)
        logger.info("✅ 多抓取数据分析成功")
        
        # 验证关键字段
        if 'hand_model_pose' in analyzed_sample:
            hand_pose = analyzed_sample['hand_model_pose']
            if hand_pose.shape == (num_grasps, 23):
                logger.info(f"✅ 手部姿态形状正确: {hand_pose.shape}")
            else:
                logger.warning(f"⚠️ 手部姿态形状异常: {hand_pose.shape}")
        
        if 'se3' in analyzed_sample:
            se3_matrices = analyzed_sample['se3']
            if se3_matrices.shape == (num_grasps, 4, 4):
                logger.info(f"✅ SE3矩阵形状正确: {se3_matrices.shape}")
            else:
                logger.warning(f"⚠️ SE3矩阵形状异常: {se3_matrices.shape}")
                
    except Exception as e:
        logger.error(f"✗ 多抓取数据分析失败: {e}")

def test_dataset_loading():
    """测试数据集加载"""
    logger.info("测试数据集加载...")
    
    # 数据路径配置
    root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15"
    succ_grasp_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
    obj_root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
    
    # 首先验证路径
    if not verify_paths():
        logger.warning("数据路径验证失败，跳过数据集加载测试")
        return None
    
    try:
        # 创建数据集实例
        dataset = SceneLeapPlusDatasetCached(
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            num_grasps=4,  # 测试用较小的数量
            mode="camera_centric",
            max_grasps_per_object=2,  # 限制数据量
            mesh_scale=0.1,
            num_neg_prompts=2,
            enable_cropping=True,
            max_points=5000,  # 限制点云大小
            grasp_sampling_strategy="random",
            cache_version="v1.0_plus_test"
        )
        
        logger.info(f"✅ 数据集加载成功，包含 {len(dataset)} 个样本")
        logger.info(f"   - 每个样本抓取数量: {dataset.num_grasps}")
        
        if len(dataset) > 0:
            # 测试获取样本
            sample = dataset[0]
            logger.info(f"✅ 样本获取成功")
            logger.info(f"  - 场景ID: {sample.get('scene_id', 'N/A')}")
            logger.info(f"  - 物体代码: {sample.get('obj_code', 'N/A')}")
            
            if 'scene_pc' in sample:
                logger.info(f"  - 点云形状: {sample['scene_pc'].shape}")
            if 'hand_model_pose' in sample:
                logger.info(f"  - 手部姿态形状: {sample['hand_model_pose'].shape}")
            if 'obj_verts' in sample:
                logger.info(f"  - 物体顶点形状: {sample['obj_verts'].shape}")
            
            return dataset, sample
        else:
            logger.warning("✗ 数据集为空")
            return dataset, None
            
    except Exception as e:
        logger.error(f"✗ 数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_hand_meshes_creation():
    """测试多抓取手部mesh创建（模拟）"""
    logger.info("测试多抓取手部mesh创建...")
    
    # 创建模拟的多抓取手部姿态数据
    num_grasps = 3
    hand_poses = torch.randn(num_grasps, 23)
    
    try:
        # 注意：这里不能真正创建hand_model，因为需要LEAP模型文件
        # 但我们可以测试函数的输入验证逻辑
        logger.info(f"模拟创建 {num_grasps} 个手部mesh...")
        logger.info(f"手部姿态形状: {hand_poses.shape}")
        
        # 测试抓取索引选择逻辑
        grasp_indices = [0, 2]  # 选择第1和第3个抓取
        logger.info(f"选择的抓取索引: {grasp_indices}")
        
        # 验证索引有效性
        valid_indices = [i for i in grasp_indices if 0 <= i < num_grasps]
        if len(valid_indices) == len(grasp_indices):
            logger.info("✅ 抓取索引验证通过")
        else:
            logger.warning(f"⚠️ 部分抓取索引无效: {grasp_indices} -> {valid_indices}")
        
        logger.info("✅ 多抓取手部mesh创建逻辑测试通过")
        
    except Exception as e:
        logger.error(f"✗ 多抓取手部mesh创建测试失败: {e}")

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("SceneLeapPlusDatasetCached 可视化功能测试")
    logger.info("=" * 60)
    
    # 测试各个组件
    test_basic_functions()
    test_multi_grasp_analysis()
    test_hand_meshes_creation()
    
    # 测试数据集加载
    dataset, sample = test_dataset_loading()
    
    if dataset is not None:
        logger.info("\n✅ 所有基础测试通过！")
        logger.info("\n可以运行以下命令进行完整可视化测试:")
        logger.info("python visualize_sceneleapplus_cached.py")
        
        if sample is not None:
            logger.info("\n样本数据验证:")
            analyze_sample_data(sample, 0)
    else:
        logger.warning("\n⚠️ 数据集加载失败，请检查数据路径配置")
        logger.info("请确保以下路径存在并包含有效数据:")
        logger.info("  - /home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15")
        logger.info("  - /home/<USER>/source/grasp/SceneLeapPro/data/succ_collect")
        logger.info("  - /home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models")
        logger.info("\n但基础可视化函数测试已通过，可以进行其他测试")

if __name__ == "__main__":
    main()
