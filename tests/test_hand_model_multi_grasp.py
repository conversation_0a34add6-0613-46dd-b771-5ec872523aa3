#!/usr/bin/env python3
"""
测试HandModel多抓取架构重构的正确性
验证单抓取和多抓取格式的兼容性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel, HandModelType


def test_single_grasp_compatibility():
    """测试单抓取格式的向后兼容性"""
    print("=== 测试单抓取格式兼容性 ===")
    
    # 初始化手部模型
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        device='cpu',
        n_surface_points=100,
        rot_type='r6d'
    )
    
    # 创建单抓取输入 [B, pose_dim]
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 6  # translation + joints + r6d
    hand_pose_single = torch.randn(batch_size, pose_dim)
    
    print(f"输入形状: {hand_pose_single.shape}")
    
    # 测试set_parameters
    hand_model.set_parameters(hand_pose_single)
    
    # 验证内部状态
    assert hasattr(hand_model, 'is_multi_grasp'), "缺少is_multi_grasp属性"
    assert not hand_model.is_multi_grasp, "单抓取格式应该设置is_multi_grasp=False"
    assert hand_model.batch_size == batch_size, f"批次大小不匹配: {hand_model.batch_size} vs {batch_size}"
    
    print(f"✓ is_multi_grasp: {hand_model.is_multi_grasp}")
    print(f"✓ batch_size: {hand_model.batch_size}")
    print(f"✓ global_translation shape: {hand_model.global_translation.shape}")
    print(f"✓ global_rotation shape: {hand_model.global_rotation.shape}")
    
    # 测试__call__方法
    result = hand_model(hand_pose_single, with_surface_points=True)
    print(f"✓ surface_points shape: {result['surface_points'].shape}")
    
    print("单抓取格式测试通过!\n")


def test_multi_grasp_format():
    """测试多抓取格式"""
    print("=== 测试多抓取格式 ===")
    
    # 初始化手部模型
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        device='cpu',
        n_surface_points=100,
        rot_type='r6d'
    )
    
    # 创建多抓取输入 [B, num_grasps, pose_dim]
    batch_size = 2
    num_grasps = 3
    pose_dim = 3 + hand_model.n_dofs + 6  # translation + joints + r6d
    hand_pose_multi = torch.randn(batch_size, num_grasps, pose_dim)
    
    print(f"输入形状: {hand_pose_multi.shape}")
    
    # 测试set_parameters
    hand_model.set_parameters(hand_pose_multi)
    
    # 验证内部状态
    assert hand_model.is_multi_grasp, "多抓取格式应该设置is_multi_grasp=True"
    assert hand_model.batch_size == batch_size, f"批次大小不匹配: {hand_model.batch_size} vs {batch_size}"
    assert hand_model.num_grasps == num_grasps, f"抓取数量不匹配: {hand_model.num_grasps} vs {num_grasps}"
    
    print(f"✓ is_multi_grasp: {hand_model.is_multi_grasp}")
    print(f"✓ batch_size: {hand_model.batch_size}")
    print(f"✓ num_grasps: {hand_model.num_grasps}")
    print(f"✓ global_translation shape: {hand_model.global_translation.shape}")
    print(f"✓ global_rotation shape: {hand_model.global_rotation.shape}")
    
    # 验证重塑后的形状
    expected_translation_shape = (batch_size, num_grasps, 3)
    expected_rotation_shape = (batch_size, num_grasps, 3, 3)
    
    assert hand_model.global_translation.shape == expected_translation_shape, \
        f"global_translation形状错误: {hand_model.global_translation.shape} vs {expected_translation_shape}"
    assert hand_model.global_rotation.shape == expected_rotation_shape, \
        f"global_rotation形状错误: {hand_model.global_rotation.shape} vs {expected_rotation_shape}"
    
    # 测试__call__方法
    result = hand_model(hand_pose_multi, with_surface_points=True)
    print(f"✓ surface_points shape: {result['surface_points'].shape}")
    
    print("多抓取格式测试通过!\n")


def test_scene_pc_processing():
    """测试scene_pc的维度匹配处理"""
    print("=== 测试scene_pc处理 ===")
    
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        device='cpu',
        n_surface_points=50,
        rot_type='r6d'
    )
    
    # 多抓取格式
    batch_size = 2
    num_grasps = 3
    pose_dim = 3 + hand_model.n_dofs + 6
    hand_pose_multi = torch.randn(batch_size, num_grasps, pose_dim)
    
    # 创建scene_pc
    n_points = 100
    scene_pc = torch.randn(batch_size, n_points, 4)  # [B, N, 4]
    
    print(f"hand_pose shape: {hand_pose_multi.shape}")
    print(f"scene_pc shape: {scene_pc.shape}")
    
    # 测试with_penetration
    try:
        result = hand_model(hand_pose_multi, scene_pc=scene_pc, with_penetration=True)
        print(f"✓ penetration shape: {result['penetration'].shape}")
        
        # 验证penetration的形状应该是[B*num_grasps, N]
        expected_penetration_shape = (batch_size * num_grasps, n_points)
        assert result['penetration'].shape == expected_penetration_shape, \
            f"penetration形状错误: {result['penetration'].shape} vs {expected_penetration_shape}"
        
    except Exception as e:
        print(f"penetration测试失败: {e}")
    
    print("scene_pc处理测试通过!\n")


def test_dimension_validation():
    """测试维度验证"""
    print("=== 测试维度验证 ===")
    
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        device='cpu',
        rot_type='r6d'
    )
    
    # 测试错误的维度
    try:
        # 4D张量应该报错
        wrong_pose = torch.randn(2, 3, 4, 25)
        hand_model.set_parameters(wrong_pose)
        assert False, "应该抛出维度错误"
    except ValueError as e:
        print(f"✓ 正确捕获4D张量错误: {e}")
    
    # 测试错误的pose_dim
    try:
        wrong_pose = torch.randn(2, 3, 10)  # pose_dim太小
        hand_model.set_parameters(wrong_pose)
        assert False, "应该抛出pose_dim错误"
    except AssertionError as e:
        print(f"✓ 正确捕获pose_dim错误: {e}")
    
    print("维度验证测试通过!\n")


if __name__ == "__main__":
    print("开始HandModel多抓取架构重构测试...\n")
    
    try:
        test_single_grasp_compatibility()
        test_multi_grasp_format()
        test_scene_pc_processing()
        test_dimension_validation()
        
        print("🎉 所有测试通过! HandModel多抓取重构成功!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
