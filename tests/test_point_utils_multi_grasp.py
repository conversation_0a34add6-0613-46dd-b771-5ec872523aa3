#!/usr/bin/env python3
"""
测试多抓取点云处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.point_utils import (
    transform_point,
    transform_points,
    transform_points_batch_numpy,
    transform_points_batch_torch,
    transform_points_multi_grasp,
    get_points_in_grid,
    get_points_in_grid_batch,
    apply_se3_to_points,
    compute_point_distances,
    sample_points_on_surface
)


def test_backward_compatibility():
    """测试向后兼容性"""
    print("测试向后兼容性...")
    
    # 测试单点变换
    T = np.eye(4)
    T[:3, 3] = [1, 2, 3]  # 平移
    point = np.array([0, 0, 0])
    
    transformed = transform_point(T, point)
    expected = np.array([1, 2, 3])
    assert np.allclose(transformed, expected), "单点变换失败"
    
    # 测试多点变换
    points = np.random.randn(10, 3)
    transformed_points = transform_points(T, points)
    expected_points = points + np.array([1, 2, 3])
    assert np.allclose(transformed_points, expected_points), "多点变换失败"
    
    print("✅ 向后兼容性测试通过")


def test_numpy_batch_transforms():
    """测试NumPy批量变换"""
    print("测试NumPy批量变换...")
    
    # 测试数据
    B, N, num_grasps = 4, 100, 6
    
    # 1. 单变换多点云
    T_single = np.eye(4)
    T_single[:3, 3] = [1, 0, 0]  # X方向平移1
    points_batch = np.random.randn(B, N, 3)
    
    transformed = transform_points_batch_numpy(T_single, points_batch)
    assert transformed.shape == (B, N, 3), f"形状不匹配: {transformed.shape}"
    
    # 验证变换正确性
    expected = points_batch + np.array([1, 0, 0])
    assert np.allclose(transformed, expected), "单变换多点云失败"
    
    # 2. 多变换多点云
    T_batch = np.tile(np.eye(4)[None, :, :], (B, 1, 1))
    for i in range(B):
        T_batch[i, :3, 3] = [i, 0, 0]  # 不同的平移
    
    transformed = transform_points_batch_numpy(T_batch, points_batch)
    assert transformed.shape == (B, N, 3), f"形状不匹配: {transformed.shape}"
    
    # 3. 多抓取变换
    T_multi_grasp = np.tile(np.eye(4)[None, None, :, :], (B, num_grasps, 1, 1))
    for i in range(B):
        for j in range(num_grasps):
            T_multi_grasp[i, j, :3, 3] = [i, j, 0]
    
    transformed = transform_points_batch_numpy(T_multi_grasp, points_batch)
    assert transformed.shape == (B, num_grasps, N, 3), f"形状不匹配: {transformed.shape}"
    
    print("✅ NumPy批量变换测试通过")


def test_torch_batch_transforms():
    """测试PyTorch批量变换"""
    print("测试PyTorch批量变换...")
    
    # 测试数据
    B, N, num_grasps = 4, 100, 6
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 1. 单变换多点云
    T_single = torch.eye(4, device=device)
    T_single[:3, 3] = torch.tensor([1, 0, 0], device=device)
    points_batch = torch.randn(B, N, 3, device=device)
    
    transformed = transform_points_batch_torch(T_single, points_batch)
    assert transformed.shape == (B, N, 3), f"形状不匹配: {transformed.shape}"
    
    # 验证变换正确性
    expected = points_batch + torch.tensor([1, 0, 0], device=device)
    assert torch.allclose(transformed, expected), "单变换多点云失败"
    
    # 2. 多变换多点云
    T_batch = torch.eye(4, device=device).unsqueeze(0).expand(B, -1, -1).clone()
    for i in range(B):
        T_batch[i, :3, 3] = torch.tensor([i, 0, 0], device=device)
    
    transformed = transform_points_batch_torch(T_batch, points_batch)
    assert transformed.shape == (B, N, 3), f"形状不匹配: {transformed.shape}"
    
    # 3. 多抓取变换
    T_multi_grasp = torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).expand(B, num_grasps, -1, -1).clone()
    for i in range(B):
        for j in range(num_grasps):
            T_multi_grasp[i, j, :3, 3] = torch.tensor([i, j, 0], device=device)
    
    transformed = transform_points_batch_torch(T_multi_grasp, points_batch)
    assert transformed.shape == (B, num_grasps, N, 3), f"形状不匹配: {transformed.shape}"
    
    print("✅ PyTorch批量变换测试通过")


def test_unified_interface():
    """测试统一接口"""
    print("测试统一接口...")
    
    # NumPy版本
    T_np = np.eye(4)
    T_np[:3, 3] = [1, 2, 3]
    points_np = np.random.randn(10, 3)
    
    result_np = transform_points_multi_grasp(T_np, points_np)
    expected_np = points_np + np.array([1, 2, 3])
    assert np.allclose(result_np, expected_np), "NumPy统一接口失败"
    
    # PyTorch版本
    T_torch = torch.eye(4)
    T_torch[:3, 3] = torch.tensor([1, 2, 3])
    points_torch = torch.randn(10, 3)
    
    result_torch = transform_points_multi_grasp(T_torch, points_torch)
    expected_torch = points_torch + torch.tensor([1, 2, 3])
    assert torch.allclose(result_torch, expected_torch), "PyTorch统一接口失败"
    
    print("✅ 统一接口测试通过")


def test_grid_generation():
    """测试网格生成"""
    print("测试网格生成...")
    
    # 测试原始函数
    lb = np.array([0, 0, 0])
    ub = np.array([1, 1, 1])
    grid = get_points_in_grid(lb, ub, 5, 5, 5)
    assert grid.shape == (5, 5, 5, 3), f"网格形状不匹配: {grid.shape}"
    
    # 测试批量网格生成 - NumPy
    bounds_np = np.array([[0, 0, 0], [1, 1, 1]])[None, :, :]  # [1, 2, 3]
    batch_grid_np = get_points_in_grid_batch(bounds_np, (3, 3, 3))
    assert batch_grid_np.shape == (1, 3, 3, 3, 3), f"批量网格形状不匹配: {batch_grid_np.shape}"
    
    # 测试批量网格生成 - PyTorch
    bounds_torch = torch.tensor([[0, 0, 0], [1, 1, 1]], dtype=torch.float32)[None, :, :]
    batch_grid_torch = get_points_in_grid_batch(bounds_torch, (3, 3, 3))
    assert batch_grid_torch.shape == (1, 3, 3, 3, 3), f"PyTorch批量网格形状不匹配: {batch_grid_torch.shape}"
    
    print("✅ 网格生成测试通过")


def test_point_distances():
    """测试点距离计算"""
    print("测试点距离计算...")
    
    # NumPy版本
    points1_np = np.random.randn(10, 3)
    points2_np = np.random.randn(15, 3)
    
    distances_np = compute_point_distances(points1_np, points2_np)
    assert distances_np.shape == (10, 15), f"距离矩阵形状不匹配: {distances_np.shape}"
    
    # 验证距离计算正确性
    manual_dist = np.sqrt(np.sum((points1_np[0] - points2_np[0]) ** 2))
    assert np.isclose(distances_np[0, 0], manual_dist), "距离计算不正确"
    
    # PyTorch版本
    points1_torch = torch.randn(10, 3)
    points2_torch = torch.randn(15, 3)
    
    distances_torch = compute_point_distances(points1_torch, points2_torch)
    assert distances_torch.shape == (10, 15), f"PyTorch距离矩阵形状不匹配: {distances_torch.shape}"
    
    # 批量版本
    points1_batch = torch.randn(4, 10, 3)
    points2_batch = torch.randn(4, 15, 3)
    
    distances_batch = compute_point_distances(points1_batch, points2_batch)
    assert distances_batch.shape == (4, 10, 15), f"批量距离矩阵形状不匹配: {distances_batch.shape}"
    
    print("✅ 点距离计算测试通过")


def test_surface_sampling():
    """测试表面采样"""
    print("测试表面采样...")
    
    # NumPy版本
    vertices_np = np.random.randn(100, 3)
    faces_np = np.random.randint(0, 100, (50, 3))
    
    sampled_np = sample_points_on_surface(vertices_np, faces_np, 20)
    assert sampled_np.shape == (20, 3), f"采样点形状不匹配: {sampled_np.shape}"
    
    # PyTorch版本
    vertices_torch = torch.randn(100, 3)
    faces_torch = torch.randint(0, 100, (50, 3))
    
    sampled_torch = sample_points_on_surface(vertices_torch, faces_torch, 20)
    assert sampled_torch.shape == (20, 3), f"PyTorch采样点形状不匹配: {sampled_torch.shape}"
    
    # 批量版本
    vertices_batch = torch.randn(4, 100, 3)
    faces_batch = torch.randint(0, 100, (4, 50, 3))
    
    sampled_batch = sample_points_on_surface(vertices_batch, faces_batch, 20)
    assert sampled_batch.shape == (4, 20, 3), f"批量采样点形状不匹配: {sampled_batch.shape}"
    
    print("✅ 表面采样测试通过")


def test_performance_comparison():
    """测试性能对比"""
    print("测试性能对比...")
    
    import time
    
    B, N, num_grasps = 32, 1000, 8
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 准备数据
    transforms_torch = torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).expand(B, num_grasps, -1, -1)
    points_torch = torch.randn(B, N, 3, device=device)
    
    # 测试PyTorch批量处理性能
    torch.cuda.synchronize() if device.type == 'cuda' else None
    start_time = time.time()
    
    for _ in range(10):
        result = transform_points_batch_torch(transforms_torch, points_torch)
    
    torch.cuda.synchronize() if device.type == 'cuda' else None
    torch_time = time.time() - start_time
    
    print(f"PyTorch批量处理 ({device}): {torch_time:.4f}秒")
    print(f"处理数据量: {B} × {num_grasps} × {N} = {B * num_grasps * N} 个点")
    
    print("✅ 性能对比测试完成")


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("开始运行多抓取点云处理功能测试")
    print("=" * 60)
    
    test_backward_compatibility()
    test_numpy_batch_transforms()
    test_torch_batch_transforms()
    test_unified_interface()
    test_grid_generation()
    test_point_distances()
    test_surface_sampling()
    test_performance_comparison()
    
    print("=" * 60)
    print("🎉 所有测试通过！多抓取点云处理功能正常")
    print("=" * 60)


if __name__ == "__main__":
    run_all_tests()
