#!/usr/bin/env python3
"""
测试穷尽采样设计：对每个(场景,物体,视角)的所有抓取按num_grasps分成多份
"""

import os
import sys
import torch
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

class ExhaustiveSceneLeapPlusDataset:
    """
    穷尽采样设计：对每个(场景,物体,视角)的所有抓取按num_grasps分成多份
    """
    
    def __init__(self, base_dataset: SceneLeapPlusDatasetCached, num_grasps: int, 
                 sampling_strategy: str = "sequential"):
        """
        Args:
            base_dataset: 基础数据集
            num_grasps: 每个数据项包含的抓取数
            sampling_strategy: 采样策略
                - "sequential": 顺序分割
                - "random": 随机分割
                - "farthest_point": 最远点采样分割
                - "interleaved": 交错采样
        """
        self.base_dataset = base_dataset
        self.num_grasps = num_grasps
        self.sampling_strategy = sampling_strategy
        
        # 构建穷尽数据索引
        self.exhaustive_data = self._build_exhaustive_data_index()
        
        print(f"穷尽采样设计统计:")
        print(f"  - 原始数据项数: {len(base_dataset.data)}")
        print(f"  - 新数据项数: {len(self.exhaustive_data)}")
        print(f"  - 扩展倍数: {len(self.exhaustive_data) / len(base_dataset.data):.2f}")
        print(f"  - num_grasps: {num_grasps}")
        print(f"  - 采样策略: {sampling_strategy}")
    
    def _build_exhaustive_data_index(self) -> List[Dict[str, Any]]:
        """构建穷尽数据索引"""
        exhaustive_data = []
        
        # 按(场景,物体)分组统计抓取数
        scene_object_grasps = {}
        for scene_id, scene_data in self.base_dataset.hand_pose_data.items():
            for obj_code, poses_tensor in scene_data.items():
                if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                    key = f"{scene_id}_{obj_code}"
                    # 应用max_grasps_per_object限制
                    max_grasps = self.base_dataset.max_grasps_per_object
                    if max_grasps is not None:
                        actual_grasps = min(poses_tensor.shape[0], max_grasps)
                    else:
                        actual_grasps = poses_tensor.shape[0]
                    scene_object_grasps[key] = actual_grasps
        
        # 为每个原始数据项创建多个穷尽数据项
        for original_item in self.base_dataset.data:
            scene_id = original_item['scene_id']
            obj_code = original_item['object_code']
            view_idx = original_item['depth_view_index']
            
            key = f"{scene_id}_{obj_code}"
            total_grasps = scene_object_grasps.get(key, 0)
            
            if total_grasps > 0:
                # 计算可以分成多少份
                num_chunks = total_grasps // self.num_grasps
                remainder = total_grasps % self.num_grasps
                
                # 创建完整的chunks
                for chunk_idx in range(num_chunks):
                    exhaustive_data.append({
                        'scene_id': scene_id,
                        'object_code': obj_code,
                        'depth_view_index': view_idx,
                        'category_id_for_masking': original_item['category_id_for_masking'],
                        'chunk_idx': chunk_idx,
                        'total_chunks': num_chunks + (1 if remainder > 0 else 0),
                        'total_grasps': total_grasps,
                        'is_remainder': False
                    })
                
                # 如果有剩余，创建一个剩余chunk（会重复一些抓取来填满num_grasps）
                if remainder > 0:
                    exhaustive_data.append({
                        'scene_id': scene_id,
                        'object_code': obj_code,
                        'depth_view_index': view_idx,
                        'category_id_for_masking': original_item['category_id_for_masking'],
                        'chunk_idx': num_chunks,
                        'total_chunks': num_chunks + 1,
                        'total_grasps': total_grasps,
                        'is_remainder': True,
                        'remainder_size': remainder
                    })
        
        return exhaustive_data
    
    def _get_grasp_indices_for_chunk(self, total_grasps: int, chunk_idx: int, 
                                   is_remainder: bool = False, remainder_size: int = 0) -> List[int]:
        """根据采样策略获取chunk的抓取索引"""
        
        if self.sampling_strategy == "sequential":
            # 顺序分割
            if is_remainder:
                # 对于剩余部分，重复最后几个抓取来填满
                start_idx = chunk_idx * self.num_grasps
                indices = list(range(start_idx, total_grasps))
                # 填充到num_grasps
                while len(indices) < self.num_grasps:
                    indices.extend(indices[:self.num_grasps - len(indices)])
                return indices[:self.num_grasps]
            else:
                start_idx = chunk_idx * self.num_grasps
                return list(range(start_idx, start_idx + self.num_grasps))
        
        elif self.sampling_strategy == "random":
            # 随机分割：为每个chunk随机选择不重复的抓取
            np.random.seed(chunk_idx)  # 确保可重现
            all_indices = list(range(total_grasps))
            np.random.shuffle(all_indices)
            
            if is_remainder:
                start_idx = chunk_idx * self.num_grasps
                selected = all_indices[start_idx:]
                # 填充到num_grasps
                while len(selected) < self.num_grasps:
                    selected.extend(all_indices[:self.num_grasps - len(selected)])
                return selected[:self.num_grasps]
            else:
                start_idx = chunk_idx * self.num_grasps
                return all_indices[start_idx:start_idx + self.num_grasps]
        
        elif self.sampling_strategy == "interleaved":
            # 交错采样：每个chunk包含均匀分布的抓取
            if is_remainder:
                # 对剩余部分使用顺序策略
                return self._get_grasp_indices_for_chunk(total_grasps, chunk_idx, True, remainder_size)
            else:
                # 计算步长
                step = total_grasps // self.num_grasps
                start_offset = chunk_idx % step
                indices = []
                for i in range(self.num_grasps):
                    idx = start_offset + i * step
                    if idx < total_grasps:
                        indices.append(idx)
                
                # 如果不够，用顺序填充
                while len(indices) < self.num_grasps:
                    indices.append(indices[-1] if indices else 0)
                
                return indices[:self.num_grasps]
        
        elif self.sampling_strategy == "farthest_point":
            # 最远点采样：每个chunk选择空间上最分散的抓取
            # 这里简化实现，实际需要基于抓取的3D位置
            if is_remainder:
                return self._get_grasp_indices_for_chunk(total_grasps, chunk_idx, True, remainder_size)
            else:
                # 简化：使用均匀分布近似最远点
                indices = np.linspace(0, total_grasps-1, self.num_grasps, dtype=int)
                # 为每个chunk添加偏移
                offset = chunk_idx * (total_grasps // (total_grasps // self.num_grasps))
                indices = [(idx + offset) % total_grasps for idx in indices]
                return indices
        
        else:
            raise ValueError(f"Unknown sampling strategy: {self.sampling_strategy}")
    
    def __len__(self):
        return len(self.exhaustive_data)
    
    def __getitem__(self, idx):
        """获取穷尽采样的数据项"""
        if idx >= len(self.exhaustive_data):
            raise IndexError(f"Index {idx} out of bounds")
        
        item_data = self.exhaustive_data[idx]
        
        # 获取该物体的所有抓取
        scene_id = item_data['scene_id']
        obj_code = item_data['object_code']
        
        poses_tensor = self.base_dataset.hand_pose_data[scene_id][obj_code]
        max_grasps = self.base_dataset.max_grasps_per_object
        if max_grasps is not None:
            poses_tensor = poses_tensor[:max_grasps]
        
        # 根据采样策略获取这个chunk的抓取索引
        grasp_indices = self._get_grasp_indices_for_chunk(
            item_data['total_grasps'],
            item_data['chunk_idx'],
            item_data.get('is_remainder', False),
            item_data.get('remainder_size', 0)
        )
        
        # 提取对应的抓取
        selected_poses = poses_tensor[grasp_indices]
        
        return {
            'hand_model_pose': selected_poses,
            'scene_id': scene_id,
            'object_code': obj_code,
            'depth_view_index': item_data['depth_view_index'],
            'chunk_info': f"chunk {item_data['chunk_idx']}/{item_data['total_chunks']-1}",
            'grasp_indices': grasp_indices,
            'sampling_strategy': self.sampling_strategy
        }

def test_exhaustive_sampling():
    """测试穷尽采样设计"""
    
    print("=" * 80)
    print("测试穷尽采样设计")
    print("=" * 80)
    
    # 创建基础数据集
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric",
        "max_grasps_per_object": 20,  # 使用较小值便于测试
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus_exhaustive",
        "cache_mode": "train"
    }
    
    base_dataset = SceneLeapPlusDatasetCached(**config)
    
    # 测试不同的采样策略
    strategies = ["sequential", "random", "interleaved"]
    
    for strategy in strategies:
        print(f"\n{'='*20} 测试策略: {strategy} {'='*20}")
        
        exhaustive_dataset = ExhaustiveSceneLeapPlusDataset(
            base_dataset, num_grasps=4, sampling_strategy=strategy
        )
        
        # 测试前几个样本
        print(f"\n前3个样本:")
        for i in range(min(3, len(exhaustive_dataset))):
            sample = exhaustive_dataset[i]
            print(f"  样本 {i}:")
            print(f"    场景-物体: {sample['scene_id'][:20]}..._{sample['object_code'][:30]}...")
            print(f"    视角: {sample['depth_view_index']}")
            print(f"    chunk信息: {sample['chunk_info']}")
            print(f"    抓取索引: {sample['grasp_indices']}")
            print(f"    抓取形状: {sample['hand_model_pose'].shape}")
        
        # 分析数据利用率
        total_original_items = len(base_dataset.data)
        total_exhaustive_items = len(exhaustive_dataset)
        
        print(f"\n数据利用率分析:")
        print(f"  - 原始数据项: {total_original_items}")
        print(f"  - 穷尽数据项: {total_exhaustive_items}")
        print(f"  - 扩展倍数: {total_exhaustive_items / total_original_items:.2f}")
    
    # 清理
    if hasattr(base_dataset, '_cleanup'):
        base_dataset._cleanup()

if __name__ == "__main__":
    test_exhaustive_sampling()
