#!/usr/bin/env python3
"""
端到端多抓取集成测试
验证整个重构的完整流程：数据流、模型训练、验证和推理
"""

import torch
import torch.nn as nn
import sys
import os
import pytest
import tempfile
import shutil
from typing import Dict, List, Tuple
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.diffuser_lightning import DDPMLightning
from utils.hand_helper import process_hand_pose, process_hand_pose_test
from omegaconf import OmegaConf


class MockDataModule:
    """模拟数据模块用于测试"""
    
    def __init__(self, batch_size=4, num_grasps=8):
        self.batch_size = batch_size
        self.num_grasps = num_grasps
        
    def create_multi_grasp_batch(self):
        """创建多抓取格式的批次数据"""
        B, num_grasps = self.batch_size, self.num_grasps
        
        batch = {
            # 多抓取数据
            'hand_model_pose': torch.randn(B, num_grasps, 23),  # [B, num_grasps, 23]
            'se3': torch.eye(4).unsqueeze(0).unsqueeze(0).repeat(B, num_grasps, 1, 1),  # [B, num_grasps, 4, 4]
            
            # 场景数据（保持不变）
            'scene_pc': torch.randn(B, 10000, 6),  # [B, N, 6] (xyz + rgb)
            
            # 文本条件（可选）
            'positive_prompt': ['grasp the object'] * B,
            'negative_prompt': ['avoid collision'] * B,
            
            # 其他元数据
            'scene_id': list(range(B)),
            'object_id': list(range(B)),
        }
        
        return batch
    
    def create_single_grasp_batch(self):
        """创建单抓取格式的批次数据（向后兼容测试）"""
        B = self.batch_size
        
        batch = {
            # 单抓取数据
            'hand_model_pose': torch.randn(B, 23),  # [B, 23]
            'se3': torch.eye(4).unsqueeze(0).repeat(B, 1, 1),  # [B, 4, 4]
            
            # 场景数据
            'scene_pc': torch.randn(B, 10000, 6),  # [B, N, 6]
            
            # 文本条件
            'positive_prompt': ['grasp the object'] * B,
            
            # 元数据
            'scene_id': list(range(B)),
            'object_id': list(range(B)),
        }
        
        return batch
    
    def train_dataloader(self):
        """模拟训练数据加载器"""
        return [self.create_multi_grasp_batch() for _ in range(5)]
    
    def val_dataloader(self):
        """模拟验证数据加载器"""
        return [self.create_multi_grasp_batch() for _ in range(3)]


def load_multi_grasp_config():
    """加载多抓取配置"""
    config_path = Path(__file__).parent.parent / "config" / "config.yaml"

    if config_path.exists():
        try:
            cfg = OmegaConf.load(config_path)

            # 确保多抓取配置启用
            if not hasattr(cfg, 'multi_grasp_config'):
                cfg.multi_grasp_config = OmegaConf.create({
                    'training': {'multi_grasp_enabled': True}
                })
            cfg.multi_grasp_config.training.multi_grasp_enabled = True

            # 设置模型配置
            if hasattr(cfg, 'model') and hasattr(cfg.model, 'multi_grasp'):
                cfg.model.multi_grasp.enabled = True
            elif hasattr(cfg, 'model'):
                cfg.model.multi_grasp = OmegaConf.create({'enabled': True, 'num_grasps': 8})
            else:
                # 如果没有model配置，创建最小配置
                return create_minimal_config()

            return cfg
        except Exception as e:
            print(f"配置加载失败: {e}")
            return create_minimal_config()
    else:
        # 创建最小配置用于测试
        return create_minimal_config()


def create_minimal_config():
    """创建最小配置用于测试"""
    cfg = OmegaConf.create({
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'batch_size': 4,
        'rot_type': 'r6d',
        'mode': 'camera_centric',
        'multi_grasp_config': {
            'training': {'multi_grasp_enabled': True}
        },
        'model': {
            'name': 'GraspDiffuser',
            'steps': 10,  # 减少步数用于快速测试
            'pred_x0': True,
            'use_cfg': False,
            'rot_type': 'r6d',
            'mode': 'camera_centric',
            'batch_size': 4,
            'print_freq': 500,
            'schedule_cfg': {
                'beta': [0.0001, 0.01],
                'beta_schedule': 'linear',
                's': 0.008
            },
            'optimizer': {
                'name': 'adam',
                'lr': 0.0001,
                'weight_decay': 0.0001
            },
            'scheduler': {
                'name': 'cosine',
                't_max': 1000,
                'min_lr': 1e-5
            },
            'rand_t_type': 'half',
            'loss_type': 'l2',
            'out_sigmoid': False,
            'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
            'multi_grasp': {
                'enabled': True,
                'num_grasps': 8
            },
            'decoder': {
                'name': 'unet',  # 添加缺失的decoder名称
                'rot_type': 'r6d',  # 添加rot_type配置
                'd_model': 512,
                'time_embed_mult': 2,
                'nblocks': 2,  # 减少块数用于快速测试
                'resblock_dropout': 0.0,
                'transformer_num_heads': 8,
                'transformer_dim_head': 64,
                'transformer_dropout': 0.1,
                'transformer_depth': 1,
                'transformer_mult_ff': 2,
                'context_dim': 512,
                'use_position_embedding': False,
                'use_text_condition': True,
                'text_dropout_prob': 0.1,
                'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
                'backbone': {
                    'name': 'pointnet2',
                    'use_pooling': False,
                    'layer1': {
                        'npoint': 512,
                        'radius_list': [0.04],
                        'nsample_list': [32],
                        'mlp_list': [3, 64, 64, 128]
                    },
                    'layer2': {
                        'npoint': 256,
                        'radius_list': [0.1],
                        'nsample_list': [16],
                        'mlp_list': [128, 128, 128, 256]
                    },
                    'layer3': {
                        'npoint': 128,
                        'radius_list': [0.2],
                        'nsample_list': [16],
                        'mlp_list': [256, 128, 128, 256]
                    },
                    'layer4': {
                        'npoint': 64,
                        'radius_list': [0.3],
                        'nsample_list': [8],
                        'mlp_list': [256, 512, 512]
                    },
                    'use_xyz': True,
                    'normalize_xyz': True
                }
            },
            'criterion': {
                'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
                'rot_type': 'r6d',
                'mode': 'camera_centric',
                'hand_model': {
                    'n_surface_points': 1024,
                    'rot_type': 'r6d',
                    'device': 'cuda:0' if torch.cuda.is_available() else 'cpu'
                },
                'loss_weights': {
                    'translation': 10.0,
                    'rotation': 10.0,
                    'qpos': 1.0,
                    'hand_chamfer': 0.0,
                    'neg_loss': 0.1
                },
                'cost_weights': {
                    'hand_mesh': 0.0,
                    'qpos': 1.0,
                    'translation': 2.0,
                    'rotation': 2.0
                },
                'q1': {
                    'lambda_torque': 10,
                    'm': 8,
                    'mu': 1,
                    'nms': True,
                    'thres_contact': 0.01,
                    'thres_pen': 0.005,
                    'thres_tpen': 0.01,
                    'rot_type': 'r6d'
                }
            }
        }
    })
    return cfg


class TestMultiGraspIntegration:
    """多抓取集成测试类"""
    
    @pytest.fixture
    def config(self):
        """配置fixture"""
        return load_multi_grasp_config()
    
    @pytest.fixture
    def datamodule(self):
        """数据模块fixture"""
        return MockDataModule(batch_size=4, num_grasps=8)
    
    @pytest.fixture
    def model(self, config):
        """模型fixture"""
        # 创建模型（这里需要根据实际的模型初始化方式调整）
        try:
            model = DDPMLightning(config.model)
            return model
        except Exception as e:
            pytest.skip(f"无法创建模型: {e}")
    
    def test_config_loading(self, config):
        """测试配置加载"""
        assert config is not None
        assert hasattr(config, 'model')
        
        # 检查多抓取配置
        if hasattr(config, 'multi_grasp_config'):
            assert config.multi_grasp_config.training.multi_grasp_enabled == True
        
        print("✅ 配置加载测试通过")
    
    def test_data_format_validation(self, datamodule):
        """测试数据格式验证"""
        # 测试多抓取数据格式
        multi_batch = datamodule.create_multi_grasp_batch()
        
        assert multi_batch['hand_model_pose'].dim() == 3  # [B, num_grasps, pose_dim]
        assert multi_batch['se3'].dim() == 4              # [B, num_grasps, 4, 4]
        assert multi_batch['scene_pc'].dim() == 3         # [B, N, 6]
        
        B, num_grasps, pose_dim = multi_batch['hand_model_pose'].shape
        assert B == datamodule.batch_size
        assert num_grasps == datamodule.num_grasps
        assert pose_dim == 23
        
        # 测试单抓取数据格式（向后兼容）
        single_batch = datamodule.create_single_grasp_batch()
        
        assert single_batch['hand_model_pose'].dim() == 2  # [B, pose_dim]
        assert single_batch['se3'].dim() == 3              # [B, 4, 4]
        
        print("✅ 数据格式验证测试通过")
    
    def test_data_preprocessing(self, datamodule, config):
        """测试数据预处理"""
        batch = datamodule.create_multi_grasp_batch()
        
        # 测试手部姿态处理
        processed_batch = process_hand_pose(batch, 
                                          rot_type=config.rot_type, 
                                          mode=config.mode)
        
        assert 'norm_pose' in processed_batch
        assert processed_batch['norm_pose'].dim() == 3  # [B, num_grasps, pose_dim]
        
        # 测试测试阶段处理
        test_processed = process_hand_pose_test(batch,
                                              rot_type=config.rot_type,
                                              mode=config.mode)
        
        assert 'norm_pose' in test_processed
        
        print("✅ 数据预处理测试通过")
    
    @pytest.mark.skipif(not torch.cuda.is_available(), reason="需要GPU进行模型测试")
    def test_model_initialization(self, model, config):
        """测试模型初始化"""
        assert model is not None

        # 检查模型是否在正确的设备上
        device = torch.device(config.device)
        model = model.to(device)

        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        assert total_params > 0
        assert trainable_params > 0

        print(f"✅ 模型初始化测试通过 - 总参数: {total_params:,}, 可训练参数: {trainable_params:,}")

    @pytest.mark.skipif(not torch.cuda.is_available(), reason="需要GPU进行训练测试")
    def test_training_step(self, model, datamodule, config):
        """测试训练步骤"""
        device = torch.device(config.device)
        model = model.to(device)
        model.train()

        # 获取训练批次
        batch = datamodule.create_multi_grasp_batch()

        # 将数据移到设备
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                batch[key] = value.to(device)

        try:
            # 执行训练步骤
            loss = model.training_step(batch, 0)

            assert isinstance(loss, torch.Tensor)
            assert loss.item() >= 0
            assert not torch.isnan(loss)
            assert not torch.isinf(loss)

            print(f"✅ 训练步骤测试通过 - 损失值: {loss.item():.6f}")

        except Exception as e:
            print(f"❌ 训练步骤测试失败: {e}")
            raise

    @pytest.mark.skipif(not torch.cuda.is_available(), reason="需要GPU进行验证测试")
    def test_validation_step(self, model, datamodule, config):
        """测试验证步骤"""
        device = torch.device(config.device)
        model = model.to(device)
        model.eval()

        # 获取验证批次
        batch = datamodule.create_multi_grasp_batch()

        # 将数据移到设备
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                batch[key] = value.to(device)

        try:
            with torch.no_grad():
                # 执行验证步骤
                val_result = model.validation_step(batch, 0)

                assert isinstance(val_result, dict)
                assert 'loss' in val_result
                assert isinstance(val_result['loss'], torch.Tensor)
                assert val_result['loss'].item() >= 0

                print(f"✅ 验证步骤测试通过 - 验证损失: {val_result['loss'].item():.6f}")

        except Exception as e:
            print(f"❌ 验证步骤测试失败: {e}")
            raise

    @pytest.mark.skipif(not torch.cuda.is_available(), reason="需要GPU进行采样测试")
    def test_sampling_inference(self, model, datamodule, config):
        """测试采样推理"""
        device = torch.device(config.device)
        model = model.to(device)
        model.eval()

        # 获取推理批次
        batch = datamodule.create_multi_grasp_batch()

        # 将数据移到设备
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                batch[key] = value.to(device)

        try:
            with torch.no_grad():
                # 执行采样
                samples = model.sample(batch, k=2)

                # 验证采样结果形状
                assert isinstance(samples, torch.Tensor)
                assert samples.dim() >= 3  # 至少 [B, k, ...]

                B = batch['norm_pose'].shape[0] if 'norm_pose' in batch else batch['hand_model_pose'].shape[0]
                assert samples.shape[0] == B  # 批次维度
                assert samples.shape[1] == 2   # k个采样

                print(f"✅ 采样推理测试通过 - 采样形状: {samples.shape}")

        except Exception as e:
            print(f"❌ 采样推理测试失败: {e}")
            raise

    def test_backward_compatibility(self, datamodule, config):
        """测试向后兼容性"""
        # 创建单抓取数据
        single_batch = datamodule.create_single_grasp_batch()

        # 测试数据预处理
        processed_batch = process_hand_pose(single_batch,
                                          rot_type=config.rot_type,
                                          mode=config.mode)

        assert 'norm_pose' in processed_batch
        assert processed_batch['norm_pose'].dim() == 2  # [B, pose_dim]

        print("✅ 向后兼容性测试通过")

    def test_multi_grasp_vs_single_grasp(self, datamodule, config):
        """测试多抓取与单抓取的数据一致性"""
        # 创建数据
        multi_batch = datamodule.create_multi_grasp_batch()
        single_batch = datamodule.create_single_grasp_batch()

        # 处理数据
        multi_processed = process_hand_pose(multi_batch, config.rot_type, config.mode)
        single_processed = process_hand_pose(single_batch, config.rot_type, config.mode)

        # 验证处理后的维度
        assert multi_processed['norm_pose'].dim() == 3   # [B, num_grasps, pose_dim]
        assert single_processed['norm_pose'].dim() == 2  # [B, pose_dim]

        # 验证最后一个维度（pose_dim）应该相同
        assert multi_processed['norm_pose'].shape[-1] == single_processed['norm_pose'].shape[-1]

        print("✅ 多抓取与单抓取数据一致性测试通过")


def run_integration_tests():
    """运行集成测试"""
    print("开始运行多抓取端到端集成测试...")
    print("=" * 60)

    # 创建测试实例
    test_instance = TestMultiGraspIntegration()

    # 准备测试数据
    config = load_multi_grasp_config()
    datamodule = MockDataModule()

    test_results = {
        'passed': 0,
        'failed': 0,
        'skipped': 0
    }

    # 基础测试（不需要GPU）
    basic_tests = [
        ('配置加载', lambda: test_instance.test_config_loading(config)),
        ('数据格式验证', lambda: test_instance.test_data_format_validation(datamodule)),
        ('数据预处理', lambda: test_instance.test_data_preprocessing(datamodule, config)),
        ('向后兼容性', lambda: test_instance.test_backward_compatibility(datamodule, config)),
        ('多抓取vs单抓取一致性', lambda: test_instance.test_multi_grasp_vs_single_grasp(datamodule, config))
    ]

    print("运行基础测试...")
    for test_name, test_func in basic_tests:
        try:
            test_func()
            test_results['passed'] += 1
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            test_results['failed'] += 1

    # GPU相关测试
    if torch.cuda.is_available():
        print("\n运行GPU相关测试...")
        try:
            model = DDPMLightning(config.model)

            gpu_tests = [
                ('模型初始化', lambda: test_instance.test_model_initialization(model, config)),
                ('训练步骤', lambda: test_instance.test_training_step(model, datamodule, config)),
                ('验证步骤', lambda: test_instance.test_validation_step(model, datamodule, config)),
                ('采样推理', lambda: test_instance.test_sampling_inference(model, datamodule, config))
            ]

            for test_name, test_func in gpu_tests:
                try:
                    test_func()
                    test_results['passed'] += 1
                except Exception as e:
                    print(f"❌ {test_name}测试失败: {e}")
                    test_results['failed'] += 1

        except Exception as e:
            print(f"⚠️  无法创建模型，跳过GPU测试: {e}")
            # 如果模型创建失败，跳过4个GPU测试
            test_results['skipped'] += 4
    else:
        print("⚠️  GPU不可用，跳过GPU相关测试")
        test_results['skipped'] += 4

    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"✅ 通过: {test_results['passed']}")
    print(f"❌ 失败: {test_results['failed']}")
    print(f"⚠️  跳过: {test_results['skipped']}")
    print(f"总计: {sum(test_results.values())}")

    if test_results['failed'] > 0:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False
    else:
        print("\n✅ 所有可运行的测试都通过了！")
        return True


def run_quick_test():
    """运行快速测试（仅基础功能）"""
    print("运行快速集成测试...")

    test_instance = TestMultiGraspIntegration()
    config = load_multi_grasp_config()
    datamodule = MockDataModule()

    try:
        test_instance.test_config_loading(config)
        test_instance.test_data_format_validation(datamodule)
        test_instance.test_data_preprocessing(datamodule, config)
        print("✅ 快速测试通过")
        return True
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='多抓取端到端集成测试')
    parser.add_argument('--quick', action='store_true', help='运行快速测试（仅基础功能）')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.quick:
        success = run_quick_test()
    else:
        success = run_integration_tests()

    exit(0 if success else 1)
