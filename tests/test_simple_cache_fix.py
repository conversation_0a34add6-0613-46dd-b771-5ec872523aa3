#!/usr/bin/env python3
"""
简单验证缓存修复是否有效
"""

import os
import h5py
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cache_validation():
    """测试缓存验证逻辑"""
    from datasets.utils.cache_utils import validate_cache_file
    
    test_cache_path = "/tmp/test_validation_cache.h5"
    
    # 清理可能存在的测试文件
    if os.path.exists(test_cache_path):
        os.remove(test_cache_path)
    
    logger.info("测试1: 空缓存文件验证")
    
    # 创建空缓存文件
    with h5py.File(test_cache_path, 'w') as hf:
        pass  # 空文件
    
    # 验证空缓存
    is_valid = validate_cache_file(test_cache_path, 100)
    logger.info(f"空缓存验证结果: {'有效' if is_valid else '无效'}")
    
    if not is_valid:
        logger.info("✅ 正确识别空缓存为无效")
    else:
        logger.error("❌ 错误地将空缓存识别为有效")
        return False
    
    logger.info("测试2: 不完整缓存文件验证")
    
    # 创建不完整缓存
    with h5py.File(test_cache_path, 'w') as hf:
        for i in range(50):  # 只创建50个，期望100个
            group = hf.create_group(str(i))
            group.create_dataset('data', data=[i])
    
    is_valid = validate_cache_file(test_cache_path, 100)
    logger.info(f"不完整缓存验证结果: {'有效' if is_valid else '无效'}")
    
    if not is_valid:
        logger.info("✅ 正确识别不完整缓存为无效")
    else:
        logger.error("❌ 错误地将不完整缓存识别为有效")
        return False
    
    logger.info("测试3: 完整缓存文件验证")
    
    # 创建完整缓存
    with h5py.File(test_cache_path, 'w') as hf:
        for i in range(100):  # 创建完整的100个
            group = hf.create_group(str(i))
            group.create_dataset('data', data=[i])
    
    is_valid = validate_cache_file(test_cache_path, 100)
    logger.info(f"完整缓存验证结果: {'有效' if is_valid else '无效'}")
    
    if is_valid:
        logger.info("✅ 正确识别完整缓存为有效")
    else:
        logger.error("❌ 错误地将完整缓存识别为无效")
        return False
    
    # 清理测试文件
    if os.path.exists(test_cache_path):
        os.remove(test_cache_path)
    
    return True

def check_code_changes():
    """检查代码修改是否正确应用"""
    logger.info("检查代码修改...")
    
    # 检查 cache_utils.py 的修改
    cache_utils_path = project_root / "datasets" / "utils" / "cache_utils.py"
    
    with open(cache_utils_path, 'r') as f:
        content = f.read()
    
    # 检查关键修改是否存在
    if "Empty cache file detected in distributed training, will populate later" in content:
        logger.info("✅ cache_utils.py 修改已应用")
    else:
        logger.error("❌ cache_utils.py 修改未正确应用")
        return False
    
    # 检查 sceneleapplus_cached.py 的修改
    sceneleap_path = project_root / "datasets" / "sceneleapplus_cached.py"
    
    with open(sceneleap_path, 'r') as f:
        content = f.read()
    
    if "_is_cache_empty" in content and "needs_population" in content:
        logger.info("✅ sceneleapplus_cached.py 修改已应用")
    else:
        logger.error("❌ sceneleapplus_cached.py 修改未正确应用")
        return False
    
    return True

def main():
    """主函数"""
    logger.info("开始简单缓存修复验证...")
    
    # 检查代码修改
    logger.info("=" * 50)
    logger.info("检查代码修改")
    logger.info("=" * 50)
    code_ok = check_code_changes()
    
    # 测试缓存验证
    logger.info("=" * 50)
    logger.info("测试缓存验证逻辑")
    logger.info("=" * 50)
    validation_ok = test_cache_validation()
    
    # 总结
    logger.info("=" * 50)
    logger.info("验证结果总结")
    logger.info("=" * 50)
    
    logger.info(f"代码修改检查: {'✅ 通过' if code_ok else '❌ 失败'}")
    logger.info(f"缓存验证测试: {'✅ 通过' if validation_ok else '❌ 失败'}")
    
    if code_ok and validation_ok:
        logger.info("🎉 缓存修复验证通过！")
        logger.info("")
        logger.info("修复说明:")
        logger.info("1. 修复了分布式训练中立即加载空缓存的问题")
        logger.info("2. 添加了缓存空状态检测逻辑")
        logger.info("3. 确保缓存数据会被正确填充")
        logger.info("")
        logger.info("现在可以安全地重新开始训练！")
        return True
    else:
        logger.error("❌ 验证失败，需要检查修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
