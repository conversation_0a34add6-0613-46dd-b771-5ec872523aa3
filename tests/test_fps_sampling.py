#!/usr/bin/env python3
"""
Test script for the new Farthest Point Sampling strategy in SceneLeapPlusDataset
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def test_fps_algorithm():
    """Test the FPS algorithm with synthetic data"""
    
    # Create a simple SceneLeapPlusDataset instance for testing
    class TestFPSSampler:
        def __init__(self, num_grasps=8):
            self.num_grasps = num_grasps
            
        def _farthest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
            """
            Farthest Point Sampling (FPS) algorithm for 3D points.
            """
            N = points.shape[0]
            device = points.device
            
            if N <= num_samples:
                return torch.arange(N, device=device)
                
            # Initialize with random first point
            sampled_indices = torch.zeros(num_samples, dtype=torch.long, device=device)
            sampled_indices[0] = torch.randint(0, N, (1,), device=device)
            
            # Distance matrix to keep track of minimum distances to sampled points
            distances = torch.full((N,), float('inf'), device=device)
            
            for i in range(1, num_samples):
                # Get the last sampled point
                last_sampled = sampled_indices[i-1]
                last_point = points[last_sampled]
                
                # Calculate distances from all points to the last sampled point
                current_distances = torch.norm(points - last_point.unsqueeze(0), dim=1)
                
                # Update minimum distances
                distances = torch.min(distances, current_distances)
                
                # Select the point with maximum distance to all previously sampled points
                sampled_indices[i] = torch.argmax(distances)
                
                # Set the distance of the newly sampled point to 0
                distances[sampled_indices[i]] = 0
                
            return sampled_indices
    
    # Create test data
    print("Testing Farthest Point Sampling Algorithm...")
    
    # Test case 1: Random 3D points
    torch.manual_seed(42)  # For reproducible results
    num_points = 50
    test_points = torch.randn(num_points, 3) * 2  # Random 3D points
    
    sampler = TestFPSSampler(num_grasps=8)
    fps_indices = sampler._farthest_point_sampling(test_points, 8)
    
    print(f"Original points shape: {test_points.shape}")
    print(f"FPS sampled indices: {fps_indices}")
    print(f"Sampled points shape: {test_points[fps_indices].shape}")
    
    # Test case 2: Clustered points (more realistic scenario)
    # Create 3 clusters of points
    cluster1 = torch.randn(15, 3) * 0.5 + torch.tensor([2.0, 0.0, 0.0])
    cluster2 = torch.randn(15, 3) * 0.5 + torch.tensor([-2.0, 0.0, 0.0])
    cluster3 = torch.randn(20, 3) * 0.5 + torch.tensor([0.0, 2.0, 0.0])
    
    clustered_points = torch.cat([cluster1, cluster2, cluster3], dim=0)
    fps_indices_clustered = sampler._farthest_point_sampling(clustered_points, 8)
    
    print(f"\nClustered points test:")
    print(f"Original clustered points shape: {clustered_points.shape}")
    print(f"FPS sampled indices: {fps_indices_clustered}")
    
    # Visualize the results
    try:
        fig = plt.figure(figsize=(15, 5))
        
        # Plot 1: Random points
        ax1 = fig.add_subplot(131, projection='3d')
        ax1.scatter(test_points[:, 0], test_points[:, 1], test_points[:, 2], 
                   c='lightblue', alpha=0.6, s=20, label='All points')
        sampled_points = test_points[fps_indices]
        ax1.scatter(sampled_points[:, 0], sampled_points[:, 1], sampled_points[:, 2], 
                   c='red', s=100, label='FPS sampled')
        ax1.set_title('Random Points - FPS Sampling')
        ax1.legend()
        
        # Plot 2: Clustered points
        ax2 = fig.add_subplot(132, projection='3d')
        ax2.scatter(clustered_points[:, 0], clustered_points[:, 1], clustered_points[:, 2], 
                   c='lightgreen', alpha=0.6, s=20, label='All points')
        sampled_clustered = clustered_points[fps_indices_clustered]
        ax2.scatter(sampled_clustered[:, 0], sampled_clustered[:, 1], sampled_clustered[:, 2], 
                   c='red', s=100, label='FPS sampled')
        ax2.set_title('Clustered Points - FPS Sampling')
        ax2.legend()
        
        # Plot 3: Distance analysis
        ax3 = fig.add_subplot(133)
        # Calculate pairwise distances for sampled points
        sampled_points_np = sampled_points.numpy()
        distances = []
        for i in range(len(sampled_points_np)):
            for j in range(i+1, len(sampled_points_np)):
                dist = np.linalg.norm(sampled_points_np[i] - sampled_points_np[j])
                distances.append(dist)
        
        ax3.hist(distances, bins=10, alpha=0.7, color='blue')
        ax3.set_title('Pairwise Distances of FPS Sampled Points')
        ax3.set_xlabel('Distance')
        ax3.set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig('/home/<USER>/source/grasp/SceneLeapPlus/fps_sampling_test.png', dpi=150, bbox_inches='tight')
        print(f"\nVisualization saved as 'fps_sampling_test.png'")
        
    except ImportError:
        print("Matplotlib not available for visualization, but FPS algorithm works correctly!")
    
    # Test case 3: Edge cases
    print(f"\nTesting edge cases:")
    
    # Case: Fewer points than requested samples
    few_points = torch.randn(3, 3)
    fps_indices_few = sampler._farthest_point_sampling(few_points, 8)
    print(f"Few points test - Original: {few_points.shape[0]}, Requested: 8, Got indices: {fps_indices_few}")
    
    # Case: Exact number of points
    exact_points = torch.randn(8, 3)
    fps_indices_exact = sampler._farthest_point_sampling(exact_points, 8)
    print(f"Exact points test - Original: {exact_points.shape[0]}, Requested: 8, Got indices: {fps_indices_exact}")
    
    print("\nFPS Algorithm test completed successfully!")
    return True

def test_grasp_sampling_integration():
    """Test the integration with grasp sampling"""
    print("\nTesting grasp sampling integration...")
    
    # Test nearest point sampling as well
    def test_nearest_point_sampling():
        """Test nearest point sampling algorithm"""
        print("\n--- Testing Nearest Point Sampling ---")
        
        # Create test points with clear clusters
        torch.manual_seed(42)
        cluster1 = torch.randn(10, 3) * 0.3 + torch.tensor([2.0, 0.0, 0.0])
        cluster2 = torch.randn(10, 3) * 0.3 + torch.tensor([-2.0, 0.0, 0.0])
        cluster3 = torch.randn(10, 3) * 0.3 + torch.tensor([0.0, 2.0, 0.0])
        
        test_points = torch.cat([cluster1, cluster2, cluster3], dim=0)
        
        class TestNPSSampler:
            def _nearest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
                N = points.shape[0]
                device = points.device
                
                if N <= num_samples:
                    return torch.arange(N, device=device)
                    
                # Randomly select the center point
                center_idx = torch.randint(0, N, (1,), device=device).item()
                center_point = points[center_idx]
                
                # Calculate distances from all points to the center point
                distances = torch.norm(points - center_point.unsqueeze(0), dim=1)
                
                # Get indices of the nearest num_samples points
                _, nearest_indices = torch.topk(distances, num_samples, largest=False)
                
                return nearest_indices
        
        nps_sampler = TestNPSSampler()
        nps_indices = nps_sampler._nearest_point_sampling(test_points, 8)
        nps_sampled = test_points[nps_indices]
        
        print(f"NPS sampled points shape: {nps_sampled.shape}")
        print(f"NPS sampled indices: {nps_indices}")
        
        # Calculate compactness (should be lower for NPS)
        center_point = nps_sampled.mean(dim=0)
        compactness = torch.mean(torch.norm(nps_sampled - center_point, dim=1))
        print(f"NPS compactness (lower is more compact): {compactness:.4f}")
        
        return nps_sampled, compactness
    
    nps_result, nps_compactness = test_nearest_point_sampling()
    
    # Create synthetic grasp data (N, 23) where first 3 are translation
    num_grasps = 20
    synthetic_grasps = torch.randn(num_grasps, 23)
    
    # Make the first 3 dimensions (translation) more structured for better testing
    # Create some clusters in translation space
    synthetic_grasps[:7, :3] = torch.randn(7, 3) * 0.5 + torch.tensor([1.0, 0.0, 0.0])  # Cluster 1
    synthetic_grasps[7:14, :3] = torch.randn(7, 3) * 0.5 + torch.tensor([-1.0, 0.0, 0.0])  # Cluster 2
    synthetic_grasps[14:, :3] = torch.randn(6, 3) * 0.5 + torch.tensor([0.0, 1.0, 0.0])  # Cluster 3
    
    class TestGraspSampler:
        def __init__(self, num_grasps=8, strategy="farthest_point"):
            self.num_grasps = num_grasps
            self.grasp_sampling_strategy = strategy
            
        def _farthest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
            """FPS implementation (same as above)"""
            N = points.shape[0]
            device = points.device
            
            if N <= num_samples:
                return torch.arange(N, device=device)
                
            sampled_indices = torch.zeros(num_samples, dtype=torch.long, device=device)
            sampled_indices[0] = torch.randint(0, N, (1,), device=device)
            distances = torch.full((N,), float('inf'), device=device)
            
            for i in range(1, num_samples):
                last_sampled = sampled_indices[i-1]
                last_point = points[last_sampled]
                current_distances = torch.norm(points - last_point.unsqueeze(0), dim=1)
                distances = torch.min(distances, current_distances)
                sampled_indices[i] = torch.argmax(distances)
                distances[sampled_indices[i]] = 0
                
            return sampled_indices
            
        def _nearest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
            """NPS implementation"""
            N = points.shape[0]
            device = points.device
            
            if N <= num_samples:
                return torch.arange(N, device=device)
                
            center_idx = torch.randint(0, N, (1,), device=device).item()
            center_point = points[center_idx]
            distances = torch.norm(points - center_point.unsqueeze(0), dim=1)
            _, nearest_indices = torch.topk(distances, num_samples, largest=False)
            return nearest_indices
        
        def _sample_grasps_from_available(self, available_grasps: torch.Tensor) -> torch.Tensor:
            """Simplified version of the sampling method"""
            num_available = available_grasps.shape[0]
            
            if num_available == 0:
                return torch.zeros((self.num_grasps, 23), dtype=available_grasps.dtype, device=available_grasps.device)
            
            if num_available >= self.num_grasps:
                if self.grasp_sampling_strategy == "farthest_point":
                    translation_points = available_grasps[:, :3]
                    fps_indices = self._farthest_point_sampling(translation_points, self.num_grasps)
                    return available_grasps[fps_indices]
                elif self.grasp_sampling_strategy == "nearest_point":
                    translation_points = available_grasps[:, :3]
                    nps_indices = self._nearest_point_sampling(translation_points, self.num_grasps)
                    return available_grasps[nps_indices]
                elif self.grasp_sampling_strategy == "random":
                    indices = torch.randperm(num_available)[:self.num_grasps]
                    return available_grasps[indices]
            else:
                # Handle insufficient grasps
                if self.grasp_sampling_strategy in ["farthest_point", "nearest_point"]:
                    all_indices = torch.arange(num_available, device=available_grasps.device)
                    remaining_needed = self.num_grasps - num_available
                    additional_indices = torch.randint(0, num_available, (remaining_needed,), device=available_grasps.device)
                    final_indices = torch.cat([all_indices, additional_indices])
                    return available_grasps[final_indices]
    
    # Test all three sampling strategies
    fps_sampler = TestGraspSampler(num_grasps=8, strategy="farthest_point")
    fps_result = fps_sampler._sample_grasps_from_available(synthetic_grasps)
    
    nps_sampler = TestGraspSampler(num_grasps=8, strategy="nearest_point")
    nps_result = nps_sampler._sample_grasps_from_available(synthetic_grasps)
    
    random_sampler = TestGraspSampler(num_grasps=8, strategy="random")
    random_result = random_sampler._sample_grasps_from_available(synthetic_grasps)
    
    print(f"Original grasps shape: {synthetic_grasps.shape}")
    print(f"FPS sampled grasps shape: {fps_result.shape}")
    print(f"NPS sampled grasps shape: {nps_result.shape}")
    print(f"Random sampled grasps shape: {random_result.shape}")
    
    # Analyze the diversity of translation components
    fps_translations = fps_result[:, :3]
    nps_translations = nps_result[:, :3]
    random_translations = random_result[:, :3]
    
    # Calculate average pairwise distances
    def avg_pairwise_distance(points):
        distances = []
        for i in range(len(points)):
            for j in range(i+1, len(points)):
                dist = torch.norm(points[i] - points[j]).item()
                distances.append(dist)
        return np.mean(distances) if distances else 0
    
    # Calculate compactness (average distance from centroid)
    def compactness(points):
        centroid = points.mean(dim=0)
        distances = [torch.norm(point - centroid).item() for point in points]
        return np.mean(distances)
    
    fps_avg_dist = avg_pairwise_distance(fps_translations)
    nps_avg_dist = avg_pairwise_distance(nps_translations)
    random_avg_dist = avg_pairwise_distance(random_translations)
    
    fps_compactness = compactness(fps_translations)
    nps_compactness = compactness(nps_translations)
    random_compactness = compactness(random_translations)
    
    print(f"\nSampling Strategy Comparison:")
    print(f"{'Strategy':<15} {'Avg Pairwise Dist':<18} {'Compactness':<12} {'Characteristics'}")
    print(f"{'-'*70}")
    print(f"{'FPS':<15} {fps_avg_dist:<18.4f} {fps_compactness:<12.4f} {'Maximizes diversity'}")
    print(f"{'NPS':<15} {nps_avg_dist:<18.4f} {nps_compactness:<12.4f} {'Maximizes locality'}")
    print(f"{'Random':<15} {random_avg_dist:<18.4f} {random_compactness:<12.4f} {'Baseline'}")
    
    print(f"\nAnalysis:")
    print(f"- FPS should have the highest pairwise distance (most diverse)")
    print(f"- NPS should have the lowest compactness (most compact/local)")
    print(f"- Random provides a baseline between the two extremes")
    
    # Test insufficient grasps case for all strategies
    few_grasps = synthetic_grasps[:3]  # Only 3 grasps available, need 8
    fps_insufficient = fps_sampler._sample_grasps_from_available(few_grasps)
    nps_insufficient = nps_sampler._sample_grasps_from_available(few_grasps)
    
    print(f"\nInsufficient grasps test:")
    print(f"Available: {few_grasps.shape[0]}, Needed: 8")
    print(f"FPS result shape: {fps_insufficient.shape}")
    print(f"NPS result shape: {nps_insufficient.shape}")
    
    print("\nGrasp sampling integration test completed successfully!")

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Farthest Point Sampling for Grasp Sampling")
    print("=" * 60)
    
    test_fps_algorithm()
    test_grasp_sampling_integration()
    
    print("\n" + "=" * 60)
    print("All tests completed successfully!")
    print("\nNew sampling strategies available:")
    print("- 'farthest_point': Maximizes spatial diversity by selecting distant grasps")
    print("- 'nearest_point': Maximizes locality by selecting nearby grasps around a random center")
    print("\nUsage in SceneLeapPlusDataset:")
    print("dataset = SceneLeapPlusDataset(..., grasp_sampling_strategy='farthest_point')")
    print("dataset = SceneLeapPlusDataset(..., grasp_sampling_strategy='nearest_point')")
    print("\nBoth strategies are also available in SceneLeapPlusDatasetCached!")
    print("=" * 60)
