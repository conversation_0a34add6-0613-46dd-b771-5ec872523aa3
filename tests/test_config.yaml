# DDPM Lightning 模型对比测试配置文件

# 解码器配置
decoder:
  name: unet
  rot_type: r6d
  d_model: 256  # 减小模型大小以加快测试
  time_embed_mult: 2
  nblocks: 2  # 减少块数
  resblock_dropout: 0.0
  transformer_num_heads: 4  # 减少注意力头数
  transformer_dim_head: 32  # 减小注意力维度
  transformer_dropout: 0.1
  transformer_depth: 1
  transformer_mult_ff: 2
  context_dim: 256
  backbone:
    name: pointnet2
    use_pooling: false
    layer1:
      npoint: 512  # 减少点数
      radius_list: [0.04]
      nsample_list: [32]  # 减少采样数
      mlp_list: [3, 32, 32, 64]  # 减小MLP维度
    layer2:
      npoint: 256
      radius_list: [0.1]
      nsample_list: [16]
      mlp_list: [64, 64, 64, 128]
    layer3:
      npoint: 128
      radius_list: [0.2]
      nsample_list: [16]
      mlp_list: [128, 64, 64, 128]
    layer4:
      npoint: 64
      radius_list: [0.3]
      nsample_list: [8]
      mlp_list: [128, 256, 256]
    use_xyz: true
    normalize_xyz: true
  use_position_embedding: false
  use_text_condition: false
  text_dropout_prob: 0.1

# 损失函数配置
criterion:
  mode: multi_grasp
  device: cuda:0
  rot_type: r6d
  hand_model:
    n_surface_points: 512  # 减少表面点数
    rot_type: r6d
  loss_weights:
    translation: 10.0
    rotation: 10.0
    qpos: 1.0
    neg_loss: 0.5
    hand_chamfer: 0.0
  multi_grasp:
    loss_aggregation: mean
    use_consistency_loss: false  # 简化测试
    consistency_loss_weight: 0.1
    diversity_loss_weight: 0.05
  cost_weights:
    translation: 2.0
    rotation: 2.0
    qpos: 1.0
  scale: 0.1
  q1:
    lambda_torque: 10
    m: 8
    mu: 1
    nms: true
    thres_contact: 0.01
    thres_pen: 0.005
    thres_tpen: 0.01
    rot_type: r6d

# 扩散模型配置
steps: 50  # 减少步数以加快测试
schedule_cfg:
  schedule: linear
  beta_start: 0.0001
  beta_end: 0.02

# 预测配置
pred_x0: true

# CFG 配置
use_cfg: false
guidance_scale: 7.5
use_negative_guidance: false
negative_guidance_scale: 1.0

# 数据配置
rot_type: r6d
mode: multi_grasp
batch_size: 2  # 小批次用于测试
print_freq: 1

# 训练配置
use_score: false
score_pretrain: false
rand_t_type: all

# 损失权重
loss_weights:
  pose_loss: 1.0
  qpos_loss: 1.0
  translation_loss: 1.0
  rotation_loss: 1.0

# 优化器配置
optimizer:
  name: adamw
  lr: 1e-4
  weight_decay: 1e-4

# 学习率调度器配置
scheduler:
  name: cosine
  t_max: 100
  min_lr: 1e-6

# 测试特定配置
test:
  tolerance: 1e-5
  batch_sizes: [1, 2, 4]
  num_grasps_list: [1, 2, 4, 8]
  sampling_configs:
    - k: 1
      use_cfg: false
    - k: 2
      use_cfg: false
    - k: 1
      use_cfg: true
      guidance_scale: 7.5
  random_seeds: [42, 123, 456, 789, 999]
