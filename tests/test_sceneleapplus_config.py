#!/usr/bin/env python3
"""
Test script for SceneLeapPlus configuration file validation.
"""

import os
import sys
import yaml
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """Test loading the SceneLeapPlus configuration file."""
    config_path = project_root / "config" / "data" / "sceneleapplus.yaml"
    
    print(f"Testing config file: {config_path}")
    
    # Check if file exists
    if not config_path.exists():
        print(f"❌ Config file does not exist: {config_path}")
        return False
    
    # Load YAML
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        print("✅ Successfully loaded YAML config")
    except Exception as e:
        print(f"❌ Failed to load YAML: {e}")
        return False
    
    # Validate structure
    required_sections = ['train', 'val', 'test']
    for section in required_sections:
        if section not in config:
            print(f"❌ Missing required section: {section}")
            return False
        print(f"✅ Found section: {section}")
    
    # Validate SceneLeapPlus specific parameters
    sceneleapplus_params = ['num_grasps', 'grasp_sampling_strategy', 'cache_mode']
    for section in required_sections:
        section_config = config[section]
        for param in sceneleapplus_params:
            if param not in section_config:
                print(f"❌ Missing SceneLeapPlus parameter '{param}' in section '{section}'")
                return False
            print(f"✅ Found parameter '{param}' in section '{section}': {section_config[param]}")
    
    # Validate common parameters
    common_params = [
        'root_dir', 'succ_grasp_dir', 'obj_root_dir', 'mode', 
        'max_grasps_per_object', 'mesh_scale', 'num_neg_prompts',
        'enable_cropping', 'max_points', 'cache_version', 'batch_size', 'num_workers'
    ]
    for section in required_sections:
        section_config = config[section]
        for param in common_params:
            if param not in section_config:
                print(f"❌ Missing common parameter '{param}' in section '{section}'")
                return False
    
    print("✅ All common parameters found in all sections")
    
    # Validate parameter values
    for section in required_sections:
        section_config = config[section]
        
        # Check num_grasps is positive integer
        if not isinstance(section_config['num_grasps'], int) or section_config['num_grasps'] <= 0:
            print(f"❌ Invalid num_grasps in section '{section}': {section_config['num_grasps']}")
            return False
        
        # Check grasp_sampling_strategy is valid
        valid_strategies = ['random', 'first_n', 'repeat']
        if section_config['grasp_sampling_strategy'] not in valid_strategies:
            print(f"❌ Invalid grasp_sampling_strategy in section '{section}': {section_config['grasp_sampling_strategy']}")
            return False
        
        # Check cache_mode matches section
        expected_cache_mode = section
        if section_config['cache_mode'] != expected_cache_mode:
            print(f"❌ Cache mode mismatch in section '{section}': expected '{expected_cache_mode}', got '{section_config['cache_mode']}'")
            return False
    
    print("✅ All parameter values are valid")
    
    # Print summary
    print("\n📋 Configuration Summary:")
    print(f"  Name: {config.get('name', 'N/A')}")
    print(f"  Mode: {config.get('mode', 'N/A')}")
    
    for section in required_sections:
        section_config = config[section]
        print(f"\n  {section.upper()}:")
        print(f"    num_grasps: {section_config['num_grasps']}")
        print(f"    max_grasps_per_object: {section_config['max_grasps_per_object']}")
        print(f"    grasp_sampling_strategy: {section_config['grasp_sampling_strategy']}")
        print(f"    cache_version: {section_config['cache_version']}")
        print(f"    cache_mode: {section_config['cache_mode']}")
    
    return True

def test_config_comparison():
    """Compare with original sceneleappro config to ensure consistency."""
    sceneleappro_path = project_root / "config" / "data" / "sceneleappro.yaml"
    sceneleapplus_path = project_root / "config" / "data" / "sceneleapplus.yaml"
    
    if not sceneleappro_path.exists():
        print("⚠️  Original sceneleappro.yaml not found, skipping comparison")
        return True
    
    try:
        with open(sceneleappro_path, 'r') as f:
            pro_config = yaml.safe_load(f)
        with open(sceneleapplus_path, 'r') as f:
            plus_config = yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Failed to load configs for comparison: {e}")
        return False
    
    print("\n🔍 Comparing with sceneleappro config:")
    
    # Compare common parameters
    common_params = ['mesh_scale', 'num_neg_prompts', 'enable_cropping', 'max_points']
    
    for section in ['train', 'val', 'test']:
        if section in pro_config and section in plus_config:
            pro_section = pro_config[section]
            plus_section = plus_config[section]
            
            for param in common_params:
                if param in pro_section and param in plus_section:
                    if pro_section[param] != plus_section[param]:
                        print(f"⚠️  Parameter '{param}' differs in section '{section}': pro={pro_section[param]}, plus={plus_section[param]}")
                    else:
                        print(f"✅ Parameter '{param}' matches in section '{section}'")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing SceneLeapPlus Configuration")
    print("=" * 50)
    
    success = True
    
    # Test config loading
    if not test_config_loading():
        success = False
    
    print("\n" + "=" * 50)
    
    # Test config comparison
    if not test_config_comparison():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 All tests passed! Configuration is valid.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the configuration.")
        sys.exit(1)
