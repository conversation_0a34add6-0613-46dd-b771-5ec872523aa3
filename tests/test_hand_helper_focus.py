#!/usr/bin/env python3
"""
Focused tests for hand_helper.py normalization range issues
"""

import sys
import os
import torch
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    normalize_rot_torch, denormalize_rot_torch,
    NORM_UPPER, NORM_LOWER
)

def test_normalization_ranges_detailed():
    """Detailed test of normalization ranges to identify the issue"""
    print("=== Detailed normalization range test ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # Create test data with known ranges
    test_trans = torch.tensor([[0.1, 0.2, 0.3], [-0.1, -0.2, -0.3]], device=device, dtype=torch.float32)
    test_param = torch.tensor([[0.1]*16, [-0.1]*16], device=device, dtype=torch.float32)
    test_rot_r6d = torch.tensor([[1.0, 0.0, 0.0, 0.0, 1.0, 0.0]], device=device, dtype=torch.float32)
    
    print(f"Input translation: {test_trans}")
    print(f"Input parameters: {test_param[0][:3]}...")  # Show first 3 values
    print(f"Input r6d rotation: {test_rot_r6d}")
    
    try:
        # Test translation normalization
        norm_trans = normalize_trans_torch(test_trans, mode)
        print(f"Normalized translation: {norm_trans}")
        print(f"Translation range: [{norm_trans.min().item():.4f}, {norm_trans.max().item():.4f}]")
        trans_in_range = (norm_trans.min() >= NORM_LOWER) and (norm_trans.max() <= NORM_UPPER)
        print(f"Translation in expected range [{NORM_LOWER}, {NORM_UPPER}]: {trans_in_range}")
        
        # Test parameter normalization
        norm_param = normalize_param_torch(test_param, mode)
        print(f"Normalized parameters (first 3 values): {norm_param[0][:3]}")
        print(f"Parameter range: [{norm_param.min().item():.4f}, {norm_param.max().item():.4f}]")
        param_in_range = (norm_param.min() >= NORM_LOWER) and (norm_param.max() <= NORM_UPPER)
        print(f"Parameters in expected range [{NORM_LOWER}, {NORM_UPPER}]: {param_in_range}")
        
        # Test r6d rotation normalization
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d, 'r6d', mode)
        print(f"Normalized r6d rotation: {norm_rot_r6d}")
        print(f"R6D rotation range: [{norm_rot_r6d.min().item():.4f}, {norm_rot_r6d.max().item():.4f}]")
        r6d_in_range = (norm_rot_r6d.min() >= NORM_LOWER) and (norm_rot_r6d.max() <= NORM_UPPER)
        print(f"R6D rotation in expected range [{NORM_LOWER}, {NORM_UPPER}]: {r6d_in_range}")
        
        return trans_in_range and param_in_range and r6d_in_range
        
    except Exception as e:
        print(f"Detailed normalization range test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run focused tests"""
    print("Starting focused tests for hand_helper.py normalization...")
    
    success = test_normalization_ranges_detailed()
    
    if success:
        print("\n🎉 All focused tests passed!")
    else:
        print("\n⚠️  Some focused tests failed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)