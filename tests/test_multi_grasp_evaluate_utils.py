#!/usr/bin/env python3
"""
Comprehensive test script for multi-grasp evaluate_utils.py refactoring.

Tests both single and multi-grasp formats for:
- cal_q1 function
- cal_pen function  
- cal_distance_to_mesh function
- GraspLossPose._calculate_metrics method

Usage:
    python tests/test_multi_grasp_evaluate_utils.py
"""

import sys
import os
import time
import numpy as np
import torch
import trimesh as tm
from typing import Dict, Tuple

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.evaluate_utils import cal_q1, cal_pen, cal_distance_to_mesh
from utils.hand_model import HandModel, HandModelType
from models.loss.grasp_loss_pose import GraspLossPose


class TestMultiGraspEvaluateUtils:
    """Test suite for multi-grasp evaluate_utils refactoring"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Initialize hand model
        self.hand_model = HandModel(
            hand_model_type=HandModelType.LEAP,
            n_surface_points=100,
            rot_type='r6d',
            device=self.device
        )
        
        # Create test configuration
        self.cfg = {
            'rot_type': 'r6d',
            'nms': False,
            'thres_contact': 0.01,
            'thres_pen': 0.005,
            'm': 8,
            'mu': 0.7,
            'lambda_torque': 1.0
        }
        
        # Generate test data
        self.test_data = self._generate_test_data()
        
    def _generate_test_data(self) -> Dict:
        """Generate synthetic test data"""
        print("Generating test data...")
        
        # Create a simple box mesh for testing
        box = tm.primitives.Box(extents=[0.1, 0.1, 0.1])
        vertices = torch.tensor(box.vertices, dtype=torch.float32, device=self.device)
        faces = torch.tensor(box.faces, dtype=torch.long, device=self.device)
        
        # Generate test hand poses
        batch_size = 4
        num_grasps = 6
        pose_dim = 3 + 16 + 6  # translation + joints + r6d rotation
        
        # Single grasp format: [B, pose_dim]
        single_grasp_poses = torch.randn(batch_size, pose_dim, device=self.device) * 0.1
        
        # Multi grasp format: [B, num_grasps, pose_dim]  
        multi_grasp_poses = torch.randn(batch_size, num_grasps, pose_dim, device=self.device) * 0.1
        
        # Ensure valid joint angles (within reasonable bounds)
        single_grasp_poses[:, 3:19] = torch.clamp(single_grasp_poses[:, 3:19], -1.0, 1.0)
        multi_grasp_poses[:, :, 3:19] = torch.clamp(multi_grasp_poses[:, :, 3:19], -1.0, 1.0)
        
        return {
            'vertices': vertices,
            'faces': faces,
            'single_grasp_poses': single_grasp_poses,
            'multi_grasp_poses': multi_grasp_poses,
            'batch_size': batch_size,
            'num_grasps': num_grasps,
            'pose_dim': pose_dim
        }
    
    def test_cal_distance_to_mesh(self):
        """Test cal_distance_to_mesh function"""
        print("\n=== Testing cal_distance_to_mesh ===")
        
        # Test with different query point formats
        B, N = 2, 100
        query_points = torch.randn(B, N, 3, device=self.device) * 0.05
        
        try:
            distances, normals = cal_distance_to_mesh(
                query_points, 
                self.test_data['vertices'], 
                self.test_data['faces']
            )
            
            assert distances.shape == (B, N), f"Expected shape ({B}, {N}), got {distances.shape}"
            assert normals.shape == (B, N, 3), f"Expected shape ({B}, {N}, 3), got {normals.shape}"
            
            # Test with closest points
            distances, normals, closest_points = cal_distance_to_mesh(
                query_points, 
                self.test_data['vertices'], 
                self.test_data['faces'],
                with_closest_points=True
            )
            
            assert closest_points.shape == (B, N, 3), f"Expected shape ({B}, {N}, 3), got {closest_points.shape}"
            
            print("✓ cal_distance_to_mesh tests passed")
            
        except Exception as e:
            print(f"✗ cal_distance_to_mesh test failed: {e}")
            return False
            
        return True
    
    def test_cal_q1_single_grasp(self):
        """Test cal_q1 function with single grasp format"""
        print("\n=== Testing cal_q1 (Single Grasp) ===")
        
        try:
            single_poses = self.test_data['single_grasp_poses']
            B = single_poses.shape[0]
            
            # Test batch processing
            start_time = time.time()
            q1_results = cal_q1(
                self.cfg, 
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                single_poses
            )
            batch_time = time.time() - start_time
            
            assert torch.is_tensor(q1_results), "Expected tensor output"
            assert q1_results.shape == (B,), f"Expected shape ({B},), got {q1_results.shape}"
            assert torch.all(q1_results >= 0), "Q1 values should be non-negative"
            
            # Test individual processing for comparison
            start_time = time.time()
            individual_results = []
            for i in range(B):
                q1_val = cal_q1(
                    self.cfg,
                    self.hand_model,
                    self.test_data['vertices'],
                    self.test_data['faces'],
                    0.1,
                    single_poses[i:i+1]
                )
                individual_results.append(q1_val.item() if torch.is_tensor(q1_val) else q1_val)
            individual_time = time.time() - start_time
            
            # Compare results
            individual_tensor = torch.tensor(individual_results, device=self.device)
            max_diff = torch.max(torch.abs(q1_results - individual_tensor)).item()
            
            print(f"✓ Single grasp Q1 tests passed")
            print(f"  Batch time: {batch_time:.4f}s, Individual time: {individual_time:.4f}s")
            print(f"  Speedup: {individual_time/batch_time:.2f}x")
            print(f"  Max difference: {max_diff:.6f}")
            
        except Exception as e:
            print(f"✗ Single grasp Q1 test failed: {e}")
            return False
            
        return True
    
    def test_cal_q1_multi_grasp(self):
        """Test cal_q1 function with multi-grasp format"""
        print("\n=== Testing cal_q1 (Multi Grasp) ===")
        
        try:
            multi_poses = self.test_data['multi_grasp_poses']
            B, num_grasps = multi_poses.shape[:2]
            
            # Test batch processing
            start_time = time.time()
            q1_results = cal_q1(
                self.cfg,
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                multi_poses
            )
            batch_time = time.time() - start_time
            
            assert torch.is_tensor(q1_results), "Expected tensor output"
            assert q1_results.shape == (B, num_grasps), f"Expected shape ({B}, {num_grasps}), got {q1_results.shape}"
            assert torch.all(q1_results >= 0), "Q1 values should be non-negative"
            
            # Test individual processing for comparison
            start_time = time.time()
            individual_results = []
            for i in range(B):
                for j in range(num_grasps):
                    q1_val = cal_q1(
                        self.cfg,
                        self.hand_model,
                        self.test_data['vertices'],
                        self.test_data['faces'],
                        0.1,
                        multi_poses[i, j:j+1]
                    )
                    individual_results.append(q1_val.item() if torch.is_tensor(q1_val) else q1_val)
            individual_time = time.time() - start_time
            
            # Compare results
            individual_tensor = torch.tensor(individual_results, device=self.device).view(B, num_grasps)
            max_diff = torch.max(torch.abs(q1_results - individual_tensor)).item()
            
            print(f"✓ Multi grasp Q1 tests passed")
            print(f"  Batch time: {batch_time:.4f}s, Individual time: {individual_time:.4f}s")
            print(f"  Speedup: {individual_time/batch_time:.2f}x")
            print(f"  Max difference: {max_diff:.6f}")
            
        except Exception as e:
            print(f"✗ Multi grasp Q1 test failed: {e}")
            return False
            
        return True
    
    def test_cal_pen_single_grasp(self):
        """Test cal_pen function with single grasp format"""
        print("\n=== Testing cal_pen (Single Grasp) ===")
        
        try:
            single_poses = self.test_data['single_grasp_poses']
            B = single_poses.shape[0]
            
            # Test batch processing
            start_time = time.time()
            pen_results = cal_pen(
                self.cfg,
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                single_poses
            )
            batch_time = time.time() - start_time
            
            assert torch.is_tensor(pen_results), "Expected tensor output"
            assert pen_results.shape == (B,), f"Expected shape ({B},), got {pen_results.shape}"
            assert torch.all(pen_results >= 0), "Penetration values should be non-negative"
            
            print(f"✓ Single grasp penetration tests passed")
            print(f"  Batch time: {batch_time:.4f}s")
            
        except Exception as e:
            print(f"✗ Single grasp penetration test failed: {e}")
            return False
            
        return True
    
    def test_cal_pen_multi_grasp(self):
        """Test cal_pen function with multi-grasp format"""
        print("\n=== Testing cal_pen (Multi Grasp) ===")
        
        try:
            multi_poses = self.test_data['multi_grasp_poses']
            B, num_grasps = multi_poses.shape[:2]
            
            # Test batch processing
            start_time = time.time()
            pen_results = cal_pen(
                self.cfg,
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                multi_poses
            )
            batch_time = time.time() - start_time
            
            assert torch.is_tensor(pen_results), "Expected tensor output"
            assert pen_results.shape == (B, num_grasps), f"Expected shape ({B}, {num_grasps}), got {pen_results.shape}"
            assert torch.all(pen_results >= 0), "Penetration values should be non-negative"
            
            print(f"✓ Multi grasp penetration tests passed")
            print(f"  Batch time: {batch_time:.4f}s")
            
        except Exception as e:
            print(f"✗ Multi grasp penetration test failed: {e}")
            return False

        return True

    def test_backward_compatibility(self):
        """Test backward compatibility with original single-sample format"""
        print("\n=== Testing Backward Compatibility ===")

        try:
            # Test 1D input (single sample)
            single_pose_1d = self.test_data['single_grasp_poses'][0]  # [pose_dim]

            q1_result = cal_q1(
                self.cfg,
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                single_pose_1d
            )

            pen_result = cal_pen(
                self.cfg,
                self.hand_model,
                self.test_data['vertices'],
                self.test_data['faces'],
                0.1,
                single_pose_1d
            )

            # Results should be scalars for 1D input
            assert isinstance(q1_result, (float, int)), f"Expected scalar, got {type(q1_result)}"
            assert isinstance(pen_result, (float, int)), f"Expected scalar, got {type(pen_result)}"

            print("✓ Backward compatibility tests passed")

        except Exception as e:
            print(f"✗ Backward compatibility test failed: {e}")
            return False

        return True

    def test_grasp_loss_pose_integration(self):
        """Test integration with GraspLossPose._calculate_metrics"""
        print("\n=== Testing GraspLossPose Integration ===")

        try:
            # Create mock loss configuration
            from types import SimpleNamespace

            loss_cfg = SimpleNamespace()
            loss_cfg.device = self.device
            loss_cfg.hand_model = SimpleNamespace()
            loss_cfg.hand_model.n_surface_points = 100
            loss_cfg.rot_type = 'r6d'
            loss_cfg.loss_weights = {'para': 1.0}
            loss_cfg.cost_weights = {'para': 1.0}
            loss_cfg.q1 = self.cfg
            loss_cfg.mode = 'camera_centric'

            # Initialize GraspLossPose
            grasp_loss = GraspLossPose(loss_cfg)

            # Test single grasp format
            B = self.test_data['batch_size']
            single_batch = {
                'matched': {
                    'hand_model_pose': self.test_data['single_grasp_poses']
                },
                'obj_code': [f'obj_{i}' for i in range(B)],
                'scene_id': [f'scene_{i}' for i in range(B)],
                'category_id_from_object_index': [0] * B,
                'depth_view_index': [0] * B
            }

            single_pred_dict = {
                'matched': {
                    'obj_verts': [self.test_data['vertices']] * B,
                    'obj_faces': [self.test_data['faces']] * B
                }
            }

            single_metrics, single_details = grasp_loss._calculate_metrics(single_pred_dict, single_batch)

            # Test multi grasp format
            multi_batch = {
                'matched': {
                    'hand_model_pose': self.test_data['multi_grasp_poses']
                },
                'obj_code': [f'obj_{i}' for i in range(B)],
                'scene_id': [f'scene_{i}' for i in range(B)],
                'category_id_from_object_index': [0] * B,
                'depth_view_index': [0] * B
            }

            multi_pred_dict = {
                'matched': {
                    'obj_verts': [self.test_data['vertices']] * B,
                    'obj_faces': [self.test_data['faces']] * B
                }
            }

            multi_metrics, multi_details = grasp_loss._calculate_metrics(multi_pred_dict, multi_batch)

            # Verify metrics structure
            expected_single_keys = {'mean_q1', 'mean_pen', 'max_pen', 'mean_valid_q1'}
            expected_multi_keys = expected_single_keys | {
                'std_q1', 'std_pen', 'min_q1', 'max_q1', 'min_pen',
                'best_mean_q1', 'best_mean_pen', 'best_max_pen', 'best_mean_valid_q1',
                'success_rate', 'best_success_rate'
            }

            assert set(single_metrics.keys()) == expected_single_keys, f"Missing single grasp metrics: {expected_single_keys - set(single_metrics.keys())}"
            assert set(multi_metrics.keys()) == expected_multi_keys, f"Missing multi grasp metrics: {expected_multi_keys - set(multi_metrics.keys())}"

            print("✓ GraspLossPose integration tests passed")
            print(f"  Single grasp metrics: {len(single_metrics)} keys")
            print(f"  Multi grasp metrics: {len(multi_metrics)} keys")
            print(f"  Single grasp details: {len(single_details)} entries")
            print(f"  Multi grasp details: {len(multi_details)} entries")

        except Exception as e:
            print(f"✗ GraspLossPose integration test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        return True

    def run_all_tests(self):
        """Run all test cases"""
        print("=" * 60)
        print("MULTI-GRASP EVALUATE_UTILS TEST SUITE")
        print("=" * 60)

        test_methods = [
            self.test_cal_distance_to_mesh,
            self.test_cal_q1_single_grasp,
            self.test_cal_q1_multi_grasp,
            self.test_cal_pen_single_grasp,
            self.test_cal_pen_multi_grasp,
            self.test_backward_compatibility,
            self.test_grasp_loss_pose_integration
        ]

        passed = 0
        total = len(test_methods)

        for test_method in test_methods:
            if test_method():
                passed += 1

        print("\n" + "=" * 60)
        print(f"TEST RESULTS: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 ALL TESTS PASSED! Multi-grasp refactoring is working correctly.")
        else:
            print(f"❌ {total - passed} tests failed. Please check the implementation.")

        print("=" * 60)

        return passed == total


def main():
    """Main test execution"""
    tester = TestMultiGraspEvaluateUtils()
    success = tester.run_all_tests()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
