#!/usr/bin/env python3
"""
测试当前的归一化和反归一化是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    NORM_UPPER, NORM_LOWER
)

def test_round_trip_normalization():
    """测试归一化和反归一化的往返是否正确"""
    print("=== 测试往返归一化 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 创建一些测试数据
    test_data = torch.tensor([
        [-0.1, 0.0, -0.05],  # 一些合理的平移值
        [0.05, -0.02, -0.08],
        [0.0, 0.0, -0.09],
        [0.1, 0.1, -0.06]
    ], device=device, dtype=torch.float32)
    
    print(f"原始数据:")
    print(test_data)
    print()
    
    try:
        # 归一化
        normalized = normalize_trans_torch(test_data, mode)
        print(f"归一化后:")
        print(normalized)
        print(f"归一化范围: [{normalized.min().item():.4f}, {normalized.max().item():.4f}]")
        print(f"期望范围: [{NORM_LOWER}, {NORM_UPPER}]")
        print()
        
        # 反归一化
        denormalized = denormalize_trans_torch(normalized, mode)
        print(f"反归一化后:")
        print(denormalized)
        print()
        
        # 计算误差
        error = torch.abs(test_data - denormalized).max().item()
        print(f"往返误差: {error:.8f}")
        
        # 检查是否在合理范围内
        if error < 1e-5:
            print("✅ 往返测试通过")
            return True
        else:
            print("❌ 往返测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_normalization_range():
    """测试归一化后的值是否在正确范围内"""
    print("\n=== 测试归一化范围 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 创建更大范围的测试数据
    test_data = torch.randn(100, 3, device=device, dtype=torch.float32) * 0.2
    
    try:
        normalized = normalize_trans_torch(test_data, mode)
        
        min_val = normalized.min().item()
        max_val = normalized.max().item()
        
        print(f"归一化后范围: [{min_val:.4f}, {max_val:.4f}]")
        print(f"期望范围: [{NORM_LOWER}, {NORM_UPPER}]")
        
        # 检查是否大致在期望范围内（允许一些统计误差）
        range_ok = (min_val >= NORM_LOWER - 0.1) and (max_val <= NORM_UPPER + 0.1)
        
        if range_ok:
            print("✅ 范围测试通过")
            return True
        else:
            print("❌ 范围测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def analyze_formulas():
    """分析当前使用的公式"""
    print("\n=== 分析当前公式 ===")
    
    print("归一化公式（normalize_trans_torch第139行）:")
    print("t_final_normalized = t_scaled_0_1 * (NORM_UPPER - NORM_LOWER) + NORM_LOWER")
    print("其中 t_scaled_0_1 = (hand_t - t_min) / (t_max - t_min)")
    print()
    
    print("反归一化公式（denormalize_trans_torch第155-157行）:")
    print("t = hand_t + ((NORM_UPPER - NORM_LOWER) / 2.0)")
    print("t /= (NORM_UPPER - NORM_LOWER)")
    print("t = t * t_range + t_min")
    print()
    
    print("让我们验证这两个公式是否互为逆操作...")
    
    # 数学验证
    print("设归一化后的值为 norm_val，原始统计为 [t_min, t_max]")
    print()
    print("反归一化步骤:")
    print("1. t = norm_val + 1.0  (因为 (NORM_UPPER - NORM_LOWER) / 2 = 1)")
    print("2. t = (norm_val + 1.0) / 2.0")
    print("3. t = ((norm_val + 1.0) / 2.0) * (t_max - t_min) + t_min")
    print()
    print("现在验证这是否是归一化的逆操作...")
    print("归一化: norm_val = ((orig - t_min) / (t_max - t_min)) * 2 - 1")
    print("设 scaled = (orig - t_min) / (t_max - t_min)，则 norm_val = 2*scaled - 1")
    print("所以 scaled = (norm_val + 1) / 2")
    print("因此 orig = scaled * (t_max - t_min) + t_min = ((norm_val + 1) / 2) * (t_max - t_min) + t_min")
    print()
    print("这正好匹配反归一化公式！所以公式是正确的。")

def main():
    """主函数"""
    print("测试当前的归一化实现...")
    
    test1 = test_round_trip_normalization()
    test2 = test_normalization_range()
    analyze_formulas()
    
    print(f"\n=== 总结 ===")
    if test1 and test2:
        print("🎉 当前的归一化实现是正确的！")
        print("我之前的分析可能有误解。")
    else:
        print("⚠️ 当前的归一化实现存在问题。")
    
    return test1 and test2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
