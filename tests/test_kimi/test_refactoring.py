#!/usr/bin/env python3
"""
重构验证脚本

验证重构后的代码保持向后兼容性，并测试核心功能
"""

import torch
import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from models.diffuser_lightning import DDPMLightning
        from models.diffusion import DDPMModel
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_class_initialization():
    """测试类初始化"""
    print("测试类初始化...")
    
    try:
        from models.diffuser_lightning import DDPMLightning
        from models.diffusion.ddpm_model import DDPMModel
        
        # 创建模拟配置
        class MockCfg:
            def __init__(self):
                self.steps = 100
                self.schedule_cfg = {}
                self.decoder = type('Decoder', (), {})()
                self.criterion = type('Criterion', (), {})()
                self.criterion.loss_weights = {'total_loss': 1.0}
                self.rot_type = 'axis_angle'
                self.batch_size = 32
                self.mode = 'train'
                self.print_freq = 100
                self.optimizer = type('Optimizer', (), {})()
                self.optimizer.name = 'adam'
                self.optimizer.lr = 1e-4
                self.optimizer.weight_decay = 1e-4
                self.scheduler = type('Scheduler', (), {})()
                self.scheduler.name = 'cosine'
                self.scheduler.t_max = 100
                self.scheduler.min_lr = 1e-6
                self.pred_x0 = False
                self.rand_t_type = 'all'
        
        cfg = MockCfg()
        
        # 测试DDPMLightning初始化
        lightning_model = DDPMLightning(cfg)
        print("✓ DDPMLightning初始化成功")
        
        # 测试DDPMModel初始化
        mock_decoder = torch.nn.Linear(10, 10)
        ddpm_model = DDPMModel(
            eps_model=mock_decoder,
            timesteps=100,
            schedule_cfg={},
            pred_x0=False
        )
        print("✓ DDPMModel初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 类初始化失败: {e}")
        return False

def test_core_methods():
    """测试核心方法"""
    print("测试核心方法...")
    
    try:
        from models.diffusion.ddpm_model import DDPMModel
        
        # 创建测试数据
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        mock_decoder = torch.nn.Linear(10, 10).to(device)
        
        ddpm_model = DDPMModel(
            eps_model=mock_decoder,
            timesteps=10,
            schedule_cfg={},
            pred_x0=False
        ).to(device)
        
        # 测试q_sample
        x0 = torch.randn(2, 10, device=device)
        t = torch.randint(0, 10, (2,), device=device)
        noise = torch.randn(2, 10, device=device)
        
        x_t = ddpm_model.q_sample(x0, t, noise)
        assert x_t.shape == x0.shape, f"q_sample输出形状错误: {x_t.shape} != {x0.shape}"
        print("✓ q_sample测试通过")
        
        # 测试model_predict
        data = {'test': torch.randn(2, 10, device=device)}
        pred_noise, pred_x0 = ddpm_model.model_predict(x_t, t, data)
        assert pred_noise.shape == x_t.shape, f"model_predict输出形状错误: {pred_noise.shape} != {x_t.shape}"
        print("✓ model_predict测试通过")
        
        # 测试p_mean_variance
        model_mean, var, log_var = ddpm_model.p_mean_variance(x_t, t, data)
        assert model_mean.shape == x_t.shape, f"p_mean_variance输出形状错误: {model_mean.shape} != {x_t.shape}"
        print("✓ p_mean_variance测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_grasp_format():
    """测试多抓取格式"""
    print("测试多抓取格式...")
    
    try:
        from models.diffusion.ddpm_model import DDPMModel
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        mock_decoder = torch.nn.Linear(10, 10).to(device)
        
        ddpm_model = DDPMModel(
            eps_model=mock_decoder,
            timesteps=10,
            schedule_cfg={},
            pred_x0=False
        ).to(device)
        
        # 多抓取格式测试
        batch_size = 2
        num_grasps = 5
        pose_dim = 10
        
        x0 = torch.randn(batch_size, num_grasps, pose_dim, device=device)
        t = torch.randint(0, 10, (batch_size,), device=device)
        noise = torch.randn(batch_size, num_grasps, pose_dim, device=device)
        
        # 测试q_sample
        x_t = ddpm_model.q_sample(x0, t, noise)
        assert x_t.shape == x0.shape, f"多抓取q_sample形状错误: {x_t.shape} != {x0.shape}"
        print("✓ 多抓取q_sample测试通过")
        
        # 测试model_predict
        data = {'test': torch.randn(batch_size, num_grasps, pose_dim, device=device)}
        pred_noise, pred_x0 = ddpm_model.model_predict(x_t, t, data)
        assert pred_noise.shape == x_t.shape, f"多抓取model_predict形状错误: {pred_noise.shape} != {x_t.shape}"
        print("✓ 多抓取model_predict测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 多抓取格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("DDPM 重构验证测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_class_initialization,
        test_core_methods,
        test_multi_grasp_format,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append(False)
            print()
    
    print("=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    test_names = ["模块导入", "类初始化", "核心方法", "多抓取格式"]
    for name, result in zip(test_names, results):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查重构代码")
        return 1

if __name__ == '__main__':
    sys.exit(main())