#!/usr/bin/env python3
"""
结构验证脚本 - 无需依赖库的验证
"""

import os
import sys

def validate_directory_structure():
    """验证目录结构"""
    print("验证目录结构...")
    
    expected_dirs = [
        'models/diffusion',
        'models/lightning',
    ]
    
    expected_files = [
        'models/diffuser_lightning.py',
        'models/diffusion/__init__.py',
        'models/diffusion/ddpm_model.py',
        'models/diffusion/sampling.py',
        'models/lightning/__init__.py',
        'models/lightning/ddpm_lightning.py',
    ]
    
    missing_dirs = []
    missing_files = []
    
    for dir_path in expected_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    for file_path in expected_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_dirs:
        print(f"✗ 缺失目录: {missing_dirs}")
        return False
    
    if missing_files:
        print(f"✗ 缺失文件: {missing_files}")
        return False
    
    print("✓ 所有目录和文件结构正确")
    return True

def validate_imports():
    """验证Python文件语法和导入"""
    print("验证文件语法...")
    
    files_to_check = [
        'models/diffusion/__init__.py',
        'models/lightning/__init__.py',
        'models/diffuser_lightning.py',
    ]
    
    import_errors = []
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # 简单语法检查
            compile(content, file_path, 'exec')
            print(f"✓ {file_path} 语法正确")
            
        except SyntaxError as e:
            import_errors.append(f"{file_path}: {e}")
            print(f"✗ {file_path} 语法错误: {e}")
        except Exception as e:
            import_errors.append(f"{file_path}: {e}")
            print(f"✗ {file_path} 读取错误: {e}")
    
    return len(import_errors) == 0

def validate_file_sizes():
    """验证文件大小是否合理"""
    print("验证文件大小...")
    
    files_to_check = [
        'models/diffusion/ddpm_model.py',
        'models/lightning/ddpm_lightning.py',
    ]
    
    large_files = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            lines = len(open(file_path).readlines())
            
            if lines > 300:
                large_files.append(f"{file_path}: {lines}行")
                print(f"⚠ {file_path}: {lines}行 ({size}字节)")
            else:
                print(f"✓ {file_path}: {lines}行")
    
    if large_files:
        print(f"⚠ 以下文件可能过大: {large_files}")
    
    return True  # 警告但不失败

def validate_architecture():
    """验证架构完整性"""
    print("验证架构完整性...")
    
    # 检查DDPMModel类定义
    ddpm_model_path = 'models/diffusion/ddpm_model.py'
    if os.path.exists(ddpm_model_path):
        with open(ddpm_model_path, 'r') as f:
            content = f.read()
        
        required_methods = [
            'q_sample',
            'model_predict',
            'p_mean_variance',
            'p_mean_variance_cfg',
            'sample',
            'p_sample',
            'p_sample_loop'
        ]
        
        missing_methods = []
        for method in required_methods:
            if f'def {method}' not in content and method != 'q_sample':
                missing_methods.append(method)
            elif method == 'q_sample' and 'def q_sample' not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"✗ DDPMModel缺失方法: {missing_methods}")
            return False
        
        print("✓ DDPMModel方法完整")
    
    # 检查DDPMLightning类定义
    lightning_path = 'models/lightning/ddpm_lightning.py'
    if os.path.exists(lightning_path):
        with open(lightning_path, 'r') as f:
            content = f.read()
        
        required_methods = [
            'training_step',
            'validation_step',
            'test_step',
            'configure_optimizers'
        ]
        
        missing_methods = []
        for method in required_methods:
            if f'def {method}' not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"✗ DDPMLightning缺失方法: {missing_methods}")
            return False
        
        print("✓ DDPMLightning方法完整")
    
    return True

def main():
    """主验证函数"""
    print("=" * 60)
    print("DDPM 重构结构验证")
    print("=" * 60)
    
    tests = [
        ("目录结构", validate_directory_structure),
        ("文件语法", validate_imports),
        ("文件大小", validate_file_sizes),
        ("架构完整性", validate_architecture),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
            print()
    
    print("=" * 60)
    print("验证结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        if result is None:  # 警告
            status = "⚠ 警告"
            passed += 1
        elif result:
            status = "✓ 通过"
            passed += 1
        else:
            status = "✗ 失败"
        
        print(f"{name:<15} {status}")
    
    print(f"\n总计: {passed}/{total} 验证通过")
    
    if passed == total:
        print("🎉 重构结构验证通过！")
        print("\n重构完成后的架构:")
        print("├── models/")
        print("│   ├── diffuser_lightning.py (向后兼容包装器)")
        print("│   ├── diffusion/")
        print("│   │   ├── __init__.py")
        print("│   │   ├── ddpm_model.py (核心扩散模型)")
        print("│   │   └── sampling.py (采样工具)")
        print("│   └── lightning/")
        print("│       ├── __init__.py")
        print("│       └── ddpm_lightning.py (训练系统)")
        return 0
    else:
        print("❌ 部分验证失败，请检查重构代码")
        return 1

if __name__ == '__main__':
    sys.exit(main())