"""
采样算法工具类
提供统一的采样接口和辅助功能
"""

import torch
from typing import Dict, Optional, Tuple


class SamplingUtils:
    """采样工具类"""
    
    @staticmethod
    def create_sampling_schedule(timesteps: int, device: torch.device) -> torch.Tensor:
        """创建采样时间步调度"""
        return torch.arange(timesteps - 1, -1, -1, device=device)
    
    @staticmethod
    def prepare_noise_schedule(shape: torch.Size, device: torch.device) -> torch.Tensor:
        """准备噪声调度"""
        return torch.randn(shape, device=device)
    
    @staticmethod
    def compute_posterior_params(
        model_mean: torch.Tensor,
        posterior_variance: torch.Tensor,
        posterior_log_variance: torch.Tensor
    ) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """计算后验分布参数"""
        std = torch.exp(0.5 * posterior_log_variance)
        return model_mean, std