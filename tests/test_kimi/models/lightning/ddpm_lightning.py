import pytorch_lightning as pl
import torch
import torch.nn as nn
import logging
from typing import Dict, Any, Optional, List
from statistics import mean

from models.diffusion.ddpm_model import DDPMModel
from models.loss import GraspLossPose
from models.decoder import build_decoder
from utils.hand_helper import process_hand_pose, process_hand_pose_test, denorm_hand_pose_robust
from .utils.log_colors import HEADER, BLUE, GREEN, ENDC


class DDPMLightning(pl.LightningModule):
    """
    DDPMLightning - PyTorch Lightning训练系统
    
    该类专注于训练流程管理，包括：
    - 训练/验证/测试步骤
    - 损失计算和优化
    - 日志记录
    - 检查点管理
    
    不包含扩散算法的具体实现（由DDPMModel负责）
    """
    
    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning model{ENDC}")
        self.save_hyperparameters()
        
        # 核心组件
        self.eps_model = build_decoder(cfg.decoder)
        self.criterion = GraspLossPose(cfg.criterion)
        self.diffusion_model = DDPMModel(
            eps_model=self.eps_model,
            timesteps=cfg.steps,
            schedule_cfg=cfg.schedule_cfg,
            pred_x0=cfg.pred_x0,
            use_cfg=cfg.get('use_cfg', False),
            guidance_scale=cfg.get('guidance_scale', 7.5),
            use_negative_guidance=cfg.get('use_negative_guidance', False),
            negative_guidance_scale=cfg.get('negative_guidance_scale', 1.0)
        )
        
        # 训练配置
        self.loss_weights = cfg.criterion.loss_weights
        self.rot_type = cfg.rot_type
        self.batch_size = cfg.batch_size
        self.print_freq = cfg.print_freq
        self.use_score = cfg.get('use_score', False)
        self.score_pretrain = cfg.get('score_pretrain', False)
        
        self.rand_t_type = cfg.rand_t_type
        self.mode = cfg.mode
        
        # 优化器配置
        self.optimizer_cfg = cfg.optimizer
        self.scheduler = cfg.scheduler
        
        # 验证步骤输出存储
        self.validation_step_outputs = []
        self.metric_results = []
    
    def forward(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> torch.Tensor:
        """前向传播（供推理使用）"""
        return self.eps_model(x_t, t, data)
    
    def _sample_timesteps(self, batch_size: int) -> torch.Tensor:
        """采样时间步"""
        if self.rand_t_type == 'all':
            return torch.randint(0, self.diffusion_model.timesteps, (batch_size,), device=self.device).long()
        elif self.rand_t_type == 'half':
            ts = torch.randint(0, self.diffusion_model.timesteps, ((batch_size + 1) // 2,), device=self.device)
            if batch_size % 2 == 1:
                return torch.cat([ts, self.diffusion_model.timesteps - ts[:-1] - 1], dim=0).long()
            else:
                return torch.cat([ts, self.diffusion_model.timesteps - ts - 1], dim=0).long()
        else:
            raise ValueError(f'Unsupported rand_t_type: {self.rand_t_type}. Expected "all" or "half".')
    
    def _compute_training_loss(self, batch: Dict) -> Dict[str, torch.Tensor]:
        """计算训练损失"""
        B = batch['norm_pose'].shape[0]
        
        # 时间步采样
        ts = self._sample_timesteps(B)
        
        # 噪声生成和加噪
        noise = torch.randn_like(batch['norm_pose'], device=self.device)
        x_t = self.diffusion_model.q_sample(x0=batch['norm_pose'], t=ts, noise=noise)
        
        # 模型前向传播
        output = self.eps_model(x_t, ts, batch)
        
        # 构建预测字典
        if self.diffusion_model.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            pred_x0 = self.diffusion_model._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }
        
        # 添加负向条件（如果存在）
        if 'neg_pred' in batch and batch['neg_pred'] is not None:
            pred_dict['neg_pred'] = batch['neg_pred']
            pred_dict['neg_text_features'] = batch['neg_text_features']
        
        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='train')
        
        return loss_dict
    
    def training_step(self, batch, batch_idx):
        """训练步骤"""
        # 数据预处理（支持多抓取）
        batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 条件编码
        condition_dict = self.eps_model.condition(batch)
        batch.update(condition_dict)
        
        # 计算损失
        loss_dict = self._compute_training_loss(batch)
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)
        
        # 获取批次大小（支持多抓取）
        norm_pose = batch['norm_pose']
        if norm_pose.dim() == 3:
            B, num_grasps, _ = norm_pose.shape
            total_samples = B * num_grasps
        else:
            total_samples = norm_pose.shape[0]
        
        # 日志记录
        self.log_dict(loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=False, 
                     batch_size=total_samples)
        self.log("train/lr", self.optimizers().param_groups[0]['lr'], batch_size=total_samples)
        
        # 打印训练信息
        if batch_idx % self.print_freq == 0:
            empty_formatter = logging.Formatter('')
            root_logger = logging.getLogger()
            original_formatters = [handler.formatter for handler in root_logger.handlers]
            
            try:
                for handler in root_logger.handlers:
                    handler.setFormatter(empty_formatter)
                logging.info("")
                
                logging.info(f'{HEADER}Epoch {self.current_epoch} - Batch [{batch_idx}]{ENDC}')
                logging.info(f'{GREEN}{"Loss:":<21s} {loss.item():.4f}{ENDC}')
                for k, v in loss_dict.items():
                    logging.info(f'{BLUE}{k.title() + ":":<21s} {v.item():.4f}{ENDC}')
            finally:
                for handler, formatter in zip(root_logger.handlers, original_formatters):
                    handler.setFormatter(formatter)
        
        return loss
    
    def on_validation_epoch_start(self):
        self.validation_step_outputs = []
    
    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        # 数据预处理
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 推理
        pred_x0 = self.diffusion_model.sample(batch)  # [B, k, T+1, ...]
        pred_x0 = pred_x0[:, 0, -1]  # 取第一个采样的最后时间步
        
        # 构建预测字典（自适应维度）
        pred_dict = self._build_pred_dict(pred_x0)
        
        # 获取批次大小
        if pred_x0.dim() == 3:
            B, num_grasps, _ = pred_x0.shape
            batch_size = B * num_grasps
        else:
            batch_size = pred_x0.shape[0]
        
        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='val')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)
        
        # 日志记录
        self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
        self.validation_step_outputs.append({
            "loss": loss.item(),
            "loss_dict": {k: v.item() for k, v in loss_dict.items()}
        })
        
        return {"loss": loss, "loss_dict": loss_dict}
    
    def on_validation_epoch_end(self):
        """验证周期结束"""
        if not self.validation_step_outputs:
            return
            
        val_loss = [x["loss"] for x in self.validation_step_outputs]
        avg_loss = mean(val_loss)
        
        val_detailed_loss = {}
        for k in self.validation_step_outputs[0]["loss_dict"].keys():
            val_detailed_loss[k] = mean([x["loss_dict"][k] for x in self.validation_step_outputs])
        
        # 打印验证信息
        empty_formatter = logging.Formatter('')
        root_logger = logging.getLogger()
        original_formatters = [handler.formatter for handler in root_logger.handlers]
        
        try:
            for handler in root_logger.handlers:
                handler.setFormatter(empty_formatter)
            logging.info("")
            
            logging.info(f'{GREEN}Epoch {self.current_epoch} - Validation completed{ENDC}')
            logging.info(f'{BLUE}{"Loss:":<21s} {avg_loss:.4f}{ENDC}')
            
            for k, v in val_detailed_loss.items():
                logging.info(f'{BLUE}{k.title() + ":":<21s} {v:.4f}{ENDC}')
        finally:
            for handler, formatter in zip(root_logger.handlers, original_formatters):
                handler.setFormatter(formatter)
        
        self.log('val_loss', avg_loss, batch_size=self.batch_size, sync_dist=True)
        self.validation_step_outputs.clear()
    
    def on_test_start(self):
        self.metric_results = []
    
    def test_step(self, batch, batch_idx):
        """测试步骤"""
        # 数据预处理
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 推理
        pred_x0 = self.diffusion_model.sample(batch)
        pred_x0 = pred_x0[:, 0, -1]  # 取第一个采样的最后时间步
        
        # 构建预测字典
        pred_dict = self._build_pred_dict(pred_x0)
        
        # 指标计算
        metric_dict, metric_details = self.criterion(pred_dict, batch, mode='test')
        self.metric_results.append(metric_details)
        
        return metric_dict
    
    def _build_pred_dict(self, pred_x0: torch.Tensor) -> Dict[str, torch.Tensor]:
        """构建预测字典（自适应维度）"""
        return {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
    
    def configure_optimizers(self):
        """配置优化器和学习率调度器"""
        if self.optimizer_cfg.name.lower() == "adam":
            optimizer = torch.optim.Adam(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        elif self.optimizer_cfg.name.lower() == "adamw":
            optimizer = torch.optim.AdamW(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        else:
            raise NotImplementedError(f"No such optimizer: {self.optimizer_cfg.name}")
        
        # 处理score模型预训练
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            logging.info("Using score model without pretrain, will not load optimizer state")
            self.trainer.fit_loop.epoch_progress.current.completed = 0
            self.last_epoch = -1
        else:
            self.last_epoch = self.current_epoch - 1 if self.current_epoch else -1
        
        logging.info(f"Setting last_epoch to: {self.last_epoch}")
        
        # 学习率调度器
        if self.scheduler.name.lower() == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.scheduler.t_max,
                eta_min=self.scheduler.min_lr,
                last_epoch=self.last_epoch
            )
        elif self.scheduler.name.lower() == "steplr":
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                self.scheduler.step_size,
                gamma=self.scheduler.step_gamma,
                last_epoch=self.last_epoch
            )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss"
            }
        }
    
    def on_load_checkpoint(self, checkpoint: Dict[str, Any]) -> None:
        """加载检查点时的特殊处理"""
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            model_state_dict = self.state_dict()
            checkpoint_state_dict = checkpoint['state_dict']
            
            new_state_dict = {}
            for key, value in checkpoint_state_dict.items():
                if 'score_heads' not in key:
                    new_state_dict[key] = value
            
            model_state_dict.update(new_state_dict)
            self.load_state_dict(model_state_dict)
            logging.info("Loaded checkpoint weights (excluding score_heads)")
        else:
            current_state = self.state_dict()
            different = self._check_state_dict(checkpoint['state_dict'], current_state)
            
            if different:
                logging.warning("State dict inconsistency detected!")
                input("Press ENTER to continue or Ctrl-C to interrupt")
            else:
                logging.info("Checkpoint state dict is CONSISTENT with model state dict")
            
            logging.info("Loading full checkpoint")
    
    def _check_state_dict(self, dict1, dict2):
        """检查状态字典一致性"""
        if dict1.keys() != dict2.keys():
            logging.warning("Keys mismatch!")
            logging.warning(f"Only in dict1: {set(dict1.keys()) - set(dict2.keys())}")
            logging.warning(f"Only in dict2: {set(dict2.keys()) - set(dict1.keys())}")
            return True
        
        for key in dict1:
            if dict1[key].shape != dict2[key].shape:
                logging.warning(f"Shape mismatch for {key}!")
                return True
        
        return False
    
    # 推理接口 - 保持向后兼容
    def forward_infer(self, data: Dict, k: int = 4, timestep: int = -1):
        """推理接口"""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, timestep]
        
        # 构建预测字典
        pred_dict = self._build_pred_dict(pred_x0)
        preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
        return preds_hand, targets_hand
    
    def forward_infer_step(self, data: Dict, k: int = 4, timesteps: Optional[List[int]] = None):
        """多时间步推理接口"""
        if timesteps is None:
            timesteps = [1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]
            
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        
        results = []
        for timestep in timesteps:
            pred_x0_t = pred_x0[:, :, timestep]
            pred_dict = self._build_pred_dict(pred_x0_t)
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
            results.append(preds_hand)
        
        return results
    
    def forward_get_pose(self, data: Dict, k: int = 4):
        """获取姿态接口"""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        
        pred_dict = self._build_pred_dict(pred_x0)
        hand_model_pose = denorm_hand_pose_robust(pred_dict["pred_pose_norm"], self.rot_type, self.mode)
        return hand_model_pose
    
    def forward_get_pose_matched(self, data: Dict, k: int = 4):
        """获取匹配姿态接口"""
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        
        pred_dict = self._build_pred_dict(pred_x0)
        matched_pred, matched_targets, outputs, targets = self.criterion.infer_norm_process_dict_get_pose_matched(
            pred_dict, data
        )
        return matched_pred, matched_targets, outputs, targets
    
    def forward_train_instance(self, data: Dict):
        """训练实例接口"""
        data = process_hand_pose(data, rot_type=self.rot_type, mode=self.mode)
        B = data['norm_pose'].shape[0]
        
        # 时间步采样
        ts = self._sample_timesteps(B)
        
        # 噪声生成和加噪
        noise = torch.randn_like(data['norm_pose'], device=self.device)
        x_t = self.diffusion_model.q_sample(x0=data['norm_pose'], t=ts, noise=noise)
        
        # 条件编码
        condition = self.eps_model.condition(data)
        data["cond"] = condition
        
        # 模型前向传播
        output = self.eps_model(x_t, ts, data)
        
        # 构建预测字典
        if self.diffusion_model.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            pred_x0 = self.diffusion_model._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }
        
        # 获取姿态
        outputs, targets = self.criterion.get_hand_model_pose(pred_dict, data)
        outputs, targets = self.criterion.get_hand(outputs, targets)
        
        return outputs, targets