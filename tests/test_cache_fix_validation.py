#!/usr/bin/env python3
"""
验证缓存修复是否有效

这个脚本测试修复后的缓存系统是否能正确处理空缓存文件
"""

import os
import h5py
import logging
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datasets.utils.cache_utils import CacheManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_cache_data(cache_path: str, num_items: int) -> None:
    """创建测试缓存数据"""
    logger.info(f"开始创建测试缓存数据: {cache_path}, 项目数: {num_items}")
    
    with h5py.File(cache_path, 'w') as hf:
        for idx in range(num_items):
            group = hf.create_group(str(idx))
            group.create_dataset('test_data', data=[idx, idx*2, idx*3])
            group.attrs['test_attr'] = f'item_{idx}'
            
            if idx % 10 == 0:
                logger.info(f"已创建 {idx+1}/{num_items} 个测试项目")
    
    logger.info(f"测试缓存数据创建完成: {num_items} 个项目")

def test_fixed_cache_logic():
    """测试修复后的缓存逻辑"""
    logger.info("=" * 60)
    logger.info("测试修复后的缓存逻辑")
    logger.info("=" * 60)
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_path = os.path.join(temp_dir, "test_fixed_cache.h5")
        num_items = 50
        
        # 步骤1: 创建空缓存文件（模拟分布式训练中的情况）
        logger.info("步骤1: 创建空缓存文件")
        with h5py.File(cache_path, 'w') as hf:
            pass  # 创建空文件
        logger.info(f"✅ 空缓存文件已创建: {cache_path}")
        
        # 步骤2: 使用修复后的缓存管理器
        logger.info("步骤2: 初始化缓存管理器")
        cache_manager = CacheManager(
            config={'cache_dir': temp_dir, 'cache_version': 'test_fixed'},
            num_items=num_items
        )
        
        # 手动设置缓存路径（因为我们已经创建了空文件）
        cache_manager.cache_path = cache_path
        
        # 步骤3: 测试空缓存检测
        logger.info("步骤3: 测试空缓存检测")
        
        # 尝试加载空缓存
        try:
            hf = h5py.File(cache_path, 'r')
            actual_items = len(hf)
            hf.close()
            
            logger.info(f"空缓存包含 {actual_items} 个项目")
            
            if actual_items == 0:
                logger.info("✅ 正确检测到空缓存")
            else:
                logger.error(f"❌ 意外的项目数量: {actual_items}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 加载缓存时出错: {e}")
            return False
        
        # 步骤4: 测试缓存填充
        logger.info("步骤4: 测试缓存填充")
        
        try:
            # 使用修复后的逻辑创建缓存
            cache_manager.create_cache(create_test_cache_data)
            
            # 验证缓存是否正确填充
            hf = cache_manager.get_file_handle()
            if hf is not None:
                actual_items = len(hf)
                logger.info(f"✅ 缓存填充完成，包含 {actual_items} 个项目")
                
                if actual_items == num_items:
                    logger.info("✅ 项目数量正确")
                    return True
                else:
                    logger.error(f"❌ 项目数量不匹配: {actual_items} != {num_items}")
                    return False
            else:
                logger.error("❌ 缓存文件句柄为空")
                return False
                
        except Exception as e:
            logger.error(f"❌ 缓存填充失败: {e}")
            return False

def test_cache_validation_logic():
    """测试缓存验证逻辑"""
    logger.info("=" * 60)
    logger.info("测试缓存验证逻辑")
    logger.info("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_path = os.path.join(temp_dir, "test_validation.h5")
        
        # 测试1: 空缓存文件
        logger.info("测试1: 空缓存文件验证")
        with h5py.File(cache_path, 'w') as hf:
            pass
        
        from datasets.utils.cache_utils import validate_cache_file
        is_valid = validate_cache_file(cache_path, 100)
        
        if not is_valid:
            logger.info("✅ 正确识别空缓存为无效")
        else:
            logger.error("❌ 错误地将空缓存识别为有效")
            return False
        
        # 测试2: 不完整的缓存文件
        logger.info("测试2: 不完整缓存文件验证")
        with h5py.File(cache_path, 'w') as hf:
            for i in range(50):  # 只创建50个，期望100个
                group = hf.create_group(str(i))
                group.create_dataset('data', data=[i])
        
        is_valid = validate_cache_file(cache_path, 100)
        
        if not is_valid:
            logger.info("✅ 正确识别不完整缓存为无效")
        else:
            logger.error("❌ 错误地将不完整缓存识别为有效")
            return False
        
        # 测试3: 完整的缓存文件
        logger.info("测试3: 完整缓存文件验证")
        with h5py.File(cache_path, 'w') as hf:
            for i in range(100):  # 创建完整的100个
                group = hf.create_group(str(i))
                group.create_dataset('data', data=[i])
        
        is_valid = validate_cache_file(cache_path, 100)
        
        if is_valid:
            logger.info("✅ 正确识别完整缓存为有效")
            return True
        else:
            logger.error("❌ 错误地将完整缓存识别为无效")
            return False

def main():
    """主函数"""
    logger.info("开始验证缓存修复...")
    
    # 测试修复后的缓存逻辑
    test1_passed = test_fixed_cache_logic()
    
    # 测试缓存验证逻辑
    test2_passed = test_cache_validation_logic()
    
    # 总结结果
    logger.info("=" * 60)
    logger.info("测试结果总结")
    logger.info("=" * 60)
    
    logger.info(f"缓存逻辑修复测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    logger.info(f"缓存验证逻辑测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        logger.info("🎉 所有测试通过！缓存修复成功")
        logger.info("现在可以安全地进行训练，缓存系统将正确工作")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步检查修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
