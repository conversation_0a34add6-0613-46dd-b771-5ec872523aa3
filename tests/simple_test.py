#!/usr/bin/env python3
"""
简单测试HandModel多抓取重构
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    import torch
    print("✓ PyTorch导入成功")
    
    from utils.hand_model import HandModel, HandModelType
    print("✓ HandModel导入成功")
    
    # 创建手部模型
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        device='cpu',
        rot_type='r6d'
    )
    print("✓ HandModel初始化成功")
    
    # 测试单抓取格式
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 6  # translation + joints + r6d
    hand_pose_single = torch.randn(batch_size, pose_dim)
    
    print(f"测试单抓取格式，输入形状: {hand_pose_single.shape}")
    hand_model.set_parameters(hand_pose_single)
    
    print(f"✓ is_multi_grasp: {hand_model.is_multi_grasp}")
    print(f"✓ batch_size: {hand_model.batch_size}")
    print(f"✓ global_translation shape: {hand_model.global_translation.shape}")
    print(f"✓ global_rotation shape: {hand_model.global_rotation.shape}")
    
    # 测试多抓取格式
    num_grasps = 3
    hand_pose_multi = torch.randn(batch_size, num_grasps, pose_dim)
    
    print(f"\n测试多抓取格式，输入形状: {hand_pose_multi.shape}")
    hand_model.set_parameters(hand_pose_multi)
    
    print(f"✓ is_multi_grasp: {hand_model.is_multi_grasp}")
    print(f"✓ batch_size: {hand_model.batch_size}")
    print(f"✓ num_grasps: {hand_model.num_grasps}")
    print(f"✓ global_translation shape: {hand_model.global_translation.shape}")
    print(f"✓ global_rotation shape: {hand_model.global_rotation.shape}")
    
    print("\n🎉 基本测试通过! HandModel多抓取重构成功!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
