#!/usr/bin/env python3
"""
测试多抓取匹配器的验证脚本
验证匹配器重构是否正确支持多预测vs多目标匹配
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import pytest
from unittest.mock import MagicMock
from omegaconf import OmegaConf

from models.loss.matcher import Matcher
from models.loss.grasp_loss_pose import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def create_test_loss_config():
    """创建测试用的损失配置"""
    config = OmegaConf.create({
        'hand_model': {
            'n_surface_points': 1024,
            'rot_type': 'r6d'
        },
        'loss_weights': {
            'para': 1.0,
            'noise': 1.0,
            'translation': 10.0,
            'rotation': 10.0,
            'qpos': 1.0,
            'neg_loss': 5.0
        },
        'cost_weights': {
            'hand_mesh': 0.0,
            'qpos': 1.0,
            'translation': 2.0,
            'rotation': 2.0
        },
        'device': 'cpu',
        'rot_type': 'r6d',
        'mode': 'camera_centric',
        'q1': {
            'lambda_torque': 10,
            'm': 8,
            'mu': 1,
            'nms': True,
            'thres_contact': 0.01,
            'thres_pen': 0.005,
            'thres_tpen': 0.01,
            'rot_type': 'r6d'
        },
        'multi_grasp': {
            'loss_aggregation': 'mean',
            'use_consistency_loss': True,
            'consistency_loss_weight': 0.1,
            'diversity_loss_weight': 0.05
        }
    })
    return config


def test_multi_grasp_matcher():
    """测试多抓取匹配器"""
    
    print("=== 测试多抓取匹配器 ===")
    
    # 初始化匹配器
    weight_dict = {'translation': 1.0, 'qpos': 1.0, 'rotation': 1.0}
    matcher = Matcher(weight_dict, rot_type='r6d')
    
    # 1. 测试多预测vs多目标匹配
    B, num_preds, num_targets, pose_dim = 2, 4, 6, 25
    
    preds = {
        'translation_norm': torch.randn(B, num_preds, 3),
        'qpos_norm': torch.randn(B, num_preds, 16),
        'rotation': torch.randn(B, num_preds, 6),
    }
    
    targets = {
        'norm_pose': torch.randn(B, num_targets, pose_dim)
    }
    
    # 设置部分目标为有效
    targets['norm_pose'][0, 3:] = 0  # 第一个batch只有3个有效目标
    targets['norm_pose'][1, 4:] = 0  # 第二个batch只有4个有效目标
    
    # 执行匹配
    assignment = matcher(preds, targets)
    
    # 验证匹配结果
    assert 'assignments' in assignment
    assert 'per_query_gt_inds' in assignment
    assert 'query_matched_mask' in assignment
    
    # 验证匹配数量合理
    total_matched = assignment['query_matched_mask'].sum().item()
    assert total_matched <= min(num_preds * B, 3 + 4)  # 不超过有效目标总数
    
    print(f"✅ 多对多匹配测试通过，总匹配数: {total_matched}")
    
    # 2. 测试向后兼容性（单预测）
    single_preds = {
        'translation_norm': torch.randn(B, 3),
        'qpos_norm': torch.randn(B, 16),
        'rotation': torch.randn(B, 6),
    }
    
    single_assignment = matcher(single_preds, targets)
    single_matched = single_assignment['query_matched_mask'].sum().item()
    assert single_matched <= B  # 每个batch最多匹配1个
    
    print(f"✅ 单预测兼容性测试通过，匹配数: {single_matched}")


def test_matched_extraction():
    """测试匹配结果提取"""
    
    print("=== 测试匹配结果提取 ===")
    
    # 创建损失函数实例
    config = create_test_loss_config()
    
    # Mock the HandModel to avoid device/CUDA issues in testing
    with pytest.MonkeyPatch().context() as m:
        mock_hand_model = MagicMock()
        m.setattr('models.loss.grasp_loss_pose.HandModel', lambda *args, **kwargs: mock_hand_model)
        m.setattr('models.loss.grasp_loss_pose.Matcher', lambda *args, **kwargs: MagicMock())
        
        criterion = GraspLossPose(config)
    
    # 模拟匹配结果
    B, num_preds, num_targets = 2, 3, 4
    
    predictions = {
        'pred_pose_norm': torch.randn(B, num_preds, 25),
        'hand_model_pose': torch.randn(B, num_preds, 25),
    }
    
    targets = {
        'norm_pose': torch.randn(B, num_targets, 25),
        'hand_model_pose': torch.randn(B, num_targets, 25),
    }
    
    # 模拟匹配分配
    assignment = {
        'per_query_gt_inds': torch.tensor([[0, 1, 2], [0, 2, 3]]),  # 每个query对应的target
        'query_matched_mask': torch.tensor([[1, 1, 0], [1, 0, 1]]),  # 匹配掩码
    }
    
    # 提取匹配对
    matched_preds, matched_targets = criterion.get_matched_by_assignment(
        predictions, targets, assignment
    )
    
    # 验证匹配结果
    expected_matched_count = assignment['query_matched_mask'].sum().item()  # 应该是3个匹配
    
    assert matched_preds['pred_pose_norm'].shape[0] == expected_matched_count
    assert matched_targets['norm_pose'].shape[0] == expected_matched_count
    
    print(f"✅ 匹配提取测试通过，提取了{expected_matched_count}个匹配对")


def test_single_grasp_compatibility():
    """测试单抓取兼容性"""
    
    print("=== 测试单抓取兼容性 ===")
    
    # 创建损失函数实例
    config = create_test_loss_config()
    
    # Mock dependencies
    with pytest.MonkeyPatch().context() as m:
        mock_hand_model = MagicMock()
        m.setattr('models.loss.grasp_loss_pose.HandModel', lambda *args, **kwargs: mock_hand_model)
        m.setattr('models.loss.grasp_loss_pose.Matcher', lambda *args, **kwargs: MagicMock())
        
        criterion = GraspLossPose(config)
    
    # 单抓取数据
    B, pose_dim = 4, 25
    
    predictions = {
        'pred_pose_norm': torch.randn(B, pose_dim),
        'hand_model_pose': torch.randn(B, pose_dim),
    }
    
    targets = {
        'norm_pose': torch.randn(B, pose_dim),
        'hand_model_pose': torch.randn(B, pose_dim),
    }
    
    # 模拟单抓取匹配分配
    assignment = {
        'per_query_gt_inds': torch.tensor([[0], [0], [0], [0]]),  # 每个batch只有1个query
        'query_matched_mask': torch.tensor([[1], [1], [1], [1]]),  # 都匹配
    }
    
    # 提取匹配对
    matched_preds, matched_targets = criterion.get_matched_by_assignment(
        predictions, targets, assignment
    )
    
    # 验证单抓取结果
    expected_matched_count = B  # 每个batch一个匹配
    
    assert matched_preds['pred_pose_norm'].shape[0] == expected_matched_count
    assert matched_targets['norm_pose'].shape[0] == expected_matched_count
    
    print(f"✅ 单抓取兼容性测试通过，匹配数: {expected_matched_count}")


def test_cost_matrix_computation():
    """测试成本矩阵计算"""
    
    print("=== 测试成本矩阵计算 ===")
    
    # 初始化匹配器
    weight_dict = {'translation': 1.0, 'qpos': 1.0, 'rotation': 1.0}
    matcher = Matcher(weight_dict, rot_type='r6d')
    
    # 测试多预测成本计算
    B, num_preds, num_targets = 2, 3, 4
    
    # 多预测数据
    multi_preds = {
        'translation_norm': torch.randn(B, num_preds, 3),
        'qpos_norm': torch.randn(B, num_preds, 16),
        'rotation': torch.randn(B, num_preds, 6),
    }
    
    targets = {
        'norm_pose': torch.randn(B, num_targets, 25)
    }
    
    # 计算成本矩阵
    translation_cost = matcher.get_translation_cost_mat(multi_preds, targets)
    qpos_cost = matcher.get_qpos_cost_mat(multi_preds, targets)
    rotation_cost = matcher.get_rotation_cost_mat(multi_preds, targets, rotation_type='r6d')
    
    # 验证成本矩阵维度
    expected_shape = (B, num_preds, num_targets)
    assert translation_cost.shape == expected_shape
    assert qpos_cost.shape == expected_shape
    assert rotation_cost.shape == expected_shape
    
    print(f"✅ 多预测成本矩阵计算测试通过，形状: {translation_cost.shape}")
    
    # 测试单预测成本计算（向后兼容）
    single_preds = {
        'translation_norm': torch.randn(B, 3),
        'qpos_norm': torch.randn(B, 16),
        'rotation': torch.randn(B, 6),
    }
    
    single_translation_cost = matcher.get_translation_cost_mat(single_preds, targets)
    single_qpos_cost = matcher.get_qpos_cost_mat(single_preds, targets)
    single_rotation_cost = matcher.get_rotation_cost_mat(single_preds, targets, rotation_type='r6d')
    
    # 验证单预测成本矩阵维度（应该被扩展为[B, 1, num_targets]）
    expected_single_shape = (B, 1, num_targets)
    assert single_translation_cost.shape == expected_single_shape
    assert single_qpos_cost.shape == expected_single_shape
    assert single_rotation_cost.shape == expected_single_shape
    
    print(f"✅ 单预测成本矩阵计算测试通过，形状: {single_translation_cost.shape}")


if __name__ == "__main__":
    # 运行所有测试
    print("🚀 开始多抓取匹配器验证测试")
    
    test_multi_grasp_matcher()
    test_matched_extraction()
    test_single_grasp_compatibility()
    test_cost_matrix_computation()
    
    print("\n🎉 所有多抓取匹配器测试通过！")
