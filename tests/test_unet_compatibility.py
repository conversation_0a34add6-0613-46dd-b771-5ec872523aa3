#!/usr/bin/env python3
"""
测试 UNetModel 与 DDPMLightning 的兼容性
"""

import torch
import torch.nn as nn
from omegaconf import OmegaConf
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.decoder.unet import UNetModel
from models.diffuser_lightning import DDPMLightning

def create_test_config():
    """创建测试配置"""
    config = OmegaConf.create({
        'rot_type': 'quat',
        'd_model': 512,
        'time_embed_mult': 2,
        'nblocks': 2,  # 减少块数以加快测试
        'resblock_dropout': 0.0,
        'transformer_num_heads': 8,
        'transformer_dim_head': 64,
        'transformer_dropout': 0.1,
        'transformer_depth': 1,
        'transformer_mult_ff': 2,
        'context_dim': 512,
        'use_position_embedding': True,
        'use_text_condition': True,
        'text_dropout_prob': 0.1,
        'backbone': {
            'name': 'pointnet2',
            'use_pooling': False,
            'layer1': {
                'npoint': 512,  # 减少点数以加快测试
                'radius_list': [0.04],
                'nsample_list': [32],
                'mlp_list': [3, 64, 64, 128]
            },
            'layer2': {
                'npoint': 256,
                'radius_list': [0.1],
                'nsample_list': [16],
                'mlp_list': [128, 128, 128, 256]
            },
            'layer3': {
                'npoint': 128,
                'radius_list': [0.2],
                'nsample_list': [16],
                'mlp_list': [256, 128, 128, 256]
            },
            'layer4': {
                'npoint': 64,
                'radius_list': [0.3],
                'nsample_list': [8],
                'mlp_list': [256, 512, 512]
            },
            'use_xyz': True,
            'normalize_xyz': True
        }
    })
    return config

def create_test_data(batch_size=2, num_points=1024, num_grasps=4):
    """创建测试数据"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 场景点云数据 (B, N, 6) - xyz + rgb
    scene_pc = torch.randn(batch_size, num_points, 6, device=device)
    
    # 单抓取格式的姿态数据 (B, 23) - quat格式
    single_pose = torch.randn(batch_size, 23, device=device)
    
    # 多抓取格式的姿态数据 (B, num_grasps, 23)
    multi_pose = torch.randn(batch_size, num_grasps, 23, device=device)
    
    # 时间步
    timesteps = torch.randint(0, 100, (batch_size,), device=device)
    
    # 文本提示（可选）
    positive_prompts = ["grasp the object"] * batch_size
    
    return {
        'scene_pc': scene_pc,
        'single_pose': single_pose,
        'multi_pose': multi_pose,
        'timesteps': timesteps,
        'positive_prompt': positive_prompts,
        'device': device
    }

def test_unet_basic_functionality():
    """测试 UNetModel 基本功能"""
    print("🧪 测试 UNetModel 基本功能...")
    
    config = create_test_config()
    model = UNetModel(config)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    test_data = create_test_data()
    
    # 测试条件编码
    print("  ✓ 测试条件编码...")
    data_dict = {
        'scene_pc': test_data['scene_pc'],
        'positive_prompt': test_data['positive_prompt']
    }
    
    condition_dict = model.condition(data_dict)
    assert 'scene_cond' in condition_dict
    assert 'text_cond' in condition_dict
    print(f"    场景条件形状: {condition_dict['scene_cond'].shape}")
    if condition_dict['text_cond'] is not None:
        print(f"    文本条件形状: {condition_dict['text_cond'].shape}")
    
    # 测试单抓取前向传播
    print("  ✓ 测试单抓取前向传播...")
    data_dict.update(condition_dict)
    single_output = model(test_data['single_pose'], test_data['timesteps'], data_dict)
    assert single_output.shape == test_data['single_pose'].shape
    print(f"    单抓取输出形状: {single_output.shape}")
    
    # 测试多抓取前向传播
    print("  ✓ 测试多抓取前向传播...")
    multi_output = model(test_data['multi_pose'], test_data['timesteps'], data_dict)
    assert multi_output.shape == test_data['multi_pose'].shape
    print(f"    多抓取输出形状: {multi_output.shape}")
    
    print("✅ UNetModel 基本功能测试通过！")

def test_interface_compatibility():
    """测试接口兼容性"""
    print("\n🔗 测试接口兼容性...")
    
    config = create_test_config()
    model = UNetModel(config)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    test_data = create_test_data()
    
    # 模拟 DDPMLightning 的调用方式
    print("  ✓ 模拟 DDPMLightning 调用...")
    
    # 1. 条件编码调用
    data_dict = {
        'scene_pc': test_data['scene_pc'],
        'positive_prompt': test_data['positive_prompt']
    }
    condition_dict = model.condition(data_dict)
    data_dict.update(condition_dict)
    
    # 2. 前向传播调用 (单抓取)
    x_t = test_data['single_pose']
    ts = test_data['timesteps']
    output = model(x_t, ts, data_dict)
    
    assert output.shape == x_t.shape, f"输出形状不匹配: {output.shape} vs {x_t.shape}"
    assert output.dtype == x_t.dtype, f"输出类型不匹配: {output.dtype} vs {x_t.dtype}"
    assert output.device == x_t.device, f"输出设备不匹配: {output.device} vs {x_t.device}"
    
    # 3. 前向传播调用 (多抓取)
    x_t_multi = test_data['multi_pose']
    output_multi = model(x_t_multi, ts, data_dict)
    
    assert output_multi.shape == x_t_multi.shape, f"多抓取输出形状不匹配: {output_multi.shape} vs {x_t_multi.shape}"
    
    print("✅ 接口兼容性测试通过！")

def test_dimension_consistency():
    """测试维度一致性"""
    print("\n📏 测试维度一致性...")
    
    config = create_test_config()
    model = UNetModel(config)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 测试不同批次大小
    for batch_size in [1, 2, 4]:
        print(f"  ✓ 测试批次大小: {batch_size}")
        test_data = create_test_data(batch_size=batch_size)
        
        data_dict = {
            'scene_pc': test_data['scene_pc'],
            'positive_prompt': test_data['positive_prompt']
        }
        condition_dict = model.condition(data_dict)
        data_dict.update(condition_dict)
        
        # 单抓取
        output = model(test_data['single_pose'], test_data['timesteps'], data_dict)
        assert output.shape[0] == batch_size
        
        # 多抓取
        output_multi = model(test_data['multi_pose'], test_data['timesteps'], data_dict)
        assert output_multi.shape[0] == batch_size
    
    # 测试不同抓取数量
    for num_grasps in [1, 2, 4, 8]:
        print(f"  ✓ 测试抓取数量: {num_grasps}")
        test_data = create_test_data(num_grasps=num_grasps)
        
        data_dict = {
            'scene_pc': test_data['scene_pc'],
            'positive_prompt': test_data['positive_prompt']
        }
        condition_dict = model.condition(data_dict)
        data_dict.update(condition_dict)
        
        multi_pose = torch.randn(2, num_grasps, 23, device=device)
        output = model(multi_pose, test_data['timesteps'], data_dict)
        assert output.shape == multi_pose.shape
    
    print("✅ 维度一致性测试通过！")

def test_gradient_flow():
    """测试梯度流"""
    print("\n🌊 测试梯度流...")
    
    config = create_test_config()
    model = UNetModel(config)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.train()
    
    test_data = create_test_data()
    
    data_dict = {
        'scene_pc': test_data['scene_pc'],
        'positive_prompt': test_data['positive_prompt']
    }
    condition_dict = model.condition(data_dict)
    data_dict.update(condition_dict)
    
    # 测试单抓取梯度
    x_t = test_data['single_pose'].requires_grad_(True)
    output = model(x_t, test_data['timesteps'], data_dict)
    loss = output.sum()
    loss.backward()
    
    assert x_t.grad is not None, "输入梯度为空"
    assert not torch.isnan(x_t.grad).any(), "输入梯度包含NaN"
    
    # 检查模型参数梯度
    has_grad = False
    for param in model.parameters():
        if param.grad is not None:
            has_grad = True
            assert not torch.isnan(param.grad).any(), "参数梯度包含NaN"
    
    assert has_grad, "模型参数没有梯度"
    
    print("✅ 梯度流测试通过！")

if __name__ == "__main__":
    print("🚀 开始 UNetModel 与 DDPMLightning 兼容性测试\n")
    
    try:
        test_unet_basic_functionality()
        test_interface_compatibility()
        test_dimension_consistency()
        test_gradient_flow()
        
        print("\n🎉 所有测试通过！UNetModel 与 DDPMLightning 完全兼容！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
