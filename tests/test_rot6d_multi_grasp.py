#!/usr/bin/env python3
"""
测试多抓取6D旋转表示转换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.rot6d import (
    compute_rotation_matrix_from_ortho6d,
    robust_compute_rotation_matrix_from_ortho6d,
    compute_rotation_matrix_from_ortho6d_legacy,
    robust_compute_rotation_matrix_from_ortho6d_legacy,
    normalize_vector,
    cross_product
)


def test_normalize_vector():
    """测试向量归一化函数"""
    print("测试向量归一化函数...")
    
    # 测试数据
    batch_size = 4
    vectors = torch.randn(batch_size, 3)
    
    # 归一化
    normalized = normalize_vector(vectors)
    
    # 验证结果
    norms = torch.norm(normalized, dim=1)
    assert torch.allclose(norms, torch.ones(batch_size), atol=1e-6), "归一化失败"
    assert normalized.shape == vectors.shape, "形状不匹配"
    
    print("✅ 向量归一化测试通过")


def test_cross_product():
    """测试向量叉积函数"""
    print("测试向量叉积函数...")
    
    # 测试数据
    batch_size = 4
    u = torch.randn(batch_size, 3)
    v = torch.randn(batch_size, 3)
    
    # 计算叉积
    cross = cross_product(u, v)
    
    # 验证结果
    assert cross.shape == (batch_size, 3), "形状不匹配"
    
    # 验证叉积性质：u × v 垂直于 u 和 v
    dot_u = torch.sum(cross * u, dim=1)
    dot_v = torch.sum(cross * v, dim=1)
    assert torch.allclose(dot_u, torch.zeros(batch_size), atol=1e-5), "叉积与u不垂直"
    assert torch.allclose(dot_v, torch.zeros(batch_size), atol=1e-5), "叉积与v不垂直"
    
    print("✅ 向量叉积测试通过")


def test_single_grasp_rotation():
    """测试单抓取6D旋转表示转换"""
    print("测试单抓取6D旋转表示转换...")
    
    batch_size = 4
    poses_6d = torch.randn(batch_size, 6)
    
    # 测试标准版本
    matrices = compute_rotation_matrix_from_ortho6d(poses_6d)
    assert matrices.shape == (batch_size, 3, 3), f"形状不匹配: {matrices.shape}"
    
    # 验证旋转矩阵性质
    for i in range(batch_size):
        mat = matrices[i]
        # 验证正交性
        identity = torch.mm(mat, mat.t())
        assert torch.allclose(identity, torch.eye(3), atol=1e-5), f"矩阵{i}不正交"
        # 验证行列式为1
        det = torch.det(mat)
        assert torch.allclose(det, torch.tensor(1.0), atol=1e-5), f"矩阵{i}行列式不为1"
    
    # 测试鲁棒版本
    robust_matrices = robust_compute_rotation_matrix_from_ortho6d(poses_6d)
    assert robust_matrices.shape == (batch_size, 3, 3), f"鲁棒版本形状不匹配: {robust_matrices.shape}"
    
    print("✅ 单抓取6D旋转表示转换测试通过")


def test_multi_grasp_rotation():
    """测试多抓取6D旋转表示转换"""
    print("测试多抓取6D旋转表示转换...")
    
    batch_size = 4
    num_grasps = 8
    poses_6d = torch.randn(batch_size, num_grasps, 6)
    
    # 测试标准版本
    matrices = compute_rotation_matrix_from_ortho6d(poses_6d)
    expected_shape = (batch_size, num_grasps, 3, 3)
    assert matrices.shape == expected_shape, f"形状不匹配: {matrices.shape} vs {expected_shape}"
    
    # 验证旋转矩阵性质
    for i in range(batch_size):
        for j in range(num_grasps):
            mat = matrices[i, j]
            # 验证正交性
            identity = torch.mm(mat, mat.t())
            assert torch.allclose(identity, torch.eye(3), atol=1e-5), f"矩阵[{i},{j}]不正交"
            # 验证行列式为1
            det = torch.det(mat)
            assert torch.allclose(det, torch.tensor(1.0), atol=1e-5), f"矩阵[{i},{j}]行列式不为1"
    
    # 测试鲁棒版本
    robust_matrices = robust_compute_rotation_matrix_from_ortho6d(poses_6d)
    assert robust_matrices.shape == expected_shape, f"鲁棒版本形状不匹配: {robust_matrices.shape}"
    
    print("✅ 多抓取6D旋转表示转换测试通过")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("测试向后兼容性...")
    
    batch_size = 4
    poses_6d = torch.randn(batch_size, 6)
    
    # 测试新函数与旧函数的兼容性
    new_result = compute_rotation_matrix_from_ortho6d(poses_6d)
    legacy_result = compute_rotation_matrix_from_ortho6d_legacy(poses_6d)
    
    assert torch.allclose(new_result, legacy_result, atol=1e-6), "向后兼容性测试失败"
    
    # 测试鲁棒版本的兼容性
    new_robust_result = robust_compute_rotation_matrix_from_ortho6d(poses_6d)
    legacy_robust_result = robust_compute_rotation_matrix_from_ortho6d_legacy(poses_6d)
    
    assert torch.allclose(new_robust_result, legacy_robust_result, atol=1e-6), "鲁棒版本向后兼容性测试失败"
    
    print("✅ 向后兼容性测试通过")


def test_consistency_between_single_and_multi():
    """测试单抓取和多抓取结果的一致性"""
    print("测试单抓取和多抓取结果的一致性...")
    
    batch_size = 4
    poses_6d = torch.randn(batch_size, 6)
    
    # 单抓取结果
    single_result = compute_rotation_matrix_from_ortho6d(poses_6d)
    
    # 多抓取结果（num_grasps=1）
    poses_6d_multi = poses_6d.unsqueeze(1)  # [B, 1, 6]
    multi_result = compute_rotation_matrix_from_ortho6d(poses_6d_multi)
    multi_result_squeezed = multi_result.squeeze(1)  # [B, 3, 3]
    
    assert torch.allclose(single_result, multi_result_squeezed, atol=1e-6), "单抓取和多抓取结果不一致"
    
    print("✅ 单抓取和多抓取一致性测试通过")


def test_error_handling():
    """测试错误处理"""
    print("测试错误处理...")
    
    # 测试不支持的维度
    try:
        invalid_poses = torch.randn(4, 5, 6, 2)  # 4D输入
        compute_rotation_matrix_from_ortho6d(invalid_poses)
        assert False, "应该抛出错误"
    except ValueError as e:
        assert "Unsupported poses dimension" in str(e)
    
    # 测试legacy函数的错误处理
    try:
        multi_poses = torch.randn(4, 8, 6)  # 3D输入
        compute_rotation_matrix_from_ortho6d_legacy(multi_poses)
        assert False, "应该抛出错误"
    except ValueError as e:
        assert "Legacy function only supports 2D input" in str(e)
    
    print("✅ 错误处理测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行多抓取6D旋转表示转换测试")
    print("=" * 50)
    
    test_normalize_vector()
    test_cross_product()
    test_single_grasp_rotation()
    test_multi_grasp_rotation()
    test_backward_compatibility()
    test_consistency_between_single_and_multi()
    test_error_handling()
    
    print("=" * 50)
    print("🎉 所有测试通过！多抓取6D旋转表示转换功能正常")
    print("=" * 50)


if __name__ == "__main__":
    run_all_tests()
