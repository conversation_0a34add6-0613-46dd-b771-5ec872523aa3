
import numpy as np
import scipy.signal as signal
import scipy.optimize as optimize
import matplotlib.pyplot as plt
import argparse
import random

# ==============================================================================
# Step 1: Environment and Parameter Setup
# ==============================================================================

# According to the competition rules: R (1kΩ-10kΩ), L (1mH-10mH), C (10nF-100nF)
COMPONENT_RANGES = {
    'R': (1e3, 10e3),
    'L': (1e-3, 10e-3),
    'C': (10e-9, 100e-9)
}

# Noise level for simulated measurements
NOISE_LEVEL = 0.05  # 5% noise

# Frequency range for the sweep
FREQ_RANGE = (100, 100e3) # 100 Hz to 100 kHz
FREQ_POINTS = 500

# Test signal for time-domain simulation
TEST_SIGNAL_FREQ = 5e3 # 5 kHz


# ==============================================================================
# Step 2: Create the "Ground Truth" Unknown Circuit Model
# ==============================================================================

def create_ground_truth_model(R, L, C, topology):
    """
    Creates a scipy.signal.lti model for a series RLC circuit.

    Args:
        R (float): Resistance in Ohms.
        L (float): Inductance in Henrys.
        C (float): Capacitance in Farads.
        topology (str): One of 'low-pass', 'high-pass', 'band-pass'.

    Returns:
        scipy.signal.lti: The LTI model of the circuit.
    """
    if topology == 'low-pass': # Output across Capacitor
        # H(s) = (1/LC) / (s^2 + (R/L)s + 1/LC)
        num = [1/(L*C)]
        den = [1, R/L, 1/(L*C)]
    elif topology == 'high-pass': # Output across Inductor
        # H(s) = s^2 / (s^2 + (R/L)s + 1/LC)
        num = [1, 0, 0]
        den = [1, R/L, 1/(L*C)]
    elif topology == 'band-pass': # Output across Resistor
        # H(s) = (R/L)s / (s^2 + (R/L)s + 1/LC)
        num = [R/L, 0]
        den = [1, R/L, 1/(L*C)]
    else:
        raise ValueError(f"Unknown topology: {topology}")

    return signal.lti(num, den)


# ==============================================================================
# Step 3: Simulate the Frequency Sweep and Data Acquisition ("学习")
# ==============================================================================

def simulate_sweep(lti_model, f_range, num_points, noise_level):
    """
    Simulates a frequency sweep and returns noisy measurement data.

    Args:
        lti_model (scipy.signal.lti): The ground truth LTI model.
        f_range (tuple): (min_freq, max_freq) in Hz.
        num_points (int): Number of frequency points to sample.
        noise_level (float): Standard deviation of the Gaussian noise to add.

    Returns:
        tuple: (frequencies, noisy_gain, noisy_phase)
    """
    f_min, f_max = f_range
    frequencies = np.logspace(np.log10(f_min), np.log10(f_max), num_points)
    w = 2 * np.pi * frequencies

    # Calculate the true frequency response (complex numbers)
    w, mag_true, phase_true_deg = lti_model.bode(w=w)
    
    # Convert true magnitude to linear gain and phase to radians
    gain_true = 10**(mag_true / 20)
    phase_true_rad = np.deg2rad(phase_true_deg)

    # Add multiplicative noise to gain and additive noise to phase
    gain_noise = np.random.normal(0, noise_level, size=num_points)
    phase_noise = np.random.normal(0, noise_level, size=num_points)
    
    noisy_gain = gain_true * (1 + gain_noise)
    noisy_phase = phase_true_rad + phase_noise
    
    return frequencies, noisy_gain, noisy_phase


# ==============================================================================
# Step 4: Fit the General Model to the "Measured" Data ("建模")
# ==============================================================================

def general_model_residuals(params, f, measured_gain, measured_phase):
    """
    Calculates the residuals for the general 2nd-order transfer function.
    This is the objective function for least_squares.
    """
    b2, b1, b0, a1, a0 = params
    w = 2 * np.pi * np.array(f)
    
    # Characteristic frequency for normalization
    w_n = 2 * np.pi * np.sqrt(FREQ_RANGE[0] * FREQ_RANGE[1])
    
    # De-normalize parameters to get actual coefficients
    B0 = b0 * w_n**2
    B1 = b1 * w_n
    B2 = b2
    A0 = a0 * w_n**2
    A1 = a1 * w_n

    s = 1j * w
    numerator = B2 * s**2 + B1 * s + B0
    denominator = s**2 + A1 * s + A0
    
    # Avoid division by zero
    denominator[denominator == 0] = 1e-12
    response = numerator / denominator

    # Calculate theoretical gain and phase
    gain_theory = np.abs(response)
    phase_theory = np.angle(response)

    # Calculate residuals
    # We weight the phase error to be comparable to the gain error
    gain_residual = gain_theory - measured_gain
    phase_residual = (np.unwrap(phase_theory) - np.unwrap(measured_phase))
    
    return np.concatenate([gain_residual, phase_residual])


# ==============================================================================
# Step 5: Visualize and Compare the Fitted Model vs. Ground Truth
# ==============================================================================

def plot_bode_comparison(ground_truth_lti, fitted_lti, frequencies, noisy_gain, noisy_phase, show_plots=True):
    """
    Plots the Bode plots for ground truth, noisy data, and the fitted model.
    """
    print("Generating Bode plot for comparison...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
    fig.suptitle('Bode Plot Comparison: Ground Truth vs. Fitted Model', fontsize=16)

    # --- Frequency range for smooth plotting ---
    plot_freq = np.logspace(np.log10(frequencies[0]), np.log10(frequencies[-1]), 500)
    w_plot = 2 * np.pi * plot_freq

    # --- Ground Truth ---
    w_gt, mag_gt_db, phase_gt_deg = ground_truth_lti.bode(w=w_plot)
    ax1.semilogx(plot_freq, mag_gt_db, 'k-', label='Ground Truth')
    ax2.semilogx(plot_freq, phase_gt_deg, 'k-', label='Ground Truth')

    # --- Noisy Measured Data ---
    noisy_gain_db = 20 * np.log10(noisy_gain)
    # Un-wrap phase for better plotting
    noisy_phase_deg_unwrapped = np.unwrap(noisy_phase) * 180 / np.pi
    ax1.semilogx(frequencies, noisy_gain_db, 'ro', markersize=3, alpha=0.5, label='Measured Data (Noisy)')
    ax2.semilogx(frequencies, noisy_phase_deg_unwrapped, 'ro', markersize=3, alpha=0.5, label='Measured Data (Noisy)')

    # --- Fitted Model ---
    w_fit, mag_fit_db, phase_fit_deg = fitted_lti.bode(w=w_plot)
    ax1.semilogx(plot_freq, mag_fit_db, 'b--', label='Fitted Model')
    ax2.semilogx(plot_freq, phase_fit_deg, 'b--', label='Fitted Model')

    # --- Formatting ---
    ax1.set_ylabel('Magnitude (dB)')
    ax1.grid(True, which='both', linestyle='--')
    ax1.legend()
    
    ax2.set_xlabel('Frequency (Hz)')
    ax2.set_ylabel('Phase (degrees)')
    ax2.grid(True, which='both', linestyle='--')
    ax2.legend()

    plt.savefig('bode_comparison_plot.png')
    print("Bode plot saved to 'bode_comparison_plot.png'")
    if show_plots:
        plt.show()


# ==============================================================================
# Step 6: Simulate Real-Time Output Replication ("推理生成")
# ==============================================================================

def simulate_time_domain_response(ground_truth_lti, fitted_lti, signal_freq, v_peak_to_peak=2.0, show_plots=True):
    """
    Simulates the time-domain response to a square wave input signal.
    """
    print("Simulating time-domain response to a square wave...")

    # --- Generate Test Input Signal (Square Wave) ---
    t = np.linspace(0, 5 / signal_freq, 1000, endpoint=False) # 5 cycles
    v_in = (v_peak_to_peak / 2) * signal.square(2 * np.pi * signal_freq * t)

    # --- Simulate Output from Both Models ---
    try:
        t_out_gt, y_out_gt, _ = signal.lsim(ground_truth_lti, v_in, t)
        t_out_fit, y_out_fit, _ = signal.lsim(fitted_lti, v_in, t)
    except (ValueError, np.linalg.LinAlgError):
        print("Error during time-domain simulation, likely due to unstable fitted model.")
        return 0, np.nan, np.nan


    # --- Visualize and Quantify Performance ---
    if show_plots:
        plt.figure(figsize=(12, 6))
        plt.plot(t, v_in, 'g:', label='Input Signal (Square Wave)')
        plt.plot(t_out_gt, y_out_gt, 'k-', label='Ground Truth Output')
        plt.plot(t_out_fit, y_out_fit, 'b--', label='Fitted Model Output')
        plt.title('Time-Domain Response Replication')
        plt.xlabel('Time (s)')
        plt.ylabel('Voltage (V)')
        plt.legend()
        plt.grid(True)
        plt.savefig('time_domain_response.png')
        print("Time-domain response plot saved to 'time_domain_response.png'")
        plt.show()

    # --- Calculate Peak-to-Peak Error ---
    vpp_gt = np.ptp(y_out_gt)
    vpp_fit = np.ptp(y_out_fit)
    
    if vpp_gt > 1e-9: # Avoid division by zero for zero-output
        relative_error = (abs(vpp_fit - vpp_gt) / vpp_gt) * 100 if np.isfinite(vpp_fit) else np.inf
    else:
        relative_error = 0 if abs(vpp_fit) < 1e-9 else np.inf


    print("\n--- Replication Performance ---")
    print(f"Ground Truth Output Vp-p: {vpp_gt:.4f} V")
    print(f"Fitted Model Output Vp-p: {vpp_fit:.4f} V")
    print(f"Relative Peak-to-Peak Error: {relative_error:.2f}%")

    if np.isfinite(relative_error) and relative_error <= 10.0:
        print("SUCCESS: Relative error is within the <= 10% requirement.")
    else:
        print("FAILURE: Relative error exceeds the 10% requirement.")
    
    return vpp_gt, vpp_fit, relative_error


def find_bandwidth_indices(gain):
    """
    Finds the indices of the data points within the -3dB bandwidth.
    """
    peak_gain = np.max(gain)
    three_db_gain = peak_gain / np.sqrt(2)
    
    # Find all indices where gain is greater than or equal to the -3dB threshold
    indices = np.where(gain >= three_db_gain)[0]
    return indices

def perform_fit(frequencies, noisy_gain, noisy_phase):
    """
    Performs the least_squares fitting and returns the fitted LTI model.
    Returns None if the fitting fails.
    """
    initial_guess = [1.0, 1.0, 1.0, 1.0, 1.0] # For normalized [b2, b1, b0, a1, a0]
    bounds = ([-1e3, -1e3, -1e3, -1e3, -1e3], [1e3, 1e3, 1e3, 1e3, 1e3])

    try:
        res = optimize.least_squares(
            general_model_residuals,
            initial_guess,
            args=(frequencies, noisy_gain, noisy_phase),
            bounds=bounds,
            method='trf',
            max_nfev=5000
        )
        
        if not res.success:
            raise RuntimeError(f"Optimization failed: {res.message}")

        popt_norm = res.x
        w_n = 2 * np.pi * np.sqrt(FREQ_RANGE[0] * FREQ_RANGE[1])
        b2_fit, b1_fit, b0_fit, a1_fit, a0_fit = popt_norm
        
        B2_fit = b2_fit
        B1_fit = b1_fit * w_n
        B0_fit = b0_fit * w_n**2
        A1_fit = a1_fit * w_n
        A0_fit = a0_fit * w_n**2
        
        num_fit = [B2_fit, B1_fit, B0_fit]
        den_fit = [1, A1_fit, A0_fit]
        return signal.lti(num_fit, den_fit)

    except RuntimeError as e:
        print(f"Error during curve fitting: {e}")
        return None

def run_bandwidth_experiment(args):
    """
    Runs an experiment to compare fitting with all points vs. points within the -3dB bandwidth.
    """
    print("\n" + "="*20 + " Bandwidth Fitting Experiment " + "="*20)
    args.no_plots = True # Disable plots for the experiment
    results = []

    for i in range(args.num_runs):
        print(f"\n{'='*25} RUN {i+1}/{args.num_runs} {'='*25}")
        
        # --- Ground Truth Setup (Always Band-pass for this experiment) ---
        true_topology = 'band-pass'
        true_R = random.uniform(*COMPONENT_RANGES['R'])
        true_L = random.uniform(*COMPONENT_RANGES['L'])
        true_C = random.uniform(*COMPONENT_RANGES['C'])
        ground_truth_lti = create_ground_truth_model(true_R, true_L, true_C, true_topology)
        
        print(f"Ground Truth: Topo={true_topology}, R={true_R/1e3:.2f}kΩ, L={true_L*1e3:.2f}mH, C={true_C*1e9:.2f}nF")

        # --- Data Acquisition ---
        frequencies, noisy_gain, noisy_phase = simulate_sweep(
            ground_truth_lti, FREQ_RANGE, args.freq_points, args.noise
        )
        print(f"Acquired {len(frequencies)} data points.")
        
        # --- 1. Fit with ALL points ---
        print("\n--- Fitting with ALL points ---")
        fitted_lti_all = perform_fit(frequencies, noisy_gain, noisy_phase)
        error_all = np.nan
        if fitted_lti_all:
            _, _, error_all = simulate_time_domain_response(ground_truth_lti, fitted_lti_all, TEST_SIGNAL_FREQ, show_plots=False)

        # --- 2. Fit with points within -3dB bandwidth ---
        print("\n--- Fitting with > -3dB points ONLY ---")
        bw_indices = find_bandwidth_indices(noisy_gain)
        error_bw = np.nan

        if len(bw_indices) < 10:
            print(f"Warning: Only {len(bw_indices)} points found in bandwidth. Skipping fit.")
        else:
            print(f"Found {len(bw_indices)} points in -3dB bandwidth for fitting.")
            freq_bw = frequencies[bw_indices]
            gain_bw = noisy_gain[bw_indices]
            phase_bw = noisy_phase[bw_indices]
            fitted_lti_bw = perform_fit(freq_bw, gain_bw, phase_bw)
            if fitted_lti_bw:
                _, _, error_bw = simulate_time_domain_response(ground_truth_lti, fitted_lti_bw, TEST_SIGNAL_FREQ, show_plots=False)

        results.append({
            'R': true_R, 'L': true_L, 'C': true_C,
            'error_all': error_all,
            'error_bw': error_bw
        })

    # --- Print Summary ---
    print(f"\n{'='*30} EXPERIMENT SUMMARY {'='*30}")
    header = f"{'Run':<5} {'R(kΩ)':<7} {'L(mH)':<7} {'C(nF)':<7} {'Error All(%)':<15} {'Error BW(%)':<15}"
    print(header)
    print("-" * (len(header) + 2))
    for i, r in enumerate(results):
        err_all_str = f"{r['error_all']:.2f}" if np.isfinite(r['error_all']) else "NaN"
        err_bw_str = f"{r['error_bw']:.2f}" if np.isfinite(r['error_bw']) else "NaN"
        print(f"{i+1:<5} {r['R']/1e3:<7.2f} {r['L']*1e3:<7.2f} {r['C']*1e9:<7.2f} {err_all_str:<15} {err_bw_str:<15}")


def parse_args():
    """
    Parses command-line arguments.
    """
    parser = argparse.ArgumentParser(description="RLC Circuit Identification and Replication Simulation")
    parser.add_argument('--topology', type=str, choices=['low-pass', 'high-pass', 'band-pass', 'random'], default='random',
                        help="Specify the circuit topology. Defaults to random.")
    parser.add_argument('--R', type=float, default=None, help="Specify Resistance in Ohms (e.g., 5100). Overrides random generation.")
    parser.add_argument('--L', type=float, default=None, help="Specify Inductance in Henrys (e.g., 0.005). Overrides random generation.")
    parser.add_argument('--C', type=float, default=None, help="Specify Capacitance in Farads (e.g., 2.2e-8). Overrides random generation.")
    parser.add_argument('--noise', type=float, default=NOISE_LEVEL, help=f"Set the noise level for measurements. Defaults to {NOISE_LEVEL}.")
    parser.add_argument('--no-plots', action='store_true', help="Suppress showing matplotlib plots.")
    parser.add_argument('--num-runs', type=int, default=1, help="Number of simulation runs to perform. Plots are disabled for multiple runs.")
    parser.add_argument('--freq-points', type=int, default=FREQ_POINTS, help=f"Number of frequency points for the sweep. Defaults to {FREQ_POINTS}.")
    parser.add_argument('--points-experiment', action='store_true', help="Run an experiment on the number of fit points vs. accuracy.")
    parser.add_argument('--bandwidth-experiment', action='store_true', help="Run a comparison of fitting with all points vs. points within the -3dB bandwidth.")
    
    return parser.parse_args()

def run_single_simulation(args):
    """
    Runs a single instance of the RLC simulation.
    """
    # --- Ground Truth Setup ---
    true_topology = random.choice(['low-pass', 'high-pass', 'band-pass']) if args.topology == 'random' else args.topology
    true_R = args.R if args.R is not None else random.uniform(*COMPONENT_RANGES['R'])
    true_L = args.L if args.L is not None else random.uniform(*COMPONENT_RANGES['L'])
    true_C = args.C if args.C is not None else random.uniform(*COMPONENT_RANGES['C'])

    print(f"Ground Truth Circuit:")
    print(f"  - Topology: {true_topology}")
    print(f"  - Resistance (R): {true_R/1e3:.2f} kΩ")
    print(f"  - Inductance (L): {true_L*1e3:.2f} mH")
    print(f"  - Capacitance (C): {true_C*1e9:.2f} nF")

    ground_truth_lti = create_ground_truth_model(true_R, true_L, true_C, true_topology)
    print(f"  - Ground Truth H(s): {ground_truth_lti}")
    print("-" * 50)

    # --- Data Acquisition ---
    print("Simulating frequency sweep to acquire 'measured' data...")
    frequencies, noisy_gain, noisy_phase = simulate_sweep(
        ground_truth_lti, FREQ_RANGE, args.freq_points, args.noise
    )
    print(f"Acquired {len(frequencies)} data points from {FREQ_RANGE[0]} Hz to {FREQ_RANGE[1]/1e3} kHz with noise level {args.noise}.")
    print("-" * 50)

    # --- Model Fitting ---
    print("Fitting general model to the measured data...")
    fitted_lti = perform_fit(frequencies, noisy_gain, noisy_phase)
    
    if fitted_lti is None:
        print("Could not find optimal parameters.")
        return None

    print(f"  - Fitted H(s): {fitted_lti}")
    print("-" * 50)

    # --- Visualization ---
    plot_bode_comparison(ground_truth_lti, fitted_lti, frequencies, noisy_gain, noisy_phase, show_plots=(not args.no_plots))
    
    print("-" * 50)

    # --- Time-Domain Replication ---
    vpp_gt, vpp_fit, relative_error = simulate_time_domain_response(ground_truth_lti, fitted_lti, TEST_SIGNAL_FREQ, show_plots=(not args.no_plots))

    return {
        'topology': true_topology,
        'R': true_R,
        'L': true_L,
        'C': true_C,
        'Vpp_gt': vpp_gt,
        'Vpp_fit': vpp_fit,
        'error': relative_error
    }


def run_points_experiment(args):
    """
    Runs an experiment to test the effect of the number of fitting points on accuracy.
    """
    print("\n" + "="*20 + " Points vs. Accuracy Experiment " + "="*20)
    
    # --- Use a fixed "ground truth" circuit for consistent comparison ---
    args.topology = 'band-pass'
    args.R = 5600.0  # 5.6 kΩ
    args.L = 5.0e-3  # 5 mH
    args.C = 50e-9   # 50 nF
    args.no_plots = True # Disable plots for the experiment
    
    points_to_test = [20, 30, 40, 50, 75, 100, 200, 500]
    results = []

    print(f"Fixed Ground Truth: Topology={args.topology}, R={args.R/1e3}kΩ, L={args.L*1e3}mH, C={args.C*1e9}nF")

    for i, num_points in enumerate(points_to_test):
        print(f"\n--- Running for {num_points} points ({i+1}/{len(points_to_test)}) ---")
        args.freq_points = num_points
        sim_result = run_single_simulation(args)
        if sim_result:
            results.append({'points': num_points, 'error': sim_result['error']})

    # --- Print Summary Table ---
    print(f"\n{'='*25} EXPERIMENT SUMMARY {'='*25}")
    header = f"{'Fit Points':<12} {'Error (%)':<10} {'Status':<10}"
    print(header)
    print("-" * (len(header) + 2))
    for r in results:
        error = r['error']
        status = "SUCCESS" if error is not None and np.isfinite(error) and error <= 10.0 else "FAILURE"
        error_str = f"{error:.2f}" if error is not None and np.isfinite(error) else "NaN"
        print(f"{r['points']:<12} {error_str:<10} {status:<10}")

    # --- Plot Summary Graph ---
    if results:
        points = [r['points'] for r in results]
        errors = [r['error'] if r.get('error') is not None and np.isfinite(r['error']) else np.nan for r in results]

        plt.figure(figsize=(10, 6))
        plt.plot(points, errors, 'bo-', label='Experiment Result')
        plt.axhline(y=10.0, color='r', linestyle='--', label='10% Error Threshold')
        plt.title('Effect of Number of Fit Points on Model Accuracy')
        plt.xlabel('Number of Frequency Points Used for Fitting')
        plt.ylabel('Relative Peak-to-Peak Error (%)')
        plt.xscale('log')
        plt.grid(True, which='both', linestyle='--')
        plt.legend()
        plt.savefig('points_vs_accuracy_experiment.png')
        print("\nExperiment summary plot saved to 'points_vs_accuracy_experiment.png'")
        plt.show()


# ==============================================================================
# Step 2: Create the "Ground Truth" Unknown Circuit Model
# ==============================================================================

def main():
    """
    Main function to run the RLC circuit identification simulation.
    """
    args = parse_args()

    if args.bandwidth_experiment:
        run_bandwidth_experiment(args)
        return
    
    if args.points_experiment:
        run_points_experiment(args)
        return

    print("RLC Circuit Identification and Replication Simulation")
    
    if args.num_runs > 1:
        args.no_plots = True
        print(f"Starting {args.num_runs} simulation runs. Plots will be disabled.")
    
    results = []
    for i in range(args.num_runs):
        print(f"\n{'='*25} RUN {i+1}/{args.num_runs} {'='*25}")
        result = run_single_simulation(args)
        if result:
            results.append(result)

    if args.num_runs > 1 and results:
        print(f"\n{'='*25} SUMMARY OF {len(results)} RUNS {'='*25}")
        header = f"{'Run':<5} {'Topology':<10} {'R (kΩ)':<8} {'L (mH)':<8} {'C (nF)':<8} {'Vpp GT (V)':<12} {'Vpp Fit (V)':<12} {'Error (%)':<10} {'Status':<10}"
        print(header)
        print("-" * (len(header) + 2))
        for i, r in enumerate(results):
            status = "SUCCESS" if r.get('error') is not None and np.isfinite(r['error']) and r['error'] <= 10.0 else "FAILURE"
            # Handle potential NaN values from failed simulations for cleaner output
            vpp_gt_str = f"{r['Vpp_gt']:.4f}" if r.get('Vpp_gt') is not None and np.isfinite(r['Vpp_gt']) else "NaN"
            vpp_fit_str = f"{r['Vpp_fit']:.4f}" if r.get('Vpp_fit') is not None and np.isfinite(r['Vpp_fit']) else "NaN"
            error_str = f"{r['error']:.2f}" if r.get('error') is not None and np.isfinite(r['error']) else "NaN"
            
            print(f"{i+1:<5} {r['topology']:<10} {r['R']/1e3:<8.2f} {r['L']*1e3:<8.2f} {r['C']*1e9:<8.2f} {vpp_gt_str:<12} {vpp_fit_str:<12} {error_str:<10} {status:<10}")


if __name__ == '__main__':
    main() 