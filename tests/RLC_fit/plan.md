### Python Simulation Plan for E-Design Competition Task G

[cite_start]**Objective:** To create a Python script that simulates the process of identifying an unknown RLC circuit ("学习、建模" [cite: 30, 31][cite_start]) and then replicating its output for a given input signal ("推理生成...相同的输出信号" [cite: 35]). The simulation will use a "general model" fitting approach.

**Core Libraries:**
* `numpy` for numerical operations.
* `scipy.signal` for modeling LTI systems, frequency response, and time-domain simulation.
* `scipy.optimize` for the curve fitting process.
* `matplotlib.pyplot` for visualization and comparison.

---

#### **Step 1: Environment and Parameter Setup**

1.  **Import Libraries:** Import all necessary libraries listed above.
2.  **Define Global Parameters:**
    * [cite_start]Set the component value ranges according to the competition rules: R (1kΩ-10kΩ), L (1mH-10mH), C (10nF-100nF)[cite: 48].
    * Define a noise level for the simulated measurements (e.g., `NOISE_LEVEL = 0.05` for 5% noise) to make the simulation more realistic.
    * Define the frequency range for the sweep (e.g., 100 Hz to 100 kHz).

---

#### **Step 2: Create the "Ground Truth" Unknown Circuit Model**

1.  **Define a `create_ground_truth_model` function.**
    * **Inputs:** `R`, `L`, `C`, and a `topology` string ('low-pass', 'high-pass', 'band-pass').
    * **Functionality:**
        * Based on the `topology` string, construct the numerator and denominator coefficients of the continuous-time transfer function $H(s)$.
            * **'low-pass'**: Output across Capacitor C in a series RLC circuit.
                * $H(s) = \frac{1/LC}{s^2 + (R/L)s + 1/LC}$
            * **'high-pass'**: Output across Inductor L in a series RLC circuit.
                * $H(s) = \frac{s^2}{s^2 + (R/L)s + 1/LC}$
            * **'band-pass'**: Output across Resistor R in a series RLC circuit.
                * $H(s) = \frac{(R/L)s}{s^2 + (R/L)s + 1/LC}$
        * **Output:** Return a `scipy.signal.lti` object created from these numerator and denominator coefficients. This object is our "ground truth" circuit.

2.  **Instantiate a Ground Truth Model:**
    * [cite_start]Randomly select R, L, C values from within the specified ranges[cite: 48].
    * Choose a topology (e.g., start with 'low-pass').
    * Call `create_ground_truth_model` to generate the `ground_truth_lti` object. Print the chosen R, L, C, and topology.

---

#### **Step 3: Simulate the Frequency Sweep and Data Acquisition ("学习")**

1.  **Define a `simulate_sweep` function.**
    * **Inputs:** The `ground_truth_lti` object, frequency range (`f_min`, `f_max`), number of points (`num_points`), and `noise_level`.
    * **Functionality:**
        * Generate a logarithmically spaced array of frequencies within the specified range.
        * Use the `ground_truth_lti.freqresp()` method to calculate the true complex frequency response at these frequencies.
        * From the complex response, extract the true gain (magnitude) and phase (in radians).
        * **Simulate Measurement Noise:**
            * Add random noise to the gain values (e.g., `noisy_gain = true_gain * (1 + np.random.normal(0, noise_level, size=...))` ).
            * Add random noise to the phase values (e.g., `noisy_phase = true_phase + np.random.normal(0, noise_level, size=...))`.
    * **Output:** Return the frequency array, the noisy gain array, and the noisy phase array. These are our "measured" data points.

---

#### **Step 4: Fit the General Model to the "Measured" Data ("建模")**

1.  **Define the General Model Function for Fitting.**
    * **Name:** `general_model_response(f, B2, B1, B0, A1, A0)`.
    * **Inputs:** Frequency array `f` and the five unknown coefficients of the general model $H(s) = \frac{B_2 s^2 + B_1 s + B_0}{s^2 + A_1 s + A_0}$.
    * **Functionality:**
        * For each frequency `f` in the array, calculate $\omega = 2\pi f$.
        * Construct the complex theoretical response: $H(j\omega) = \frac{(B_0 - B_2 \omega^2) + j(B_1 \omega)}{(A_0 - \omega^2) + j(A_1 \omega)}$.
        * Calculate the theoretical gain (magnitude) and phase.
        * **Important for `curve_fit`:** Return a single 1D array containing all the theoretical data, e.g., `np.concatenate([gain, phase])`.
    * **Output:** A 1D numpy array of concatenated gain and phase values.

2.  **Perform the Curve Fitting.**
    * Prepare the experimental data by concatenating the noisy gain and phase arrays from Step 3 into a single 1D array, matching the format of the model function's output.
    * Provide an initial guess for the coefficients (e.g., `p0 = [1, 1, 1, 1, 1]`).
    * Call `scipy.optimize.curve_fit`, passing the model function, the frequency data, the concatenated noisy data, and the initial guess.
    * Store the returned optimal coefficients (`popt`). These define our fitted model.

---

#### **Step 5: Visualize and Compare the Fitted Model vs. Ground Truth**

1.  **Create an LTI object for the fitted model.**
    * The numerator is `[popt[0], popt[1], popt[2]]` (i.e., B₂, B₁, B₀).
    * The denominator is `[1, popt[3], popt[4]]` (i.e., 1, A₁, A₀).
    * Create a new `scipy.signal.lti` object called `fitted_lti`.

2.  **Plot the Bode Plots for Comparison.**
    * Generate a fine-grained frequency range for smooth plotting.
    * Calculate the frequency response for both `ground_truth_lti` and `fitted_lti` over this range.
    * Create a figure with two subplots (one for gain in dB, one for phase in degrees).
    * On both subplots, plot three things:
        1.  The **Ground Truth** model (smooth line).
        2.  The **"Measured" Noisy Data** (scatter plot markers `o`).
        3.  The **Fitted Model** (dashed line).
    * Add legends, titles, and labels for clarity. This plot will visually confirm the quality of the fit.

---

#### **Step 6: Simulate Real-Time Output Replication ("推理生成")**

1.  **Generate a Test Input Signal.**
    * [cite_start]Per the competition rules, use a signal other than a sine wave, for example, a square wave[cite: 41].
    * Define the signal's properties (e.g., frequency = 5 kHz, peak-to-peak = 2V).
    * Generate a time array `t` for the simulation (e.g., for a few cycles of the signal).
    * Use `scipy.signal.square` to create the input waveform `v_in`.

2.  **Simulate the Output from Both Models.**
    * Use `scipy.signal.lsim(ground_truth_lti, v_in, t)` to get the "true" output from the original circuit.
    * Use `scipy.signal.lsim(fitted_lti, v_in, t)` to get the "replicated" output from our identified model.

3.  **Visualize and Quantify the Replication Performance.**
    * Plot the input signal, the true output, and the replicated output on the same time-domain graph.
    * Calculate the peak-to-peak voltage of the true output and the replicated output.
    * [cite_start]Calculate and print the relative error between these peak-to-peak values, as specified in the rules ([cite: 35] requires this to be <= 10%).
    * This final step demonstrates the success of the entire identify-and-replicate process.