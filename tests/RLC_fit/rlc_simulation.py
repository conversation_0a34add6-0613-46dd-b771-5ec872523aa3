
import numpy as np
from scipy import signal
from scipy import optimize
import matplotlib.pyplot as plt

# Step 1: Environment and Parameter Setup

# Component value ranges (as per competition rules)
R_RANGE = (1e3, 10e3)   # 1kΩ to 10kΩ
L_RANGE = (1e-3, 10e-3) # 1mH to 10mH
C_RANGE = (10e-9, 100e-9) # 10nF to 100nF

# Simulation parameters
NOISE_LEVEL = 0.05  # 5% noise
FREQ_MIN = 100      # 100 Hz
FREQ_MAX = 100e3    # 100 kHz
NUM_POINTS = 200    # Number of points for frequency sweep

print("Step 1: Environment and parameters set up.")

# Step 2: Create the "Ground Truth" Unknown Circuit Model

def create_ground_truth_model(R, L, C, topology='low-pass'):
    """
    Creates a scipy.signal.lti model for a series RLC circuit.
    
    Args:
        R (float): Resistance in Ohms.
        L (float): Inductance in Henrys.
        C (float): Capacitance in Farads.
        topology (str): One of 'low-pass', 'high-pass', or 'band-pass'.

    Returns:
        scipy.signal.lti: The LTI model of the circuit.
    """
    # Denominator is the same for all series RLC topologies
    # s^2 + (R/L)s + 1/LC
    den = [1, R/L, 1/(L*C)]
    
    if topology == 'low-pass':
        # H(s) = (1/LC) / (s^2 + (R/L)s + 1/LC)
        num = [1/(L*C)]
    elif topology == 'high-pass':
        # H(s) = s^2 / (s^2 + (R/L)s + 1/LC)
        num = [1, 0, 0]
    elif topology == 'band-pass':
        # H(s) = (R/L)s / (s^2 + (R/L)s + 1/LC)
        num = [R/L, 0]
    else:
        raise ValueError("Invalid topology specified. Choose from 'low-pass', 'high-pass', 'band-pass'.")
        
    return signal.lti(num, den)

# Instantiate a Ground Truth Model
# Randomly select R, L, C values from within the specified ranges
R_true = np.random.uniform(R_RANGE[0], R_RANGE[1])
L_true = np.random.uniform(L_RANGE[0], L_RANGE[1])
C_true = np.random.uniform(C_RANGE[0], C_RANGE[1])

# Choose a topology
# topology_true = np.random.choice(['low-pass', 'high-pass', 'band-pass'])
topology_true = 'low-pass' # Start with low-pass for predictability

# Generate the ground truth LTI object
ground_truth_lti = create_ground_truth_model(R_true, L_true, C_true, topology_true)

print("Step 2: Ground truth model created.")
print(f"  - Topology: {topology_true}")
print(f"  - R = {R_true/1e3:.2f} kΩ")
print(f"  - L = {L_true*1e3:.2f} mH")
print(f"  - C = {C_true*1e9:.2f} nF")

# Step 3: Simulate the Frequency Sweep and Data Acquisition ("学习")

def simulate_sweep(lti_model, f_min, f_max, num_points, noise_level):
    """
    Simulates a frequency sweep to get "measured" data with noise.

    Args:
        lti_model (scipy.signal.lti): The ground truth LTI model.
        f_min (float): Minimum frequency in Hz.
        f_max (float): Maximum frequency in Hz.
        num_points (int): Number of frequency points.
        noise_level (float): The standard deviation of the noise to add.

    Returns:
        tuple: (frequencies, noisy_gain, noisy_phase)
    """
    # Generate a logarithmically spaced frequency array
    frequencies = np.logspace(np.log10(f_min), np.log10(f_max), num_points)
    w = 2 * np.pi * frequencies

    # 修复频率响应计算，freqresp 仅返回 (w, H)
    w, H_true = lti_model.freqresp(w=w)
    gain_true = np.abs(H_true)
    phase_true_rad = np.angle(H_true)

    # Convert magnitude to gain (linear) and phase to radians
    # gain_true = 10**(mag_true / 20) # This line is no longer needed

    # Add simulated measurement noise
    gain_noise = np.random.normal(0, noise_level, size=num_points)
    phase_noise = np.random.normal(0, noise_level, size=num_points)

    noisy_gain = gain_true * (1 + gain_noise)
    noisy_phase = phase_true_rad + phase_noise

    return frequencies, noisy_gain, noisy_phase

# Perform the simulated sweep to get our "measured" data
frequencies, measured_gain, measured_phase = simulate_sweep(
    ground_truth_lti, FREQ_MIN, FREQ_MAX, NUM_POINTS, NOISE_LEVEL
)

print("Step 3: Frequency sweep simulated and noisy data acquired.")

# Step 4: Fit the General Model to the "Measured" Data ("建模")

def general_model_response(f, B2, B1, B0, A1, A0):
    """
    Calculates the frequency response of a general second-order transfer function.
    H(s) = (B2*s^2 + B1*s + B0) / (s^2 + A1*s + A0)
    This function is designed to be used with scipy.optimize.curve_fit.
    """
    w = 2 * np.pi * f
    # Theoretical complex response
    numerator = (B0 - B2 * w**2) + 1j * (B1 * w)
    denominator = (A0 - w**2) + 1j * (A1 * w)
    H_jw = numerator / denominator
    
    # Theoretical gain (magnitude) and phase
    gain_theoretical = np.abs(H_jw)
    phase_theoretical = np.angle(H_jw)
    
    # `curve_fit` works with a single 1D array of data, so we concatenate them
    return np.concatenate([gain_theoretical, phase_theoretical])

# Prepare the experimental data for curve_fit
# It must be a single 1D array, matching the output of our model function
measured_data_combined = np.concatenate([measured_gain, measured_phase])

# Provide an initial guess for the coefficients [B2, B1, B0, A1, A0]
p0 = [1.0, 1.0, 1.0, 1.0, 1.0]

# Perform the curve fitting
# The first argument to curve_fit is the function that we want to fit, 
# in our case, this is general_model_response.
# The second argument is the independent variable, in our case, the frequency f.
# The third argument is the dependent variable, in our case, the combined measured data.
# The fourth argument is the initial guess for the parameters.
popt, pcov = optimize.curve_fit(
    general_model_response, 
    frequencies, 
    measured_data_combined, 
    p0=p0
)

# Extract the optimal coefficients
B2_fit, B1_fit, B0_fit, A1_fit, A0_fit = popt

print("Step 4: General model fitted to the data.")
print(f"  - Fitted Coefficients (B2, B1, B0, A1, A0):\n    {popt}")

# Step 5: Visualize and Compare the Fitted Model vs. Ground Truth

# 创建拟合后的 LTI 对象
fitted_lti = signal.lti([B2_fit, B1_fit, B0_fit], [1, A1_fit, A0_fit])

# 生成用于绘图的更密集的频率点
freq_fine = np.logspace(np.log10(FREQ_MIN), np.log10(FREQ_MAX), 1000)

# 计算频率响应
_, H_gt = ground_truth_lti.freqresp(w=2 * np.pi * freq_fine)
_, H_fit = fitted_lti.freqresp(w=2 * np.pi * freq_fine)

gain_gt = np.abs(H_gt)
phase_gt = np.angle(H_gt)

gain_fit = np.abs(H_fit)
phase_fit = np.angle(H_fit)

# 绘制 Bode 图
fig, (ax_mag, ax_phase) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)

# 幅值（dB）
ax_mag.semilogx(freq_fine, 20 * np.log10(gain_gt), label='Ground Truth')
ax_mag.scatter(frequencies, 20 * np.log10(measured_gain), color='red', marker='o', s=10, label='Measured')
ax_mag.semilogx(freq_fine, 20 * np.log10(gain_fit), linestyle='--', label='Fitted Model')
ax_mag.set_ylabel('Gain (dB)')
ax_mag.legend()
ax_mag.grid(True, which='both', ls='--', lw=0.5)

# 相位（度）
ax_phase.semilogx(freq_fine, np.degrees(phase_gt), label='Ground Truth')
ax_phase.scatter(frequencies, np.degrees(measured_phase), color='red', marker='o', s=10, label='Measured')
ax_phase.semilogx(freq_fine, np.degrees(phase_fit), linestyle='--', label='Fitted Model')
ax_phase.set_ylabel('Phase (deg)')
ax_phase.set_xlabel('Frequency (Hz)')
ax_phase.legend()
ax_phase.grid(True, which='both', ls='--', lw=0.5)

plt.tight_layout()
plt.show()

print("Step 5: 拟合模型与真实模型对比完成并已绘制 Bode 图。")

# Step 6: Simulate Real-Time Output Replication ("推理生成")

test_freq = 5e3  # 5 kHz
amp = 1.0        # 1 V 振幅，峰峰值 2 V
periods = 5      # 模拟 5 个周期

t_total = periods / test_freq
num_samples = 10000

t = np.linspace(0, t_total, num_samples)

# 生成方波输入
v_in = amp * signal.square(2 * np.pi * test_freq * t)

# 通过两种模型进行仿真
_, v_out_true, _ = signal.lsim(ground_truth_lti, U=v_in, T=t)
_, v_out_fit, _ = signal.lsim(fitted_lti, U=v_in, T=t)

# 计算峰峰值及相对误差
ptp_true = np.ptp(v_out_true)
ptp_fit = np.ptp(v_out_fit)
rel_error = np.abs(ptp_true - ptp_fit) / ptp_true * 100

print("Step 6: 推理生成测试完成。")
print(f"  - True Output Vpp: {ptp_true:.4f} V")
print(f"  - Fitted Output Vpp: {ptp_fit:.4f} V")
print(f"  - Relative Error: {rel_error:.2f}%")

# 绘制时域波形
plt.figure(figsize=(10, 4))
plt.plot(t, v_in, label='Input (Square Wave)')
plt.plot(t, v_out_true, label='True Output')
plt.plot(t, v_out_fit, linestyle='--', label='Fitted Output')
plt.xlabel('Time (s)')
plt.ylabel('Voltage (V)')
plt.title('Time-Domain Response Comparison')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

print("完成整个识别与复制流程！")
