#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <time.h>
#include <string.h>

// 定义复数结构
typedef struct {
    double real;
    double imag;
} complex_t;

// 定义RLC电路参数
typedef struct {
    double R;  // 电阻 (Ohms)
    double L;  // 电感 (<PERSON><PERSON>)
    double C;  // 电容 (Farads)
} rlc_params_t;

// 定义 transfer function H(s) = (B2*s^2 + B1*s + B0) / (s^2 + A1*s + A0)
typedef struct {
    double B2, B1, B0;
    double A1, A0;
} tf_params_t;

// 定义电路拓扑
typedef enum {
    LOW_PASS,
    HIGH_PASS,
    BAND_PASS
} topology_t;

// 常量定义
#define PI 3.14159265358979323846
#define FREQ_MIN 100.0      // 100 Hz
#define FREQ_MAX 100000.0   // 100 kHz
#define NUM_POINTS 200      // 频率点数
#define NOISE_LEVEL 0.05    // 噪声水平 (5%)
#define TEST_FREQ 5000.0    // 测试信号频率 (5 kHz)
#define NUM_SAMPLES 10000   // 时域样本数

// 函数声明
complex_t complex_add(complex_t a, complex_t b);
complex_t complex_sub(complex_t a, complex_t b);
complex_t complex_mul(complex_t a, complex_t b);
complex_t complex_div(complex_t a, complex_t b);
double complex_abs(complex_t z);
double complex_angle(complex_t z);

// 生成随机RLC参数
rlc_params_t generate_random_rlc();
// 创建RLC电路的传递函数参数
tf_params_t create_rlc_tf(rlc_params_t params, topology_t topology);
// 计算传递函数在给定频率下的响应
complex_t tf_response(tf_params_t tf, double freq);
// 模拟频率扫描
void simulate_sweep(tf_params_t tf, double* frequencies, double* gain, double* phase, int num_points);
// 添加噪声到数据
void add_noise(double* data, int size, double noise_level);
// 最小二乘法拟合
tf_params_t least_squares_fit(double* frequencies, double* measured_gain, double* measured_phase, int num_points);
// 计算均方误差
double calculate_mse(double* actual, double* predicted, int size);
// 时域响应仿真
void simulate_time_domain(tf_params_t tf_true, tf_params_t tf_fitted, double** v_in, double** v_out_true, double** v_out_fitted, int* num_samples);
// 生成方波信号
void generate_square_wave(double** v_in, double** time, int* num_samples, double freq, double duration);
// 计算峰峰值
double calculate_peak_to_peak(double* signal, int size);

int main() {
    printf("RLC 电路识别和复制仿真 (C语言实现)\n");
    printf("=====================================\n");
    
    // 设置随机种子
    srand(time(NULL));
    
    // 步骤1: 创建真实的RLC电路模型
    printf("步骤1: 创建真实的RLC电路模型\n");
    rlc_params_t true_params = generate_random_rlc();
    topology_t topology = LOW_PASS;  // 固定为低通滤波器
    
    printf("  - 拓扑结构: 低通滤波器\n");
    printf("  - 真实电阻 R = %.2f kΩ\n", true_params.R / 1000.0);
    printf("  - 真实电感 L = %.2f mH\n", true_params.L * 1000.0);
    printf("  - 真实电容 C = %.2f nF\n", true_params.C * 1e9);
    
    tf_params_t true_tf = create_rlc_tf(true_params, topology);
    printf("  - 真实传递函数系数 B2=%.6f, B1=%.6f, B0=%.6f, A1=%.6f, A0=%.6f\n",
           true_tf.B2, true_tf.B1, true_tf.B0, true_tf.A1, true_tf.A0);
    
    // 步骤2: 模拟频率扫描以获取"测量"数据
    printf("\n步骤2: 模拟频率扫描以获取\"测量\"数据\n");
    double* frequencies = (double*)malloc(NUM_POINTS * sizeof(double));
    double* true_gain = (double*)malloc(NUM_POINTS * sizeof(double));
    double* true_phase = (double*)malloc(NUM_POINTS * sizeof(double));
    
    simulate_sweep(true_tf, frequencies, true_gain, true_phase, NUM_POINTS);
    
    // 添加噪声模拟实际测量
    double* measured_gain = (double*)malloc(NUM_POINTS * sizeof(double));
    double* measured_phase = (double*)malloc(NUM_POINTS * sizeof(double));
    
    memcpy(measured_gain, true_gain, NUM_POINTS * sizeof(double));
    memcpy(measured_phase, true_phase, NUM_POINTS * sizeof(double));
    
    add_noise(measured_gain, NUM_POINTS, NOISE_LEVEL);
    add_noise(measured_phase, NUM_POINTS, NOISE_LEVEL);
    
    printf("  - 已获取 %d 个频率点的数据\n", NUM_POINTS);
    printf("  - 已添加 %.1f%% 的噪声\n", NOISE_LEVEL * 100);
    
    // 步骤3: 使用最小二乘法拟合模型
    printf("\n步骤3: 使用最小二乘法拟合模型\n");
    tf_params_t fitted_tf = least_squares_fit(frequencies, measured_gain, measured_phase, NUM_POINTS);
    printf("  - 拟合传递函数系数 B2=%.6f, B1=%.6f, B0=%.6f, A1=%.6f, A0=%.6f\n",
           fitted_tf.B2, fitted_tf.B1, fitted_tf.B0, fitted_tf.A1, fitted_tf.A0);
    
    // 步骤4: 验证拟合结果
    printf("\n步骤4: 验证拟合结果\n");
    double* fitted_gain = (double*)malloc(NUM_POINTS * sizeof(double));
    double* fitted_phase = (double*)malloc(NUM_POINTS * sizeof(double));
    
    for (int i = 0; i < NUM_POINTS; i++) {
        complex_t response = tf_response(fitted_tf, frequencies[i]);
        fitted_gain[i] = complex_abs(response);
        fitted_phase[i] = complex_angle(response);
    }
    
    double gain_mse = calculate_mse(true_gain, fitted_gain, NUM_POINTS);
    double phase_mse = calculate_mse(true_phase, fitted_phase, NUM_POINTS);
    
    printf("  - 增益均方误差: %.6f\n", gain_mse);
    printf("  - 相位均方误差: %.6f\n", phase_mse);
    
    // 步骤5: 时域响应仿真
    printf("\n步骤5: 时域响应仿真\n");
    double* v_in = NULL;
    double* v_out_true = NULL;
    double* v_out_fitted = NULL;
    int num_samples = 0;
    
    simulate_time_domain(true_tf, fitted_tf, &v_in, &v_out_true, &v_out_fitted, &num_samples);
    
    double vpp_true = calculate_peak_to_peak(v_out_true, num_samples);
    double vpp_fitted = calculate_peak_to_peak(v_out_fitted, num_samples);
    double relative_error = fabs(vpp_true - vpp_fitted) / vpp_true * 100;
    
    printf("  - 真实输出峰峰值: %.4f V\n", vpp_true);
    printf("  - 拟合输出峰峰值: %.4f V\n", vpp_fitted);
    printf("  - 相对误差: %.2f%%\n", relative_error);
    
    if (relative_error <= 10.0) {
        printf("  - 结果: 成功！相对误差在10%%以内。\n");
    } else {
        printf("  - 结果: 失败！相对误差超过10%%。\n");
    }
    
    // 输出参数比较 (注意：参数被归一化，比较的是相对关系而非绝对值)
    printf("\n参数比较 (注意: 参数被归一化处理):\n");
    printf("  - 真实 B0: %.2e, 拟合 B0: %.2e\n", 
           true_tf.B0, fitted_tf.B0);
    printf("  - 真实 A1: %.2e, 拟合 A1: %.2e\n", 
           true_tf.A1, fitted_tf.A1);
    printf("  - 真实 A0: %.2e, 拟合 A0: %.2e\n", 
           true_tf.A0, fitted_tf.A0);
    
    // 释放内存
    free(frequencies);
    free(true_gain);
    free(true_phase);
    free(measured_gain);
    free(measured_phase);
    free(fitted_gain);
    free(fitted_phase);
    free(v_in);
    free(v_out_true);
    free(v_out_fitted);
    
    printf("\n仿真完成。\n");
    return 0;
}

// 复数运算函数
complex_t complex_add(complex_t a, complex_t b) {
    complex_t result;
    result.real = a.real + b.real;
    result.imag = a.imag + b.imag;
    return result;
}

complex_t complex_sub(complex_t a, complex_t b) {
    complex_t result;
    result.real = a.real - b.real;
    result.imag = a.imag - b.imag;
    return result;
}

complex_t complex_mul(complex_t a, complex_t b) {
    complex_t result;
    result.real = a.real * b.real - a.imag * b.imag;
    result.imag = a.real * b.imag + a.imag * b.real;
    return result;
}

complex_t complex_div(complex_t a, complex_t b) {
    complex_t result;
    double denominator = b.real * b.real + b.imag * b.imag;
    if (denominator == 0) {
        result.real = 0;
        result.imag = 0;
        return result;
    }
    result.real = (a.real * b.real + a.imag * b.imag) / denominator;
    result.imag = (a.imag * b.real - a.real * b.imag) / denominator;
    return result;
}

double complex_abs(complex_t z) {
    return sqrt(z.real * z.real + z.imag * z.imag);
}

double complex_angle(complex_t z) {
    return atan2(z.imag, z.real);
}

// 生成随机RLC参数
rlc_params_t generate_random_rlc() {
    rlc_params_t params;
    // 根据竞赛规则: R (1kΩ-10kΩ), L (1mH-10mH), C (10nF-100nF)
    params.R = 1000.0 + (rand() / (double)RAND_MAX) * 9000.0;     // 1kΩ to 10kΩ
    params.L = 0.001 + (rand() / (double)RAND_MAX) * 0.009;       // 1mH to 10mH
    params.C = 10e-9 + (rand() / (double)RAND_MAX) * 90e-9;       // 10nF to 100nF
    return params;
}

// 创建RLC电路的传递函数参数
tf_params_t create_rlc_tf(rlc_params_t params, topology_t topology) {
    tf_params_t tf;
    
    // 对于串联RLC电路，分母总是: s^2 + (R/L)s + 1/LC
    tf.A1 = params.R / params.L;
    tf.A0 = 1.0 / (params.L * params.C);
    
    switch (topology) {
        case LOW_PASS:  // 输出在电容上
            // H(s) = (1/LC) / (s^2 + (R/L)s + 1/LC)
            tf.B2 = 0.0;
            tf.B1 = 0.0;
            tf.B0 = 1.0 / (params.L * params.C);
            break;
        case HIGH_PASS:  // 输出在电感上
            // H(s) = s^2 / (s^2 + (R/L)s + 1/LC)
            tf.B2 = 1.0;
            tf.B1 = 0.0;
            tf.B0 = 0.0;
            break;
        case BAND_PASS:  // 输出在电阻上
            // H(s) = (R/L)s / (s^2 + (R/L)s + 1/LC)
            tf.B2 = 0.0;
            tf.B1 = params.R / params.L;
            tf.B0 = 0.0;
            break;
    }
    
    return tf;
}

// 计算传递函数在给定频率下的响应
complex_t tf_response(tf_params_t tf, double freq) {
    complex_t s, s_squared, numerator, denominator, response;
    double w = 2.0 * PI * freq;
    
    // s = jω
    s.real = 0.0;
    s.imag = w;
    
    // s^2
    s_squared = complex_mul(s, s);
    
    // 分子: B2*s^2 + B1*s + B0
    complex_t b2_s2 = complex_mul((complex_t){tf.B2, 0.0}, s_squared);
    complex_t b1_s = complex_mul((complex_t){tf.B1, 0.0}, s);
    complex_t b0_term = (complex_t){tf.B0, 0.0};
    
    numerator = complex_add(b2_s2, b1_s);
    numerator = complex_add(numerator, b0_term);
    
    // 分母: s^2 + A1*s + A0
    complex_t a1_s = complex_mul((complex_t){tf.A1, 0.0}, s);
    complex_t a0_term = (complex_t){tf.A0, 0.0};
    
    denominator = complex_add(s_squared, a1_s);
    denominator = complex_add(denominator, a0_term);
    
    // H(s) = numerator / denominator
    response = complex_div(numerator, denominator);
    
    return response;
}

// 模拟频率扫描
void simulate_sweep(tf_params_t tf, double* frequencies, double* gain, double* phase, int num_points) {
    // 生成对数间隔的频率点
    double log_min = log10(FREQ_MIN);
    double log_max = log10(FREQ_MAX);
    
    for (int i = 0; i < num_points; i++) {
        double log_freq = log_min + (log_max - log_min) * i / (num_points - 1);
        frequencies[i] = pow(10.0, log_freq);
        
        complex_t response = tf_response(tf, frequencies[i]);
        gain[i] = complex_abs(response);
        phase[i] = complex_angle(response);
    }
}

// 添加噪声到数据
void add_noise(double* data, int size, double noise_level) {
    for (int i = 0; i < size; i++) {
        // 生成高斯噪声
        double noise = ((double)rand() / RAND_MAX) * 2.0 - 1.0;  // [-1, 1]
        noise *= noise_level;
        
        // 对增益添加乘性噪声，对相位添加加性噪声
        data[i] *= (1.0 + noise);  // 乘性噪声
    }
}

// 简化的最小二乘法拟合（使用梯度下降）
tf_params_t least_squares_fit(double* frequencies, double* measured_gain, double* measured_phase, int num_points) {
    tf_params_t tf = {0.0, 0.0, 1.0, 1.0, 1.0};  // 初始猜测 (B2=0, B1=0 for low-pass)
    double learning_rate = 1e-6;
    int max_iterations = 2000;
    
    // 简化实现：使用固定的学习率和迭代次数
    for (int iter = 0; iter < max_iterations; iter++) {
        // 计算梯度（简化版）
        double grad_B2 = 0, grad_B1 = 0, grad_B0 = 0, grad_A1 = 0, grad_A0 = 0;
        
        for (int i = 0; i < num_points; i++) {
            complex_t predicted = tf_response(tf, frequencies[i]);
            double pred_gain = complex_abs(predicted);
            double pred_phase = complex_angle(predicted);
            
            // 计算误差
            double gain_error = pred_gain - measured_gain[i];
            double phase_error = pred_phase - measured_phase[i];
            
            // 累积梯度
            grad_B2 += 2 * gain_error * pred_gain * 0.000001;  // 缩放梯度
            grad_B1 += 2 * gain_error * pred_gain * 0.000001;
            grad_B0 += 2 * gain_error * pred_gain * 0.00001;
            grad_A1 += 2 * phase_error * 0.00001;
            grad_A0 += 2 * phase_error * 0.0001;
        }
        
        // 更新参数 (带阻尼)
        tf.B2 -= learning_rate * grad_B2;
        tf.B1 -= learning_rate * grad_B1;
        tf.B0 -= learning_rate * grad_B0;
        tf.A1 -= learning_rate * grad_A1;
        tf.A0 -= learning_rate * grad_A0;
        
        // 限制参数范围以提高稳定性
        if (fabs(tf.B2) > 1e6) tf.B2 = 1e6 * (tf.B2 > 0 ? 1 : -1);
        if (fabs(tf.B1) > 1e6) tf.B1 = 1e6 * (tf.B1 > 0 ? 1 : -1);
        if (fabs(tf.B0) > 1e10) tf.B0 = 1e10 * (tf.B0 > 0 ? 1 : -1);
        if (fabs(tf.A1) > 1e7) tf.A1 = 1e7 * (tf.A1 > 0 ? 1 : -1);
        if (fabs(tf.A0) > 1e10) tf.A0 = 1e10 * (tf.A0 > 0 ? 1 : -1);
    }
    
    return tf;
}

// 计算均方误差
double calculate_mse(double* actual, double* predicted, int size) {
    double sum = 0.0;
    for (int i = 0; i < size; i++) {
        double diff = actual[i] - predicted[i];
        sum += diff * diff;
    }
    return sum / size;
}

// 生成方波信号
void generate_square_wave(double** v_in, double** time, int* num_samples, double freq, double duration) {
    *num_samples = NUM_SAMPLES;
    *v_in = (double*)malloc(*num_samples * sizeof(double));
    *time = (double*)malloc(*num_samples * sizeof(double));
    
    double period = 1.0 / freq;
    double dt = duration / *num_samples;
    
    for (int i = 0; i < *num_samples; i++) {
        (*time)[i] = i * dt;
        // 方波: 高电平为1V，低电平为-1V
        (*v_in)[i] = (fmod((*time)[i], period) < period/2) ? 1.0 : -1.0;
    }
}

// 简化的时域仿真（欧拉方法）
void simulate_time_domain_lsim(tf_params_t tf, double* v_in, double* v_out, double* time, int num_samples) {
    // 使用简化的二阶系统仿真
    // d2y/dt2 + A1*dy/dt + A0*y = B2*d2u/dt2 + B1*du/dt + B0*u
    
    // 初始化状态
    double y_prev2 = 0, y_prev1 = 0;
    double u_prev2 = 0, u_prev1 = 0;
    double dt = time[1] - time[0];
    
    // 归一化系数以提高数值稳定性
    double scale = (tf.A0 != 0) ? sqrt(tf.A0) : 1.0;
    tf_params_t norm_tf = tf;
    if (scale != 0) {
        norm_tf.B2 /= (scale * scale);
        norm_tf.B1 /= scale;
        norm_tf.B0 /= 1.0;  // B0保持不变
        norm_tf.A1 /= scale;
        norm_tf.A0 /= (scale * scale);
    }
    
    for (int i = 0; i < num_samples; i++) {
        double u = v_in[i];
        
        // 计算输入的导数（简化）
        double du_dt = (i > 0) ? (u - u_prev1) / dt : 0;
        double d2u_dt2 = (i > 1) ? (du_dt - (u_prev1 - u_prev2) / dt) / dt : 0;
        
        // 计算输出（使用差分方程近似）
        // 重新排列方程: y = (B2*d2u/dt2 + B1*du/dt + B0*u - A1*dy/dt - A0*y) / 1
        // 由于我们不能直接使用 dy/dt，我们用差分近似: dy/dt ≈ (y_prev1 - y_prev2)/dt
        double dy_dt = (i > 1) ? (y_prev1 - y_prev2) / dt : 0;
        double y = (norm_tf.B2*d2u_dt2 + norm_tf.B1*du_dt + norm_tf.B0*u - norm_tf.A1*dy_dt - norm_tf.A0*y_prev1);
        
        // 限制输出范围以防止数值溢出
        if (y > 10.0) y = 10.0;
        if (y < -10.0) y = -10.0;
        
        v_out[i] = y;
        
        // 更新状态
        y_prev2 = y_prev1;
        y_prev1 = y;
        u_prev2 = u_prev1;
        u_prev1 = u;
    }
}

// 时域响应仿真
void simulate_time_domain(tf_params_t tf_true, tf_params_t tf_fitted, double** v_in, double** v_out_true, double** v_out_fitted, int* num_samples) {
    double duration = 5.0 / TEST_FREQ;  // 5个周期
    
    double* time = NULL;
    generate_square_wave(v_in, &time, num_samples, TEST_FREQ, duration);
    
    *v_out_true = (double*)malloc(*num_samples * sizeof(double));
    *v_out_fitted = (double*)malloc(*num_samples * sizeof(double));
    
    // 仿真真实系统
    simulate_time_domain_lsim(tf_true, *v_in, *v_out_true, time, *num_samples);
    
    // 仿真拟合系统
    simulate_time_domain_lsim(tf_fitted, *v_in, *v_out_fitted, time, *num_samples);
    
    free(time);
}

// 计算峰峰值
double calculate_peak_to_peak(double* signal, int size) {
    double min = signal[0];
    double max = signal[0];
    
    for (int i = 1; i < size; i++) {
        if (signal[i] < min) min = signal[i];
        if (signal[i] > max) max = signal[i];
    }
    
    return max - min;
}