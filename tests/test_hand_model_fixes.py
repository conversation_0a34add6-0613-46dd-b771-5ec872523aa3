#!/usr/bin/env python3
"""
测试 hand_model.py 修复的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel, HandModelType


def test_surface_points_fix():
    """测试问题3：类型转换问题修复"""
    print("测试问题3：类型转换问题修复...")
    try:
        hand_model = HandModel(
            hand_model_type=HandModelType.LEAP, 
            device='cpu', 
            n_surface_points=100
        )
        print("✓ 表面点采样成功，类型转换问题已修复")
        return True
    except Exception as e:
        print(f"✗ 表面点采样失败: {e}")
        return False


def test_batch_size_check_fix():
    """测试问题4：批次大小检查逻辑错误修复"""
    print("测试问题4：批次大小检查逻辑错误修复...")
    try:
        hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')
        
        # 设置正确的手部姿态
        batch_size = 2
        hand_pose = torch.zeros(batch_size, 3 + hand_model.n_dofs + 4)  # 3 trans + n_dofs + 4 quat
        hand_model.set_parameters(hand_pose)
        
        # 测试错误的批次大小
        wrong_batch_x = torch.randn(3, 100, 3)  # 错误的批次大小
        
        try:
            hand_model.cal_distance(wrong_batch_x)
            print("✗ 应该抛出异常但没有抛出")
            return False
        except ValueError as e:
            if "batch size mismatch" in str(e):
                print("✓ 批次大小检查正确抛出异常")
                return True
            else:
                print(f"✗ 抛出了错误类型的异常: {e}")
                return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_condition_logic_fix():
    """测试问题5：变量命名不一致修复"""
    print("测试问题5：条件判断逻辑修复...")
    try:
        hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu')
        
        # 设置手部姿态
        batch_size = 2
        hand_pose = torch.zeros(batch_size, 3 + hand_model.n_dofs + 4)
        hand_model.set_parameters(hand_pose)
        
        # 测试获取接触候选点
        contact_candidates = hand_model.get_contact_candidates()
        print(f"✓ 接触候选点形状: {contact_candidates.shape}")
        
        # 测试获取穿透关键点
        penetration_keypoints = hand_model.get_penetration_keypoints()
        print(f"✓ 穿透关键点形状: {penetration_keypoints.shape}")
        
        return True
    except Exception as e:
        print(f"✗ 条件判断逻辑测试失败: {e}")
        return False


def test_error_handling_fix():
    """测试问题6：错误处理不完整修复"""
    print("测试问题6：错误处理机制修复...")
    try:
        hand_model = HandModel(hand_model_type=HandModelType.LEAP, device='cpu', n_surface_points=100)
        
        # 设置手部姿态
        batch_size = 1
        hand_pose = torch.zeros(batch_size, 3 + hand_model.n_dofs + 4)
        hand_model.set_parameters(hand_pose)
        
        # 测试获取表面点
        surface_points = hand_model.get_surface_points()
        print(f"✓ 表面点形状: {surface_points.shape}")
        print("✓ 错误处理机制工作正常")
        
        return True
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_device_consistency_fix():
    """测试问题7：设备一致性问题修复"""
    print("测试问题7：设备一致性问题修复...")
    try:
        # 选择合适的设备进行测试
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        hand_model = HandModel(hand_model_type=HandModelType.LEAP, device=device)

        # 测试设备一致性方法
        tensor1 = torch.randn(2, 3)
        tensor2 = torch.randn(3, 4)

        result1, result2 = hand_model._ensure_device_consistency(tensor1, tensor2)

        assert result1.device.type == device
        assert result2.device.type == device
        print("✓ 设备一致性方法工作正常")

        # 测试在实际使用中的设备一致性
        batch_size = 1
        hand_pose = torch.zeros(batch_size, 3 + hand_model.n_dofs + 4)
        hand_model.set_parameters(hand_pose)

        # 测试场景点云处理 - 只在CUDA可用时测试penetration，因为torchsdf需要CUDA
        if torch.cuda.is_available():
            scene_pc = torch.randn(100, 4)
            result = hand_model(hand_pose, scene_pc=scene_pc, with_penetration=True)
            print("✓ 场景点云处理设备一致性正常（CUDA）")
        else:
            # 在CPU上只测试不需要torchsdf的功能
            scene_pc = torch.randn(100, 4)
            result = hand_model(hand_pose, scene_pc=scene_pc, with_contact_candidates=False, with_penetration=False)
            print("✓ 场景点云处理设备一致性正常（CPU，跳过penetration测试因为torchsdf需要CUDA）")

        return True
    except Exception as e:
        print(f"✗ 设备一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("开始测试 hand_model.py 修复...")
    print("=" * 50)
    
    tests = [
        test_surface_points_fix,
        test_batch_size_check_fix,
        test_condition_logic_fix,
        test_error_handling_fix,
        test_device_consistency_fix,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复都成功了！")
        return True
    else:
        print("⚠️  部分修复需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
