"""
DDPM Lightning 模型对比测试脚本

测试目标：验证重构后的 DDPMLightning 模型与原始版本的功能一致性
- 重构版本：models/diffuser_lightning.py
- 原始版本：models/diffuser_lightning_old.py

测试内容：
1. 模型初始化对比
2. 训练过程数据流对比
3. 推理过程数据流对比
4. 输出结果一致性验证
5. 关键中间层输出对比
6. 梯度计算一致性
7. 损失函数值对比
8. 扩散过程核心方法对比
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from omegaconf import OmegaConf
import warnings
warnings.filterwarnings("ignore")

# 导入模型
from models.diffuser_lightning import DDPMLightning as DDPMLightningRefactored
from models.diffuser_lightning_old import DDPMLightning as DDPMLightningOriginal

# 导入工具函数
from utils.hand_helper import process_hand_pose, process_hand_pose_test

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DDPMComparisonTester:
    """DDPM Lightning 模型对比测试器"""

    def __init__(self, config_path: str = None, tolerance: float = 1e-5):
        """
        初始化测试器

        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
            tolerance: 数值比较容差
        """
        self.tolerance = tolerance
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")

        # 创建测试配置
        self.cfg = self._create_test_config()

        # 初始化模型
        self.model_refactored = None
        self.model_original = None

        # 测试结果存储
        self.test_results = {}

    def _create_test_config(self) -> OmegaConf:
        """创建测试配置"""
        cfg = OmegaConf.create({
            # 模型配置
            'decoder': {
                'name': 'unet',
                'rot_type': 'r6d',
                'd_model': 512,
                'time_embed_mult': 2,
                'nblocks': 4,
                'resblock_dropout': 0.0,
                'transformer_num_heads': 8,
                'transformer_dim_head': 64,
                'transformer_dropout': 0.1,
                'transformer_depth': 1,
                'transformer_mult_ff': 2,
                'context_dim': 512,
                'backbone': {
                    'name': 'pointnet2',
                    'use_pooling': False,
                    'layer1': {
                        'npoint': 2048,
                        'radius_list': [0.04],
                        'nsample_list': [64],
                        'mlp_list': [3, 64, 64, 128]
                    },
                    'layer2': {
                        'npoint': 1024,
                        'radius_list': [0.1],
                        'nsample_list': [32],
                        'mlp_list': [128, 128, 128, 256]
                    },
                    'layer3': {
                        'npoint': 512,
                        'radius_list': [0.2],
                        'nsample_list': [32],
                        'mlp_list': [256, 128, 128, 256]
                    },
                    'layer4': {
                        'npoint': 128,
                        'radius_list': [0.3],
                        'nsample_list': [16],
                        'mlp_list': [256, 512, 512]
                    },
                    'use_xyz': True,
                    'normalize_xyz': True
                },
                'use_position_embedding': False,
                'use_text_condition': True,
                'text_dropout_prob': 0.1
            },

            # 损失函数配置
            'criterion': {
                'mode': 'camera_centric_scene_mean_normalized',
                'device': 'cuda:0',
                'rot_type': 'r6d',
                'hand_model': {
                    'n_surface_points': 1024,
                    'rot_type': 'r6d'
                },
                'loss_weights': {
                    'translation': 10.0,
                    'rotation': 10.0,
                    'qpos': 1.0,
                    'neg_loss': 0.5,
                    'hand_chamfer': 0.0
                },
                'multi_grasp': {
                    'loss_aggregation': 'mean',
                    'use_consistency_loss': True,
                    'consistency_loss_weight': 0.1,
                    'diversity_loss_weight': 0.05
                },
                'cost_weights': {
                    'translation': 2.0,
                    'rotation': 2.0,
                    'qpos': 1.0
                },
                'scale': 0.1,
                'q1': {
                    'lambda_torque': 10,
                    'm': 8,
                    'mu': 1,
                    'nms': True,
                    'thres_contact': 0.01,
                    'thres_pen': 0.005,
                    'thres_tpen': 0.01,
                    'rot_type': 'r6d'
                }
            },

            # 扩散模型配置
            'rot_type': 'r6d',
            'mode': 'camera_centric_scene_mean_normalized',
            'batch_size': 4,
            'steps': 100,
            'pred_x0': True,
            'rand_t_type': 'half',
            'schedule_cfg': {
                'beta': [0.0001, 0.01],
                'beta_schedule': 'linear',
                's': 0.008
            },
            'use_cfg': True,
            'guidance_scale': 7.5,
            'use_negative_guidance': True,
            'negative_guidance_scale': 1.0,
            'print_freq': 250,

            # 优化器配置
            'optimizer': {
                'name': 'adam',
                'lr': 0.0001,
                'weight_decay': 0.0001
            },
            'scheduler': {
                'name': 'cosine',
                't_max': 1000,
                'min_lr': 1e-5
            },

            # 其他配置
            'loss_type': 'l2',
            'out_sigmoid': False,
            'use_score': False,
            'score_pretrain': False
        })

        return cfg

    def _create_virtual_dataset(self, batch_size: int = 4, num_grasps: int = 8) -> Dict[str, Any]:
        """
        创建虚拟多抓取数据集

        Args:
            batch_size: 批次大小
            num_grasps: 每个场景的抓取数量

        Returns:
            batch: 虚拟数据批次
        """
        device = self.device

        # 场景点云 [B, M, 6] (XYZ + RGB)
        M = 2048  # 点云大小
        scene_pc = torch.randn(batch_size, M, 6, device=device)

        # 手部模型姿态 [B, N, 23] (P(3) + Joints(16) + Q_wxyz(4))
        hand_model_pose = torch.randn(batch_size, num_grasps, 23, device=device)

        # SE(3) 变换矩阵 [B, N, 4, 4]
        se3 = torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).repeat(batch_size, num_grasps, 1, 1)
        se3[:, :, :3, 3] = torch.randn(batch_size, num_grasps, 3, device=device)  # 随机平移

        # 对象掩码
        object_mask = [torch.randint(0, 2, (M,), dtype=torch.bool, device=device) for _ in range(batch_size)]

        # 对象顶点和面（变长）
        obj_verts = [torch.randn(np.random.randint(100, 500), 3, device=device) for _ in range(batch_size)]
        obj_faces = [torch.randint(0, len(verts), (np.random.randint(50, 200), 3), device=device)
                    for verts in obj_verts]

        # 文本提示
        positive_prompt = [f"object_{i}" for i in range(batch_size)]
        negative_prompts = [[f"neg_object_{j}" for j in range(3)] for _ in range(batch_size)]

        batch = {
            'obj_code': [f"obj_{i}_uid_{i}" for i in range(batch_size)],
            'scene_pc': scene_pc,
            'object_mask': object_mask,
            'hand_model_pose': hand_model_pose,
            'se3': se3,
            'scene_id': [f"scene_{i}" for i in range(batch_size)],
            'category_id_from_object_index': torch.randint(0, 10, (batch_size,), device=device),
            'depth_view_index': torch.randint(0, 5, (batch_size,), device=device),
            'obj_verts': obj_verts,
            'obj_faces': obj_faces,
            'positive_prompt': positive_prompt,
            'negative_prompts': negative_prompts
        }

        return batch

    def initialize_models(self):
        """初始化两个模型"""
        logger.info("初始化模型...")

        try:
            # 初始化重构版本
            self.model_refactored = DDPMLightningRefactored(self.cfg).to(self.device)
            logger.info("✓ 重构版本模型初始化成功")
        except Exception as e:
            logger.error(f"✗ 重构版本模型初始化失败: {e}")
            raise

        try:
            # 初始化原始版本
            self.model_original = DDPMLightningOriginal(self.cfg).to(self.device)
            logger.info("✓ 原始版本模型初始化成功")
        except Exception as e:
            logger.error(f"✗ 原始版本模型初始化失败: {e}")
            raise

        # 同步模型参数
        self._sync_model_parameters()

    def _sync_model_parameters(self):
        """同步两个模型的参数以确保公平比较"""
        logger.info("同步模型参数...")

        # 暂时跳过参数同步，使用独立的随机初始化
        logger.info("使用独立的随机初始化（暂时跳过参数同步）")
        torch.manual_seed(42)
        self.model_original = DDPMLightningOriginal(self.cfg).to(self.device)
        torch.manual_seed(42)
        self.model_refactored = DDPMLightningRefactored(self.cfg).to(self.device)
        logger.info("✓ 模型参数初始化完成")

    def _record_test_result(self, name: str, success: bool, details: str = ""):
        """记录测试结果并打印日志"""
        self.test_results[name] = success
        if success:
            logger.info(f"   ✓ {name} 测试通过")
        else:
            message = f"   ✗ {name} 测试失败"
            if details:
                message += f" - {details}"
            logger.error(message)

    def _compare_tensors(self, name: str, t1: torch.Tensor, t2: torch.Tensor):
        """比较两个张量并记录结果"""
        diff = torch.abs(t1 - t2).max().item()
        logger.info(f"   {name} 最大差异: {diff:.2e}")
        self._record_test_result(name, diff < self.tolerance, f"差异 {diff:.2e} > 容差 {self.tolerance}")

    def test_diffusion_core_methods(self, batch_size: int = 2, num_grasps: int = 4):
        """测试扩散过程核心方法的一致性"""
        batch = self._create_virtual_dataset(batch_size, num_grasps)
        batch = process_hand_pose(batch, rot_type=self.cfg.rot_type, mode=self.cfg.mode)
        self.model_original.eval()
        self.model_refactored.eval()

        with torch.no_grad():
            x0 = batch['norm_pose']
            B = x0.shape[0]
            t = torch.randint(0, self.cfg.steps, (B,), device=self.device).long()
            noise = torch.randn_like(x0)

            # 1. 测试 q_sample
            x_t_orig = self.model_original.q_sample(x0, t, noise)
            x_t_ref = self.model_refactored.diffusion_model.q_sample(x0, t, noise)
            self._compare_tensors("q_sample", x_t_orig, x_t_ref)

            # 2. 测试 model_predict
            cond_orig = self.model_original.eps_model.condition(batch)
            cond_ref = self.model_refactored.diffusion_model.core.eps_model.condition(batch)
            batch.update(cond_orig) # 两个模型的condition输出应该一致

            pred_noise_orig, pred_x0_orig = self.model_original.model_predict(x_t_orig, t, batch)
            pred_noise_ref, pred_x0_ref = self.model_refactored.diffusion_model.model_predict(x_t_ref, t, batch)

            self._compare_tensors("model_predict_noise", pred_noise_orig, pred_noise_ref)
            self._compare_tensors("model_predict_x0", pred_x0_orig, pred_x0_ref)

    def test_training_step(self, batch_size: int = 2, num_grasps: int = 4):
        """测试训练步骤的一致性"""
        batch = self._create_virtual_dataset(batch_size, num_grasps)
        self.model_original.train()
        self.model_refactored.train()

        loss_original = self.model_original.training_step(batch, 0)
        loss_refactored = self.model_refactored.training_step(batch, 0)

        diff = torch.abs(loss_original - loss_refactored).item()
        logger.info(f"原始版本损失: {loss_original.item():.6f}, 重构版本损失: {loss_refactored.item():.6f}")
        logger.info(f"训练损失差异: {diff:.2e}")
        # 训练过程由于实现细节可能差异稍大
        self._record_test_result("training_step_loss", diff < self.tolerance * 10, f"差异 {diff:.2e} > 容差 {self.tolerance * 10}")

    def test_validation_step(self, batch_size: int = 2, num_grasps: int = 4):
        """测试验证步骤的一致性"""
        batch = self._create_virtual_dataset(batch_size, num_grasps)
        self.model_original.eval()
        self.model_refactored.eval()

        with torch.no_grad():
            res_orig = self.model_original.validation_step(batch, 0)
            res_ref = self.model_refactored.validation_step(batch, 0)
            loss_orig = res_orig.get('loss', res_orig).item()
            loss_ref = res_ref.get('loss', res_ref).item()

        diff = abs(loss_orig - loss_ref)
        logger.info(f"原始版本验证损失: {loss_orig:.6f}, 重构版本验证损失: {loss_ref:.6f}")
        logger.info(f"验证损失差异: {diff:.2e}")
        self._record_test_result("validation_step_loss", diff < self.tolerance * 10, f"差异 {diff:.2e} > 容差 {self.tolerance * 10}")

    def test_sampling_process(self, batch_size: int = 2, num_grasps: int = 4):
        """测试采样过程的一致性"""
        batch = self._create_virtual_dataset(batch_size, num_grasps)
        batch = process_hand_pose_test(batch, rot_type=self.cfg.rot_type, mode=self.cfg.mode)
        self.model_original.eval()
        self.model_refactored.eval()

        with torch.no_grad():
            samples_orig = self.model_original.sample(batch, k=1)
            samples_ref = self.model_refactored.diffusion_model.sample(batch, k=1)

        shape_ok = samples_orig.shape == samples_ref.shape
        self._record_test_result("sampling_shape", shape_ok, f"形状不匹配: {samples_orig.shape} vs {samples_ref.shape}")

        if shape_ok:
            # 由于采样过程的随机性，我们只比较最终结果的统计特性
            final_orig = samples_orig[:, 0, -1]
            final_ref = samples_ref[:, 0, -1]
            mean_diff = torch.abs(final_orig.mean() - final_ref.mean()).item()
            std_diff = torch.abs(final_orig.std() - final_ref.std()).item()

            logger.info(f"最终采样均值差异: {mean_diff:.2e}, 标准差差异: {std_diff:.2e}")
            # 对统计特性的容差放宽
            stats_ok = (mean_diff < self.tolerance * 100) and (std_diff < self.tolerance * 100)
            self._record_test_result("sampling_stats", stats_ok, f"均值或标准差差异过大")

    def test_gradient_consistency(self, batch_size: int = 2, num_grasps: int = 4):
        """测试梯度计算的一致性"""
        batch = self._create_virtual_dataset(batch_size, num_grasps)
        self.model_original.train()
        self.model_refactored.train()

        self.model_original.zero_grad()
        self.model_refactored.zero_grad()

        loss_original = self.model_original.training_step(batch, 0)
        loss_refactored = self.model_refactored.training_step(batch, 0)

        loss_original.backward()
        loss_refactored.backward()

        grad_diffs = []
        orig_params = dict(self.model_original.named_parameters())
        ref_params = dict(self.model_refactored.named_parameters())

        # 比较共享参数的梯度
        for name, param in orig_params.items():
            if param.grad is not None and name in ref_params and ref_params[name].grad is not None:
                grad_diff = torch.abs(param.grad - ref_params[name].grad).max().item()
                grad_diffs.append(grad_diff)

        if not grad_diffs:
            self._record_test_result("gradient_consistency", False, "未找到可比较的梯度")
            return

        max_grad_diff = max(grad_diffs)
        avg_grad_diff = sum(grad_diffs) / len(grad_diffs)
        logger.info(f"最大梯度差异: {max_grad_diff:.2e}, 平均梯度差异: {avg_grad_diff:.2e}")
        # 梯度差异的容差通常需要更大
        self._record_test_result("gradient_consistency", max_grad_diff < self.tolerance * 100, f"最大梯度差异 {max_grad_diff:.2e} 过大")

    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始 DDPM Lightning 模型对比测试")
        logger.info("=" * 80)

        self.initialize_models()

        test_suite = {
            "扩散核心方法": self.test_diffusion_core_methods,
            "训练步骤": self.test_training_step,
            "验证步骤": self.test_validation_step,
            "采样过程": self.test_sampling_process,
            "梯度一致性": self.test_gradient_consistency,
        }

        for name, test_func in test_suite.items():
            try:
                logger.info(f"\n🔍 [{name}]")
                test_func()
            except Exception as e:
                logger.error(f"❌ 测试 [{name}] 执行时发生严重错误: {e}")
                import traceback
                traceback.print_exc()
                # 记录一个失败，以防测试函数内部没有记录
                if name not in self.test_results:
                    self.test_results[name] = False


        return self.generate_test_report()

    def generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 测试报告")
        logger.info("=" * 80)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests

        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {failed_tests}")
        if total_tests > 0:
            logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        else:
            logger.info("没有执行任何测试。")


        logger.info("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name:<25}: {status}")

        # 总结
        if failed_tests == 0:
            logger.info("\n🎉 所有测试通过！重构版本与原始版本功能一致。")
            logger.info("✅ 重构版本可以安全替换原始版本。")
        else:
            logger.info(f"\n⚠️  有 {failed_tests} 个测试失败，需要进一步调试。")
            logger.info("❌ 建议在修复问题后再进行替换。")

        return failed_tests == 0


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='DDPM Lightning 模型对比测试')
    parser.add_argument('--config', type=str, default=None,
                       help='配置文件路径')
    parser.add_argument('--tolerance', type=float, default=1e-5,
                       help='数值比较容差')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备')

    args = parser.parse_args()

    # 创建测试器
    tester = DDPMComparisonTester(
        config_path=args.config,
        tolerance=args.tolerance
    )

    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)

    tester.device = device

    # 运行测试
    all_passed = tester.run_all_tests()

    # 返回退出码
    exit_code = 0 if all_passed else 1

    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
