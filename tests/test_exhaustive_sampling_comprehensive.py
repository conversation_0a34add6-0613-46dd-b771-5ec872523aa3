#!/usr/bin/env python3
"""
穷尽采样改进效果综合测试
对比改进前后的数据利用率、数据集长度、采样策略等关键指标
"""

import sys
import os
import time
import torch
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached


def test_traditional_vs_exhaustive_sampling():
    """对比传统采样和穷尽采样的效果"""
    
    print("=" * 100)
    print("穷尽采样改进效果综合测试")
    print("=" * 100)
    
    # 基础配置
    base_config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric_scene_mean_normalized",
        "max_grasps_per_object": 200,  # 使用较大值来展示改进效果
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "random",
        "cache_mode": "train"
    }
    
    # 测试配置列表
    test_configs = [
        {
            "name": "传统采样 (原始设计)",
            "config": {
                **base_config,
                "use_exhaustive_sampling": False,
                "cache_version": "v1.0_traditional_test"
            }
        },
        {
            "name": "穷尽采样 - Sequential",
            "config": {
                **base_config,
                "use_exhaustive_sampling": True,
                "exhaustive_sampling_strategy": "sequential",
                "cache_version": "v1.0_exhaustive_sequential_test"
            }
        },
        {
            "name": "穷尽采样 - Random",
            "config": {
                **base_config,
                "use_exhaustive_sampling": True,
                "exhaustive_sampling_strategy": "random",
                "cache_version": "v1.0_exhaustive_random_test"
            }
        },
        {
            "name": "穷尽采样 - Interleaved",
            "config": {
                **base_config,
                "use_exhaustive_sampling": True,
                "exhaustive_sampling_strategy": "interleaved",
                "cache_version": "v1.0_exhaustive_interleaved_test"
            }
        },
        {
            "name": "穷尽采样 - Chunk FPS",
            "config": {
                **base_config,
                "use_exhaustive_sampling": True,
                "exhaustive_sampling_strategy": "chunk_farthest_point",
                "cache_version": "v1.0_exhaustive_fps_test"
            }
        },
        {
            "name": "穷尽采样 - Chunk NPS",
            "config": {
                **base_config,
                "use_exhaustive_sampling": True,
                "exhaustive_sampling_strategy": "chunk_nearest_point",
                "cache_version": "v1.0_exhaustive_nps_test"
            }
        }
    ]
    
    results = []
    
    for test_config in test_configs:
        print(f"\n{'='*60}")
        print(f"测试: {test_config['name']}")
        print(f"{'='*60}")
        
        try:
            # 创建数据集
            start_time = time.time()
            dataset = SceneLeapPlusDatasetCached(**test_config['config'])
            init_time = time.time() - start_time
            
            # 收集统计信息
            stats = analyze_dataset_statistics(dataset, test_config['name'])
            stats['init_time'] = init_time
            
            # 测试样本加载
            sample_stats = test_sample_loading(dataset, num_samples=3)
            stats.update(sample_stats)
            
            results.append(stats)
            
            print(f"✓ 测试完成: {test_config['name']}")
            
        except Exception as e:
            print(f"✗ 测试失败: {test_config['name']} - {str(e)}")
            import traceback
            traceback.print_exc()
            continue
    
    # 生成对比报告
    print(f"\n{'='*100}")
    print("综合对比报告")
    print(f"{'='*100}")
    
    generate_comparison_report(results)
    
    return results


def analyze_dataset_statistics(dataset, test_name: str) -> Dict[str, Any]:
    """分析数据集统计信息"""
    
    print(f"\n数据集统计信息:")
    print(f"  - 数据集长度: {len(dataset)}")
    print(f"  - num_grasps: {dataset.num_grasps}")
    print(f"  - max_grasps_per_object: {dataset.max_grasps_per_object}")
    print(f"  - grasp_sampling_strategy: {dataset.grasp_sampling_strategy}")
    print(f"  - use_exhaustive_sampling: {getattr(dataset, 'use_exhaustive_sampling', False)}")
    print(f"  - exhaustive_sampling_strategy: {getattr(dataset, 'exhaustive_sampling_strategy', 'N/A')}")
    
    # 统计总的可用抓取数
    total_available_grasps = 0
    scene_object_count = 0
    
    for scene_id, scene_data in dataset.hand_pose_data.items():
        for obj_code, poses_tensor in scene_data.items():
            if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                # 应用 max_grasps_per_object 限制
                max_grasps = dataset.max_grasps_per_object
                if max_grasps is not None:
                    actual_grasps = min(poses_tensor.shape[0], max_grasps)
                else:
                    actual_grasps = poses_tensor.shape[0]
                
                total_available_grasps += actual_grasps
                scene_object_count += 1
    
    # 计算数据利用率
    dataset_length = len(dataset)
    num_grasps = dataset.num_grasps
    theoretical_sampling_count = dataset_length * num_grasps
    
    utilization_rate = theoretical_sampling_count / total_available_grasps if total_available_grasps > 0 else 0
    
    print(f"\n数据利用率分析:")
    print(f"  - 总可用抓取数: {total_available_grasps}")
    print(f"  - 理论采样次数: {dataset_length} × {num_grasps} = {theoretical_sampling_count}")
    print(f"  - 数据利用率: {utilization_rate:.1%}")
    print(f"  - 场景-物体组合数: {scene_object_count}")
    
    return {
        'test_name': test_name,
        'dataset_length': dataset_length,
        'num_grasps': num_grasps,
        'total_available_grasps': total_available_grasps,
        'theoretical_sampling_count': theoretical_sampling_count,
        'utilization_rate': utilization_rate,
        'scene_object_count': scene_object_count,
        'use_exhaustive_sampling': getattr(dataset, 'use_exhaustive_sampling', False),
        'exhaustive_sampling_strategy': getattr(dataset, 'exhaustive_sampling_strategy', 'N/A')
    }


def test_sample_loading(dataset, num_samples: int = 3) -> Dict[str, Any]:
    """测试样本加载性能和正确性"""
    
    print(f"\n样本加载测试:")
    
    load_times = []
    sample_shapes = []
    error_count = 0
    
    test_indices = list(range(min(num_samples, len(dataset))))
    
    for idx in test_indices:
        try:
            start_time = time.time()
            sample = dataset[idx]
            load_time = time.time() - start_time
            load_times.append(load_time)
            
            if 'error' in sample:
                print(f"  样本 {idx}: 错误 - {sample['error']}")
                error_count += 1
            else:
                hand_poses = sample.get('hand_model_pose')
                se3_matrices = sample.get('se3')
                
                if isinstance(hand_poses, torch.Tensor):
                    sample_shapes.append(hand_poses.shape)
                    print(f"  样本 {idx}: 加载时间={load_time:.3f}s, hand_poses={hand_poses.shape}, se3={se3_matrices.shape if isinstance(se3_matrices, torch.Tensor) else 'N/A'}")
                else:
                    print(f"  样本 {idx}: 加载时间={load_time:.3f}s, 数据格式异常")
                    error_count += 1
                    
        except Exception as e:
            print(f"  样本 {idx}: 加载失败 - {str(e)}")
            error_count += 1
    
    avg_load_time = sum(load_times) / len(load_times) if load_times else 0
    
    return {
        'avg_load_time': avg_load_time,
        'error_count': error_count,
        'sample_shapes': sample_shapes
    }


def generate_comparison_report(results: List[Dict[str, Any]]):
    """生成对比报告"""
    
    if not results:
        print("没有可用的测试结果")
        return
    
    # 找到基准（传统采样）
    baseline = None
    for result in results:
        if not result.get('use_exhaustive_sampling', False):
            baseline = result
            break
    
    if baseline is None:
        print("未找到传统采样基准")
        return
    
    print(f"\n基准 (传统采样):")
    print(f"  - 数据集长度: {baseline['dataset_length']}")
    print(f"  - 数据利用率: {baseline['utilization_rate']:.1%}")
    print(f"  - 初始化时间: {baseline['init_time']:.2f}s")
    print(f"  - 平均加载时间: {baseline['avg_load_time']:.3f}s")
    
    print(f"\n改进效果对比:")
    print(f"{'策略':<25} {'数据集长度':<12} {'利用率':<10} {'扩展倍数':<10} {'初始化时间':<12} {'加载时间':<10}")
    print("-" * 90)
    
    for result in results:
        if result.get('use_exhaustive_sampling', False):
            expansion_factor = result['dataset_length'] / baseline['dataset_length']
            utilization_improvement = result['utilization_rate'] / baseline['utilization_rate'] if baseline['utilization_rate'] > 0 else float('inf')
            
            print(f"{result['exhaustive_sampling_strategy']:<25} "
                  f"{result['dataset_length']:<12} "
                  f"{result['utilization_rate']:.1%}{'':>5} "
                  f"{expansion_factor:.1f}x{'':>6} "
                  f"{result['init_time']:.2f}s{'':>7} "
                  f"{result['avg_load_time']:.3f}s")
    
    # 找到最佳策略
    best_strategy = None
    best_utilization = 0
    
    for result in results:
        if result.get('use_exhaustive_sampling', False) and result['utilization_rate'] > best_utilization:
            best_utilization = result['utilization_rate']
            best_strategy = result
    
    if best_strategy:
        print(f"\n🏆 最佳策略: {best_strategy['exhaustive_sampling_strategy']}")
        print(f"  - 数据利用率: {best_strategy['utilization_rate']:.1%}")
        print(f"  - 数据集扩展: {best_strategy['dataset_length'] / baseline['dataset_length']:.1f}倍")
        print(f"  - 利用率提升: {best_strategy['utilization_rate'] / baseline['utilization_rate']:.1f}倍")


if __name__ == "__main__":
    try:
        results = test_traditional_vs_exhaustive_sampling()
        print(f"\n🎉 综合测试完成！共测试了 {len(results)} 种配置")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
