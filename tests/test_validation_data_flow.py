
import unittest
import torch
from omegaconf import OmegaConf
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from datasets.scenedex_datamodule import SceneLeapDataModule
from utils.hand_helper import process_hand_pose_test

class TestValidationDataFlow(unittest.TestCase):

    def setUp(self):
        """Set up the test environment"""
        logging.info("Setting up test config...")
        # Create a mock configuration that mirrors the structure of the real config
        # You might need to adjust paths to be absolute or relative to your test execution environment
        self.cfg = OmegaConf.create({
            'data': {
                'name': 'SceneLeapPlus',
                'train': {
                    'root_dir': 'path_to_train_data', # Placeholder
                    'succ_grasp_dir': 'path_to_succ_grasps', # Placeholder
                    'obj_root_dir': 'path_to_obj_models', # Placeholder
                    'batch_size': 2,
                    'num_workers': 0,
                },
                'val': {
                    'root_dir': 'data/sceneleapplus_grasp_dataset_val',
                    'succ_grasp_dir': 'data/succ_grasps',
                    'obj_root_dir': 'data/object_models',
                    'num_grasps': 8,
                    'batch_size': 2, # Using a small batch size for testing
                    'num_workers': 0,
                    'cache_mode': 'val',
                },
                'test': { # Needed for setup
                    'root_dir': 'data/sceneleapplus_grasp_dataset_val',
                    'succ_grasp_dir': 'data/succ_grasps',
                    'obj_root_dir': 'data/object_models',
                    'batch_size': 2,
                    'num_workers': 0,
                }
            },
            'model': { # Add a dummy model config for rot_type and mode
                'rot_type': '6d',
                'mode': 'camera_centric'
            }
        })
        
        # NOTE: You must have a cached validation dataset available for this test to run.
        # The paths in the config should point to a small, representative sample of your data.
        # If your data paths are not correct, this test will fail during data loading.
        
        # Check if data paths exist
        val_root_dir = os.path.join(project_root, self.cfg.data.val.root_dir)
        if not os.path.exists(val_root_dir):
            self.fail(f"Validation data directory not found at: {val_root_dir}. Please update the path in the test script.")

        logging.info("Initializing SceneLeapDataModule...")
        self.datamodule = SceneLeapDataModule(self.cfg.data)
        self.datamodule.setup('fit')
        self.val_loader = self.datamodule.val_dataloader()
        logging.info("Setup complete.")

    def test_data_flow_to_hand_helper(self):
        """
        Tests the data flow from the dataloader to the point before hand_helper processing.
        """
        logging.info("Starting test_data_flow_to_hand_helper...")
        
        try:
            batch = next(iter(self.val_loader))
        except StopIteration:
            self.fail("Validation dataloader is empty. Check your data setup.")
        except Exception as e:
            logging.error(f"Error loading data from dataloader: {e}")
            self.fail(f"Failed to load a batch from the dataloader. Error: {e}")

        logging.info("Successfully loaded one batch of data.")
        
        # -----------------------------------------------------------------
        # 1. Inspect the raw batch from the dataloader
        # -----------------------------------------------------------------
        self.assertIn('hand_model_pose', batch)
        self.assertIn('se3', batch)
        
        logging.info("\n" + "="*50)
        logging.info("Step 1: Inspecting raw batch from Dataloader")
        logging.info("="*50)
        
        hand_pose = batch['hand_model_pose']
        se3 = batch['se3']
        
        self.assertIsInstance(hand_pose, torch.Tensor)
        self.assertIsInstance(se3, torch.Tensor)
        
        logging.info(f"batch['hand_model_pose'].shape: {hand_pose.shape}")
        logging.info(f"batch['hand_model_pose'].dtype: {hand_pose.dtype}")
        
        logging.info(f"batch['se3'].shape: {se3.shape}")
        logging.info(f"batch['se3'].dtype: {se3.dtype}")

        # Expected shapes: [batch_size, num_grasps, 23] and [batch_size, num_grasps, 4, 4]
        expected_hand_pose_shape = (self.cfg.data.val.batch_size, self.cfg.data.val.num_grasps, 23)
        expected_se3_shape = (self.cfg.data.val.batch_size, self.cfg.data.val.num_grasps, 4, 4)

        self.assertEqual(hand_pose.shape, expected_hand_pose_shape, "Shape of 'hand_model_pose' is incorrect.")
        self.assertEqual(se3.shape, expected_se3_shape, "Shape of 'se3' is incorrect.")
        
        # -----------------------------------------------------------------
        # 2. Simulate the DDPMLightning validation_step pre-processing
        # -----------------------------------------------------------------
        # The validation_step in DDPMLightning directly calls process_hand_pose_test.
        # We will replicate that call here.
        
        logging.info("\n" + "="*50)
        logging.info("Step 2: Calling process_hand_pose_test")
        logging.info("="*50)

        rot_type = self.cfg.model.rot_type
        mode = self.cfg.model.mode
        
        logging.info(f"Calling process_hand_pose_test with rot_type='{rot_type}' and mode='{mode}'")
        
        try:
            # This is where the error occurs in the traceback
            processed_batch = process_hand_pose_test(batch.copy(), rot_type=rot_type, mode=mode)
            
            logging.info("Successfully processed batch with process_hand_pose_test.")
            
            # -----------------------------------------------------------------
            # 3. Inspect the processed batch
            # -----------------------------------------------------------------
            logging.info("\n" + "="*50)
            logging.info("Step 3: Inspecting batch after process_hand_pose_test")
            logging.info("="*50)
            
            # The function `process_hand_pose_test` modifies the batch in-place and returns it.
            # It unstacks the grasps from the batch dimension.
            hand_pose_processed = processed_batch['hand_pose']
            se3_processed = processed_batch['se3']

            logging.info(f"processed_batch['hand_pose'].shape: {hand_pose_processed.shape}")
            logging.info(f"processed_batch['se3'].shape: {se3_processed.shape}")
            
            # Check the shapes after processing
            # Expected shapes: [batch_size * num_grasps, 23] and [batch_size * num_grasps, 4, 4]
            bs = self.cfg.data.val.batch_size
            ng = self.cfg.data.val.num_grasps
            expected_processed_hand_pose_shape = (bs * ng, 23)
            expected_processed_se3_shape = (bs * ng, 4, 4)
            
            self.assertEqual(hand_pose_processed.shape, expected_processed_hand_pose_shape)
            self.assertEqual(se3_processed.shape, expected_processed_se3_shape)

            logging.info("Data flow and shapes appear correct through process_hand_pose_test.")

        except RuntimeError as e:
            logging.error(f"Caught a RuntimeError during process_hand_pose_test: {e}")
            logging.error("This is the same error as in the traceback.")
            
            # Let's add more debug info here. The error is in `_process_batch_pose_logic`
            # se3_flat = se3_batch.view(B * num_grasps, 4, 4)
            # This means `se3_batch` is the problem. `se3_batch` comes from `batch['se3']`.
            se3_batch_problem = batch.get('se3')
            if se3_batch_problem is not None:
                logging.error(f"Inspecting the problematic tensor `batch['se3']`:")
                logging.error(f"  - Shape: {se3_batch_problem.shape}")
                logging.error(f"  - Dtype: {se3_batch_problem.dtype}")
                logging.error(f"  - Total elements: {se3_batch_problem.numel()}")
                
                # The error message said input size was 256 for a target of [512, 4, 4]
                # Let's calculate what the code was expecting
                B = batch['hand_model_pose'].shape[0]
                num_grasps = batch['hand_model_pose'].shape[1]
                expected_elements = B * num_grasps * 4 * 4
                logging.error(f"Expected elements for view(): {B} * {num_grasps} * 4 * 4 = {expected_elements}")
                logging.error("The error indicates the input tensor is much smaller than expected.")

            self.fail(f"RuntimeError encountered, which confirms the issue is reproducible. See logs for details. Error: {e}")
        except Exception as e:
            self.fail(f"An unexpected error occurred: {e}")


if __name__ == '__main__':
    # You might need to change the working directory for paths to resolve correctly
    # os.chdir(project_root)
    unittest.main() 