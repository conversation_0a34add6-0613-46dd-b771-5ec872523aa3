#!/usr/bin/env python3
"""
Test script to verify SceneLeapPlus dataset support in datamodule
"""

import sys
import os
import logging
from omegaconf import OmegaConf

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets import build_datasets
from datasets.scenedex_datamodule import SceneLeapDataModule

def test_sceneleapplus_config():
    """Test loading SceneLeapPlus configuration"""
    print("Testing SceneLeapPlus configuration loading...")
    
    # Load the configuration
    config_path = "config/data/sceneleapplus.yaml"
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    try:
        cfg = OmegaConf.load(config_path)
        print(f"✅ Configuration loaded successfully")
        print(f"   Dataset name: {cfg.name}")
        print(f"   Train root_dir: {cfg.train.root_dir}")
        print(f"   Num grasps: {cfg.train.num_grasps}")
        return True
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_build_datasets():
    """Test build_datasets function with SceneLeapPlus"""
    print("\nTesting build_datasets function...")
    
    # Create a mock configuration
    mock_cfg = OmegaConf.create({
        'name': 'plussceneleap',
        'mode': 'camera_centric',
        'train': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'train'
        },
        'val': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'val'
        },
        'test': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'test'
        }
    })
    
    try:
        # Test dataset creation
        train_set, val_set, test_set = build_datasets(mock_cfg, stage="fit")
        
        if train_set is None or val_set is None:
            print("❌ Failed to create train/val datasets")
            return False
        
        print(f"✅ Datasets created successfully")
        print(f"   Train dataset type: {type(train_set).__name__}")
        print(f"   Val dataset type: {type(val_set).__name__}")
        print(f"   Train dataset size: {len(train_set)}")
        print(f"   Val dataset size: {len(val_set)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating datasets: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_datamodule():
    """Test SceneLeapDataModule with SceneLeapPlus"""
    print("\nTesting SceneLeapDataModule...")
    
    # Create mock data configuration
    data_cfg = {
        'name': 'plussceneleap',
        'mode': 'camera_centric',
        'train': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'train',
            'batch_size': 4,
            'num_workers': 2
        },
        'val': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'val',
            'batch_size': 4,
            'num_workers': 2
        },
        'test': {
            'root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3',
            'succ_grasp_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect',
            'obj_root_dir': '/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models',
            'num_grasps': 8,
            'mode': 'camera_centric',
            'max_grasps_per_object': 2,
            'mesh_scale': 0.1,
            'num_neg_prompts': 4,
            'enable_cropping': True,
            'max_points': 10000,
            'grasp_sampling_strategy': 'random',
            'cache_version': 'v1.0_plus',
            'cache_mode': 'test',
            'batch_size': 4,
            'num_workers': 2
        }
    }
    
    try:
        # Create datamodule
        datamodule = SceneLeapDataModule(data_cfg)
        print(f"✅ DataModule created successfully")
        
        # Test setup
        datamodule.setup(stage="fit")
        print(f"✅ DataModule setup completed")
        
        if datamodule.train_dataset is not None:
            print(f"   Train dataset: {type(datamodule.train_dataset).__name__}")
            print(f"   Train dataset size: {len(datamodule.train_dataset)}")
        
        if datamodule.val_dataset is not None:
            print(f"   Val dataset: {type(datamodule.val_dataset).__name__}")
            print(f"   Val dataset size: {len(datamodule.val_dataset)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with DataModule: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logging.basicConfig(level=logging.INFO)
    
    print("=" * 60)
    print("SceneLeapPlus DataModule Support Test")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Configuration loading
    if test_sceneleapplus_config():
        success_count += 1
    
    # Test 2: build_datasets function
    if test_build_datasets():
        success_count += 1
    
    # Test 3: DataModule
    if test_datamodule():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! SceneLeapPlus is now supported by the datamodule.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
