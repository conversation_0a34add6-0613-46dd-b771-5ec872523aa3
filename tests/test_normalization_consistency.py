#!/usr/bin/env python3
"""
测试归一化的一致性和正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    NORM_UPPER, NORM_LOWER
)

def test_formula_consistency():
    """测试不同归一化函数使用的公式是否一致"""
    print("=== 测试公式一致性 ===")
    
    # 模拟相同的输入条件
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 创建测试数据
    test_data = torch.tensor([0.5], device=device, dtype=torch.float32)
    
    # 手动计算两种公式的结果
    # 假设统计范围是 [0, 1]，那么 scaled_0_1 = 0.5
    scaled_0_1 = 0.5
    
    # 公式1：平移使用的公式
    result1 = scaled_0_1 * (NORM_UPPER - NORM_LOWER) - ((NORM_UPPER - NORM_LOWER) / 2.0)
    
    # 公式2：参数使用的公式  
    result2 = scaled_0_1 * (NORM_UPPER - NORM_LOWER) + NORM_LOWER
    
    print(f"公式1结果: {result1}")
    print(f"公式2结果: {result2}")
    print(f"是否相等: {abs(result1 - result2) < 1e-8}")
    
    return abs(result1 - result2) < 1e-8

def test_normalization_range_correctness():
    """测试归一化是否将数据正确映射到[-1, 1]范围"""
    print("\n=== 测试归一化范围正确性 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 从统计文件中获取实际的min/max值
    from utils.hand_helper import get_min_max_from_stats
    
    try:
        # 测试平移
        labels = ['translation_x', 'translation_y', 'translation_z']
        t_min, t_max = get_min_max_from_stats(mode, labels, device, torch.float32)
        
        print(f"平移统计范围: min={t_min}, max={t_max}")
        
        # 测试边界值
        boundary_values = torch.stack([t_min, t_max], dim=0)  # [2, 3]
        normalized = normalize_trans_torch(boundary_values, mode)
        
        print(f"边界值归一化结果: {normalized}")
        print(f"期望结果: 最小值应接近{NORM_LOWER}，最大值应接近{NORM_UPPER}")
        
        # 检查是否接近期望值
        min_close = torch.allclose(normalized[0], torch.full_like(normalized[0], NORM_LOWER), atol=1e-6)
        max_close = torch.allclose(normalized[1], torch.full_like(normalized[1], NORM_UPPER), atol=1e-6)
        
        print(f"最小值正确: {min_close}")
        print(f"最大值正确: {max_close}")
        
        return min_close and max_close
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_round_trip_accuracy():
    """测试往返精度"""
    print("\n=== 测试往返精度 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 创建测试数据
    test_data = torch.tensor([
        [-0.1, 0.0, -0.05],
        [0.05, -0.02, -0.08],
        [0.0, 0.0, -0.09]
    ], device=device, dtype=torch.float32)
    
    try:
        # 平移往返测试
        norm_trans = normalize_trans_torch(test_data, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_data - denorm_trans).max().item()
        
        print(f"平移往返误差: {trans_error:.8f}")
        
        # 参数往返测试
        test_params = torch.randn(3, 16, device=device, dtype=torch.float32) * 0.5
        norm_params = normalize_param_torch(test_params, mode)
        denorm_params = denormalize_param_torch(norm_params, mode)
        param_error = torch.abs(test_params - denorm_params).max().item()
        
        print(f"参数往返误差: {param_error:.8f}")
        
        # 检查精度
        trans_ok = trans_error < 1e-5
        param_ok = param_error < 1e-5
        
        print(f"平移精度OK: {trans_ok}")
        print(f"参数精度OK: {param_ok}")
        
        return trans_ok and param_ok
        
    except Exception as e:
        print(f"往返测试失败: {e}")
        return False

def analyze_current_formulas():
    """分析当前使用的公式"""
    print("\n=== 当前公式分析 ===")
    
    print("平移归一化公式:")
    print("t_final_normalized = t_scaled_0_1 * (NORM_UPPER - NORM_LOWER) - ((NORM_UPPER - NORM_LOWER) / 2.0)")
    print("简化: t_scaled_0_1 * 2 - 1")
    print()
    
    print("参数归一化公式:")
    print("p = p * (NORM_UPPER - NORM_LOWER) + NORM_LOWER")
    print("简化: p * 2 + (-1) = p * 2 - 1")
    print()
    
    print("结论: 两个公式数学上等价，但写法不一致")
    print("建议: 统一使用一种写法以提高代码可读性")

def main():
    """主函数"""
    print("检查归一化代码的正确性...")
    
    test1 = test_formula_consistency()
    test2 = test_normalization_range_correctness()
    test3 = test_round_trip_accuracy()
    analyze_current_formulas()
    
    print(f"\n=== 总结 ===")
    if test1 and test2 and test3:
        print("✅ 归一化代码数学上是正确的")
        print("⚠️  但存在代码风格不一致的问题")
        print("建议统一归一化公式的写法")
    else:
        print("❌ 归一化代码存在问题")
        print(f"公式一致性: {test1}")
        print(f"范围正确性: {test2}")
        print(f"往返精度: {test3}")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
