#!/usr/bin/env python3
"""
Test script for Stage 1: Data preprocessing refactoring
Tests the multi-grasp support in hand_helper.py functions
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    _process_batch_pose_logic,
    norm_hand_pose_robust,
    denorm_hand_pose_robust,
    process_hand_pose,
    process_hand_pose_test
)

def test_multi_grasp_pose_processing():
    """Test multi-grasp pose processing functions"""
    print("Testing multi-grasp pose processing...")
    
    # Test parameters
    B, num_grasps, pose_dim = 4, 8, 23
    rot_type = 'r6d'
    mode = 'camera_centric'
    
    # Create test data
    se3 = torch.randn(B, num_grasps, 4, 4)
    # Ensure SE3 matrices are valid (set bottom row to [0,0,0,1])
    se3[:, :, 3, :3] = 0
    se3[:, :, 3, 3] = 1
    
    hand_pose = torch.randn(B, num_grasps, pose_dim)
    
    print(f"Input shapes: se3={se3.shape}, hand_pose={hand_pose.shape}")
    
    # Test _process_batch_pose_logic
    try:
        norm_pose, processed_pose = _process_batch_pose_logic(se3, hand_pose, rot_type, mode)
        print(f"✅ _process_batch_pose_logic: norm_pose={norm_pose.shape}, processed_pose={processed_pose.shape}")
        
        # For r6d, expected output dimension should be 3 (trans) + 16 (joints) + 6 (r6d) = 25
        expected_dim = 25
        assert processed_pose.shape == (B, num_grasps, expected_dim), f"Expected {(B, num_grasps, expected_dim)}, got {processed_pose.shape}"
        assert norm_pose.shape == (B, num_grasps, expected_dim), f"Expected {(B, num_grasps, expected_dim)}, got {norm_pose.shape}"
        
    except Exception as e:
        print(f"❌ _process_batch_pose_logic failed: {e}")
        return False
    
    # Test backward compatibility with single grasp
    try:
        se3_single = se3[:, 0, :, :]  # [B, 4, 4]
        hand_pose_single = hand_pose[:, 0, :]  # [B, 23]
        
        norm_pose_single, processed_pose_single = _process_batch_pose_logic(se3_single, hand_pose_single, rot_type, mode)
        print(f"✅ Backward compatibility: norm_pose={norm_pose_single.shape}, processed_pose={processed_pose_single.shape}")
        
        assert processed_pose_single.shape == (B, expected_dim), f"Expected {(B, expected_dim)}, got {processed_pose_single.shape}"
        
    except Exception as e:
        print(f"❌ Backward compatibility failed: {e}")
        return False
    
    return True

def test_normalization_functions():
    """Test normalization and denormalization functions"""
    print("\nTesting normalization functions...")
    
    B, num_grasps, pose_dim = 2, 4, 25  # Using processed pose dimension
    rot_type = 'r6d'
    mode = 'camera_centric'
    
    # Test data
    hand_pose = torch.randn(B, num_grasps, pose_dim)
    
    try:
        # Test normalization
        norm_pose = norm_hand_pose_robust(hand_pose, rot_type, mode)
        print(f"✅ norm_hand_pose_robust: input={hand_pose.shape}, output={norm_pose.shape}")
        assert norm_pose.shape == hand_pose.shape
        
        # Test denormalization
        denorm_pose = denorm_hand_pose_robust(norm_pose, rot_type, mode)
        print(f"✅ denorm_hand_pose_robust: input={norm_pose.shape}, output={denorm_pose.shape}")
        assert denorm_pose.shape == hand_pose.shape
        
        # Test round-trip consistency (should be approximately equal)
        diff = torch.abs(hand_pose - denorm_pose).mean()
        print(f"✅ Round-trip difference: {diff.item():.6f}")
        assert diff < 1e-4, f"Round-trip difference too large: {diff.item()}"
        
    except Exception as e:
        print(f"❌ Normalization functions failed: {e}")
        return False
    
    return True

def test_process_hand_pose():
    """Test process_hand_pose function"""
    print("\nTesting process_hand_pose function...")
    
    B, num_grasps = 2, 6
    rot_type = 'r6d'
    mode = 'camera_centric'
    
    # Create test data
    se3 = torch.randn(B, num_grasps, 4, 4)
    se3[:, :, 3, :3] = 0
    se3[:, :, 3, 3] = 1
    
    hand_model_pose = torch.randn(B, num_grasps, 23)
    
    data = {
        'se3': se3,
        'hand_model_pose': hand_model_pose,
        'scene_pc': torch.randn(B, 1000, 6)  # Additional data
    }
    
    try:
        processed_data = process_hand_pose(data, rot_type, mode)
        
        print(f"✅ process_hand_pose: norm_pose={processed_data['norm_pose'].shape}")
        print(f"✅ process_hand_pose: hand_model_pose={processed_data['hand_model_pose'].shape}")
        
        assert 'norm_pose' in processed_data
        assert 'hand_model_pose' in processed_data
        assert processed_data['norm_pose'].shape[:-1] == (B, num_grasps)
        assert processed_data['hand_model_pose'].shape[:-1] == (B, num_grasps)
        
    except Exception as e:
        print(f"❌ process_hand_pose failed: {e}")
        return False
    
    return True

def test_process_hand_pose_test():
    """Test process_hand_pose_test function"""
    print("\nTesting process_hand_pose_test function...")
    
    B, num_grasps = 2, 5
    rot_type = 'r6d'
    mode = 'camera_centric'
    
    # Create test data with some zero poses (invalid)
    se3 = torch.randn(B, num_grasps, 4, 4)
    se3[:, :, 3, :3] = 0
    se3[:, :, 3, 3] = 1
    
    hand_model_pose = torch.randn(B, num_grasps, 23)
    # Set some poses to zero (invalid)
    hand_model_pose[0, 2:4, :] = 0  # Make poses 2 and 3 invalid for first batch
    hand_model_pose[1, 4, :] = 0    # Make pose 4 invalid for second batch
    
    data = {
        'se3': se3,
        'hand_model_pose': hand_model_pose
    }
    
    try:
        processed_data = process_hand_pose_test(data, rot_type, mode)
        
        print(f"✅ process_hand_pose_test: norm_pose={processed_data['norm_pose'].shape}")
        print(f"✅ process_hand_pose_test: hand_model_pose={processed_data['hand_model_pose'].shape}")
        
        # Check that invalid poses are still zero
        assert torch.allclose(processed_data['hand_model_pose'][0, 2:4, :], torch.zeros_like(processed_data['hand_model_pose'][0, 2:4, :]))
        assert torch.allclose(processed_data['hand_model_pose'][1, 4, :], torch.zeros_like(processed_data['hand_model_pose'][1, 4, :]))
        
        print("✅ Invalid pose masking works correctly")
        
    except Exception as e:
        print(f"❌ process_hand_pose_test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("Stage 1 Multi-Grasp Data Preprocessing Tests")
    print("=" * 60)
    
    tests = [
        test_multi_grasp_pose_processing,
        test_normalization_functions,
        test_process_hand_pose,
        test_process_hand_pose_test
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Stage 1 tests passed! Multi-grasp data preprocessing is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
