#!/usr/bin/env python3
"""
Comprehensive tests for hand_helper.py
"""

import sys
import os
import torch
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    normalize_rot_torch, denormalize_rot_torch,
    norm_hand_pose_robust, denorm_hand_pose_robust,
    normalize_rot6d_torch, normalize_rot6d_numpy,
    normalize_trans_numpy, denormalize_trans_numpy,
    normalize_param_numpy, denormalize_param_numpy,
    normalize_rot_numpy, denormalize_rot_numpy,
    norm_hand_pose, denorm_hand_pose,
    process_hand_pose,
    decompose_hand_pose,
    NORM_UPPER, NORM_LOWER,
    ROT_DIM_DICT
)

def test_torch_normalization_ranges():
    """Test that normalized values are in the correct range for PyTorch tensors"""
    print("=== Testing PyTorch normalization ranges ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_trans = torch.randn(10, 3, device=device, dtype=torch.float32)
    test_param = torch.randn(10, 16, device=device, dtype=torch.float32)
    test_rot_quat = torch.randn(10, 4, device=device, dtype=torch.float32)
    test_rot_r6d = torch.randn(10, 6, device=device, dtype=torch.float32)
    
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # Test translation normalization
        norm_trans = normalize_trans_torch(test_trans, mode)
        trans_in_range = (norm_trans.min() >= NORM_LOWER) and (norm_trans.max() <= NORM_UPPER)
        print(f"Translation normalization range test: {trans_in_range}")
        
        # Test parameter normalization
        norm_param = normalize_param_torch(test_param, mode)
        param_in_range = (norm_param.min() >= NORM_LOWER) and (norm_param.max() <= NORM_UPPER)
        print(f"Parameter normalization range test: {param_in_range}")
        
        # Test quaternion rotation normalization (should remain unchanged)
        norm_rot_quat = normalize_rot_torch(test_rot_quat, 'quat', mode)
        quat_unchanged = torch.allclose(test_rot_quat, norm_rot_quat)
        print(f"Quaternion normalization (should be unchanged): {quat_unchanged}")
        
        # Test r6d rotation normalization
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d, 'r6d', mode)
        r6d_in_range = (norm_rot_r6d.min() >= NORM_LOWER) and (norm_rot_r6d.max() <= NORM_UPPER)
        print(f"R6D rotation normalization range test: {r6d_in_range}")
        
        return trans_in_range and param_in_range and quat_unchanged and r6d_in_range
        
    except Exception as e:
        print(f"PyTorch normalization range test failed: {e}")
        return False

def test_numpy_normalization_ranges():
    """Test that normalized values are in the correct range for NumPy arrays"""
    print("\n=== Testing NumPy normalization ranges ===")
    
    test_trans = np.random.randn(10, 3).astype(np.float32)
    test_param = np.random.randn(10, 16).astype(np.float32)
    test_rot_quat = np.random.randn(10, 4).astype(np.float32)
    test_rot_r6d = np.random.randn(10, 6).astype(np.float32)
    
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # Test translation normalization
        norm_trans = normalize_trans_numpy(test_trans, mode)
        trans_in_range = (norm_trans.min() >= NORM_LOWER) and (norm_trans.max() <= NORM_UPPER)
        print(f"Translation normalization range test: {trans_in_range}")
        
        # Test parameter normalization
        norm_param = normalize_param_numpy(test_param, mode)
        param_in_range = (norm_param.min() >= NORM_LOWER) and (norm_param.max() <= NORM_UPPER)
        print(f"Parameter normalization range test: {param_in_range}")
        
        # Test quaternion rotation normalization (should remain unchanged)
        norm_rot_quat = normalize_rot_numpy(test_rot_quat, 'quat', mode)
        quat_unchanged = np.allclose(test_rot_quat, norm_rot_quat)
        print(f"Quaternion normalization (should be unchanged): {quat_unchanged}")
        
        # Test r6d rotation normalization
        norm_rot_r6d = normalize_rot_numpy(test_rot_r6d, 'r6d', mode)
        r6d_in_range = (norm_rot_r6d.min() >= NORM_LOWER) and (norm_rot_r6d.max() <= NORM_UPPER)
        print(f"R6D rotation normalization range test: {r6d_in_range}")
        
        return trans_in_range and param_in_range and quat_unchanged and r6d_in_range
        
    except Exception as e:
        print(f"NumPy normalization range test failed: {e}")
        return False

def test_torch_inverse_operations():
    """Test that normalization and denormalization are inverse operations for PyTorch tensors"""
    print("\n=== Testing PyTorch inverse operations ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_trans = torch.randn(5, 3, device=device, dtype=torch.float32) * 0.1  # Small range test
    test_param = torch.randn(5, 16, device=device, dtype=torch.float32) * 0.5
    test_rot_r6d = torch.randn(5, 6, device=device, dtype=torch.float32) * 0.2
    
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # Test translation
        norm_trans = normalize_trans_torch(test_trans, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_trans - denorm_trans).max().item()
        print(f"Translation inverse operation error: {trans_error:.6f}")
        
        # Test parameters
        norm_param = normalize_param_torch(test_param, mode)
        denorm_param = denormalize_param_torch(norm_param, mode)
        param_error = torch.abs(test_param - denorm_param).max().item()
        print(f"Parameter inverse operation error: {param_error:.6f}")
        
        # Test r6d rotation
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d, 'r6d', mode)
        denorm_rot_r6d = denormalize_rot_torch(norm_rot_r6d, 'r6d', mode)
        rot_r6d_error = torch.abs(test_rot_r6d - denorm_rot_r6d).max().item()
        print(f"R6D rotation inverse operation error: {rot_r6d_error:.6f}")
        
        tolerance = 1e-4
        return (trans_error < tolerance) and (param_error < tolerance) and (rot_r6d_error < tolerance)
        
    except Exception as e:
        print(f"PyTorch inverse operations test failed: {e}")
        return False

def test_numpy_inverse_operations():
    """Test that normalization and denormalization are inverse operations for NumPy arrays"""
    print("\n=== Testing NumPy inverse operations ===")
    
    test_trans = (np.random.randn(5, 3) * 0.1).astype(np.float32)  # Small range test
    test_param = (np.random.randn(5, 16) * 0.5).astype(np.float32)
    test_rot_r6d = (np.random.randn(5, 6) * 0.2).astype(np.float32)
    
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # Test translation
        norm_trans = normalize_trans_numpy(test_trans, mode)
        denorm_trans = denormalize_trans_numpy(norm_trans, mode)
        trans_error = np.abs(test_trans - denorm_trans).max()
        print(f"Translation inverse operation error: {trans_error:.6f}")
        
        # Test parameters
        norm_param = normalize_param_numpy(test_param, mode)
        denorm_param = denormalize_param_numpy(norm_param, mode)
        param_error = np.abs(test_param - denorm_param).max()
        print(f"Parameter inverse operation error: {param_error:.6f}")
        
        # Test r6d rotation
        norm_rot_r6d = normalize_rot_numpy(test_rot_r6d, 'r6d', mode)
        denorm_rot_r6d = denormalize_rot_numpy(norm_rot_r6d, 'r6d', mode)
        rot_r6d_error = np.abs(test_rot_r6d - denorm_rot_r6d).max()
        print(f"R6D rotation inverse operation error: {rot_r6d_error:.6f}")
        
        tolerance = 1e-4
        return (trans_error < tolerance) and (param_error < tolerance) and (rot_r6d_error < tolerance)
        
    except Exception as e:
        print(f"NumPy inverse operations test failed: {e}")
        return False

def test_rot6d_normalization():
    """Test rot6d normalization functions"""
    print("\n=== Testing rot6d normalization ===")
    
    # Test PyTorch version
    try:
        test_rot6d_torch = torch.randn(5, 6, dtype=torch.float32)
        norm_rot6d_torch = normalize_rot6d_torch(test_rot6d_torch)
        # Check if the result is a valid rotation matrix when converted back
        print(f"PyTorch rot6d normalization shape: {norm_rot6d_torch.shape}")
        
        # Test NumPy version
        test_rot6d_numpy = np.random.randn(5, 6).astype(np.float32)
        norm_rot6d_numpy = normalize_rot6d_numpy(test_rot6d_numpy)
        print(f"NumPy rot6d normalization shape: {norm_rot6d_numpy.shape}")
        
        return True
    except Exception as e:
        print(f"Rot6d normalization test failed: {e}")
        return False

def test_multi_grasp_support():
    """Test multi-grasp format support"""
    print("\n=== Testing multi-grasp support ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    B, num_grasps = 4, 8
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # Create multi-grasp data [B, num_grasps, pose_dim]
    multi_grasp_pose = torch.randn(B, num_grasps, pose_dim, device=device, dtype=torch.float32)
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # Test multi-grasp normalization
        norm_multi_pose = norm_hand_pose_robust(multi_grasp_pose, rot_type, mode)
        print(f"Multi-grasp input shape: {multi_grasp_pose.shape}")
        print(f"Multi-grasp output shape: {norm_multi_pose.shape}")
        
        # Test multi-grasp denormalization
        denorm_multi_pose = denorm_hand_pose_robust(norm_multi_pose, rot_type, mode)
        print(f"Multi-grasp denormalization shape: {denorm_multi_pose.shape}")
        
        # Check shape consistency
        shape_match = (multi_grasp_pose.shape == norm_multi_pose.shape == denorm_multi_pose.shape)
        print(f"Shape consistency: {shape_match}")
        
        # Check numerical consistency (for quat part should be exactly the same)
        quat_part_orig = multi_grasp_pose[:, :, -4:]
        quat_part_denorm = denorm_multi_pose[:, :, -4:]
        quat_error = torch.abs(quat_part_orig - quat_part_denorm).max().item()
        print(f"Quaternion part error: {quat_error:.6f}")
        
        return shape_match and quat_error < 1e-5
        
    except Exception as e:
        print(f"Multi-grasp test failed: {e}")
        return False

def test_single_grasp_compatibility():
    """Test single-grasp backward compatibility"""
    print("\n=== Testing single-grasp compatibility ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # Create single-grasp data [B, pose_dim]
    single_grasp_pose = torch.randn(4, pose_dim, device=device, dtype=torch.float32)
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # Test single-grasp normalization
        norm_single_pose = norm_hand_pose_robust(single_grasp_pose, rot_type, mode)
        print(f"Single-grasp input shape: {single_grasp_pose.shape}")
        print(f"Single-grasp output shape: {norm_single_pose.shape}")
        
        # Test single-grasp denormalization
        denorm_single_pose = denorm_hand_pose_robust(norm_single_pose, rot_type, mode)
        print(f"Single-grasp denormalization shape: {denorm_single_pose.shape}")
        
        # Check shape consistency
        shape_match = (single_grasp_pose.shape == norm_single_pose.shape == denorm_single_pose.shape)
        print(f"Shape consistency: {shape_match}")
        
        return shape_match
        
    except Exception as e:
        print(f"Single-grasp test failed: {e}")
        return False

def test_process_hand_pose():
    """Test process_hand_pose function"""
    print("\n=== Testing process_hand_pose function ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    B = 4
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # Create test data
    test_data = {
        'se3': torch.randn(B, 4, 4, device=device, dtype=torch.float32),
        'hand_model_pose': torch.randn(B, pose_dim, device=device, dtype=torch.float32)
    }
    
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # Process the data
        processed_data = process_hand_pose(test_data, rot_type, mode)
        
        # Check that the required keys are present
        has_norm_pose = 'norm_pose' in processed_data
        has_hand_model_pose = 'hand_model_pose' in processed_data
        
        print(f"Has norm_pose key: {has_norm_pose}")
        print(f"Has hand_model_pose key: {has_hand_model_pose}")
        print(f"norm_pose shape: {processed_data['norm_pose'].shape}")
        print(f"hand_model_pose shape: {processed_data['hand_model_pose'].shape}")
        
        return has_norm_pose and has_hand_model_pose
    except Exception as e:
        print(f"process_hand_pose test failed: {e}")
        return False

def test_decompose_hand_pose():
    """Test decompose_hand_pose function"""
    print("\n=== Testing decompose_hand_pose function ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    B = 4
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # Create test pose data
    test_pose = torch.randn(B, pose_dim, device=device, dtype=torch.float32)
    rot_type = 'quat'
    
    try:
        # Decompose the pose
        translation, rotation, joint_angles = decompose_hand_pose(test_pose, rot_type)
        
        # Check shapes
        trans_shape_correct = translation.shape == (B, 3)
        rotation_shape_correct = rotation.shape == (B, 3, 3)
        joint_shape_correct = joint_angles.shape == (B, 16)
        
        print(f"Translation shape correct: {trans_shape_correct}")
        print(f"Rotation shape correct: {rotation_shape_correct}")
        print(f"Joint angles shape correct: {joint_shape_correct}")
        
        return trans_shape_correct and rotation_shape_correct and joint_shape_correct
    except Exception as e:
        print(f"decompose_hand_pose test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Starting comprehensive tests for hand_helper.py...")
    
    tests = [
        ("PyTorch normalization ranges", test_torch_normalization_ranges),
        ("NumPy normalization ranges", test_numpy_normalization_ranges),
        ("PyTorch inverse operations", test_torch_inverse_operations),
        ("NumPy inverse operations", test_numpy_inverse_operations),
        ("Rot6d normalization", test_rot6d_normalization),
        ("Multi-grasp support", test_multi_grasp_support),
        ("Single-grasp compatibility", test_single_grasp_compatibility),
        ("process_hand_pose function", test_process_hand_pose),
        ("decompose_hand_pose function", test_decompose_hand_pose),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name}: ❌ EXCEPTION - {e}")
    
    print("\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! hand_helper.py is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)