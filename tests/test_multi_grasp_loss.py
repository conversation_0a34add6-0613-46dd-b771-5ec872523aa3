"""
Test script for multi-grasp loss computation
验证多抓取损失计算更新的正确性
"""

import torch
import torch.nn.functional as F
import pytest
import sys
import os
from unittest.mock import MagicMock
from omegaconf import OmegaConf

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.loss.grasp_loss_pose import <PERSON>rasp<PERSON><PERSON>Pose


def create_mock_loss_config():
    """Create mock loss configuration for testing"""
    config = OmegaConf.create({
        'hand_model': {
            'n_surface_points': 1024,
            'rot_type': 'r6d'
        },
        'loss_weights': {
            'para': 1.0,
            'noise': 1.0,
            'translation': 10.0,
            'rotation': 10.0,
            'qpos': 1.0,
            'neg_loss': 5.0
        },
        'cost_weights': {
            'hand_mesh': 0.0,
            'qpos': 1.0,
            'translation': 2.0,
            'rotation': 2.0
        },
        'device': 'cpu',
        'rot_type': 'r6d',
        'mode': 'camera_centric',
        'q1': {
            'lambda_torque': 10,
            'm': 8,
            'mu': 1,
            'nms': True,
            'thres_contact': 0.01,
            'thres_pen': 0.005,
            'thres_tpen': 0.01,
            'rot_type': 'r6d'
        },
        'multi_grasp': {
            'loss_aggregation': 'mean',
            'use_consistency_loss': True,
            'consistency_loss_weight': 0.1,
            'diversity_loss_weight': 0.05
        }
    })
    return config


def create_single_grasp_data(batch_size=4, pose_dim=25):
    """Create single grasp test data"""
    pred_dict = {
        'matched': {
            'pred_pose_norm': torch.randn(batch_size, pose_dim),
            'pred_noise': torch.randn(batch_size, pose_dim),
            'noise': torch.randn(batch_size, pose_dim),
        },
        'rot_type': 'r6d'
    }
    
    batch = {
        'matched': {
            'norm_pose': torch.randn(batch_size, pose_dim),
        }
    }
    
    return pred_dict, batch


def create_multi_grasp_data(batch_size=4, num_grasps=8, pose_dim=25):
    """Create multi-grasp test data"""
    pred_dict = {
        'matched': {
            'pred_pose_norm': torch.randn(batch_size, num_grasps, pose_dim),
            'pred_noise': torch.randn(batch_size, num_grasps, pose_dim),
            'noise': torch.randn(batch_size, num_grasps, pose_dim),
        },
        'rot_type': 'r6d'
    }
    
    batch = {
        'matched': {
            'norm_pose': torch.randn(batch_size, num_grasps, pose_dim),
        }
    }
    
    return pred_dict, batch


class TestMultiGraspLoss:
    """Test class for multi-grasp loss computation"""
    
    def setup_method(self):
        """Setup test environment"""
        self.config = create_mock_loss_config()
        
        # Mock the HandModel to avoid device/CUDA issues in testing
        with pytest.MonkeyPatch().context() as m:
            mock_hand_model = MagicMock()
            m.setattr('models.loss.grasp_loss_pose.HandModel', lambda *args, **kwargs: mock_hand_model)
            m.setattr('models.loss.grasp_loss_pose.Matcher', lambda *args, **kwargs: MagicMock())
            
            self.criterion = GraspLossPose(self.config)
    
    def test_multi_grasp_para_loss(self):
        """Test parameter loss with multi-grasp format"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test para loss
        loss_dict = self.criterion.get_para_loss(pred_dict, batch)
        
        assert 'para' in loss_dict
        assert isinstance(loss_dict['para'], torch.Tensor)
        assert loss_dict['para'].item() >= 0
        
        print(f"✅ Multi-grasp para loss: {loss_dict['para'].item():.6f}")
    
    def test_multi_grasp_noise_loss(self):
        """Test noise loss with multi-grasp format"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test noise loss
        loss_dict = self.criterion.get_noise_loss(pred_dict, batch)
        
        assert 'noise' in loss_dict
        assert isinstance(loss_dict['noise'], torch.Tensor)
        assert loss_dict['noise'].item() >= 0
        
        print(f"✅ Multi-grasp noise loss: {loss_dict['noise'].item():.6f}")
    
    def test_multi_grasp_translation_loss(self):
        """Test translation loss with multi-grasp format"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test translation loss
        loss_dict = self.criterion.get_translation_loss(pred_dict, batch)
        
        assert 'translation' in loss_dict
        assert isinstance(loss_dict['translation'], torch.Tensor)
        assert loss_dict['translation'].item() >= 0
        
        print(f"✅ Multi-grasp translation loss: {loss_dict['translation'].item():.6f}")
    
    def test_multi_grasp_qpos_loss(self):
        """Test qpos loss with multi-grasp format"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test qpos loss
        loss_dict = self.criterion.get_qpos_loss(pred_dict, batch)
        
        assert 'qpos' in loss_dict
        assert isinstance(loss_dict['qpos'], torch.Tensor)
        assert loss_dict['qpos'].item() >= 0
        
        print(f"✅ Multi-grasp qpos loss: {loss_dict['qpos'].item():.6f}")
    
    def test_multi_grasp_rotation_loss(self):
        """Test rotation loss with multi-grasp format"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test rotation loss
        loss_dict = self.criterion.get_rotation_loss(pred_dict, batch)
        
        assert 'rotation' in loss_dict
        assert isinstance(loss_dict['rotation'], torch.Tensor)
        assert loss_dict['rotation'].item() >= 0
        
        print(f"✅ Multi-grasp rotation loss: {loss_dict['rotation'].item():.6f}")
    
    def test_consistency_loss(self):
        """Test grasp consistency loss"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test consistency loss
        loss_dict = self.criterion.get_consistency_loss(pred_dict, batch)
        
        assert 'consistency' in loss_dict
        assert isinstance(loss_dict['consistency'], torch.Tensor)
        assert loss_dict['consistency'].item() >= 0
        
        print(f"✅ Consistency loss: {loss_dict['consistency'].item():.6f}")
    
    def test_diversity_loss(self):
        """Test grasp diversity loss"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test diversity loss
        loss_dict = self.criterion.get_diversity_loss(pred_dict, batch)
        
        assert 'diversity' in loss_dict
        assert isinstance(loss_dict['diversity'], torch.Tensor)
        
        print(f"✅ Diversity loss: {loss_dict['diversity'].item():.6f}")
    
    def test_loss_aggregation_strategies(self):
        """Test different loss aggregation strategies"""
        pred_dict, batch = create_multi_grasp_data()
        
        # Test mean aggregation
        self.criterion.loss_aggregation = 'mean'
        loss_mean = self.criterion.get_para_loss(pred_dict, batch)['para']
        
        # Test sum aggregation
        self.criterion.loss_aggregation = 'sum'
        loss_sum = self.criterion.get_para_loss(pred_dict, batch)['para']
        
        # Test weighted aggregation
        self.criterion.loss_aggregation = 'weighted'
        batch['grasp_weights'] = torch.ones(4, 8)  # Equal weights
        loss_weighted = self.criterion.get_para_loss(pred_dict, batch)['para']
        
        assert all(isinstance(loss, torch.Tensor) for loss in [loss_mean, loss_sum, loss_weighted])
        assert all(loss.item() >= 0 for loss in [loss_mean, loss_sum, loss_weighted])
        
        print(f"✅ Loss aggregation - Mean: {loss_mean.item():.6f}, Sum: {loss_sum.item():.6f}, Weighted: {loss_weighted.item():.6f}")
    
    def test_backward_compatibility(self):
        """Test backward compatibility with single grasp format"""
        pred_dict, batch = create_single_grasp_data()
        
        # Test that single grasp format still works
        para_loss = self.criterion.get_para_loss(pred_dict, batch)
        noise_loss = self.criterion.get_noise_loss(pred_dict, batch)
        translation_loss = self.criterion.get_translation_loss(pred_dict, batch)
        qpos_loss = self.criterion.get_qpos_loss(pred_dict, batch)
        rotation_loss = self.criterion.get_rotation_loss(pred_dict, batch)
        
        # All losses should be computed successfully
        assert all(['para' in para_loss, 'noise' in noise_loss, 'translation' in translation_loss,
                   'qpos' in qpos_loss, 'rotation' in rotation_loss])
        
        print("✅ Backward compatibility test passed")


class TestMultiGraspMetrics:
    """Test class for multi-grasp evaluation metrics"""
    
    def setup_method(self):
        """Setup test environment"""
        self.config = create_mock_loss_config()
        
        # Mock dependencies
        with pytest.MonkeyPatch().context() as m:
            mock_hand_model = MagicMock()
            m.setattr('models.loss.grasp_loss_pose.HandModel', lambda *args, **kwargs: mock_hand_model)
            m.setattr('models.loss.grasp_loss_pose.Matcher', lambda *args, **kwargs: MagicMock())
            
            self.criterion = GraspLossPose(self.config)
    
    def test_multi_grasp_metrics(self):
        """Test multi-grasp evaluation metrics"""
        B, num_grasps, pose_dim = 4, 8, 25
        
        pred_dict = {
            'pred_pose_norm': torch.randn(B, num_grasps, pose_dim),
        }
        batch = {
            'norm_pose': torch.randn(B, num_grasps, pose_dim),
        }
        
        metrics, _ = self.criterion.forward_metric(pred_dict, batch)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'mse_mean', 'mse_std', 'mse_min', 'mse_max',
            'best_grasp_mse', 'diversity_score',
            'translation_mse', 'best_translation_mse',
            'qpos_mse', 'best_qpos_mse',
            'rotation_mse', 'best_rotation_mse'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
            assert isinstance(metrics[metric], (int, float)), f"Invalid metric type: {metric}"
        
        # Check Top-K metrics
        for k in [1, 3, 5]:
            if k <= num_grasps:
                assert f'top{k}_mse' in metrics
        
        print("✅ Multi-grasp metrics test passed")
        print(f"   MSE mean: {metrics['mse_mean']:.6f}")
        print(f"   Best grasp MSE: {metrics['best_grasp_mse']:.6f}")
        print(f"   Diversity score: {metrics['diversity_score']:.6f}")
    
    def test_single_grasp_metrics_compatibility(self):
        """Test single grasp metrics compatibility"""
        B, pose_dim = 4, 25
        
        pred_dict = {
            'pred_pose_norm': torch.randn(B, pose_dim),
        }
        batch = {
            'norm_pose': torch.randn(B, pose_dim),
        }
        
        metrics, _ = self.criterion.forward_metric(pred_dict, batch)
        
        # Check single grasp metrics
        expected_metrics = ['mse', 'translation_mse', 'qpos_mse', 'rotation_mse']
        
        for metric in expected_metrics:
            assert metric in metrics, f"Missing single grasp metric: {metric}"
            assert isinstance(metrics[metric], (int, float)), f"Invalid metric type: {metric}"
        
        print("✅ Single grasp metrics compatibility test passed")


if __name__ == "__main__":
    # Run tests
    test_loss = TestMultiGraspLoss()
    test_loss.setup_method()
    
    print("=== Testing Multi-Grasp Loss Computation ===")
    test_loss.test_multi_grasp_para_loss()
    test_loss.test_multi_grasp_noise_loss()
    test_loss.test_multi_grasp_translation_loss()
    test_loss.test_multi_grasp_qpos_loss()
    test_loss.test_multi_grasp_rotation_loss()
    test_loss.test_consistency_loss()
    test_loss.test_diversity_loss()
    test_loss.test_loss_aggregation_strategies()
    test_loss.test_backward_compatibility()
    
    print("\n=== Testing Multi-Grasp Metrics ===")
    test_metrics = TestMultiGraspMetrics()
    test_metrics.setup_method()
    test_metrics.test_multi_grasp_metrics()
    test_metrics.test_single_grasp_metrics_compatibility()
    
    print("\n🎉 All tests passed!")
