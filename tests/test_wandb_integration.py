#!/usr/bin/env python3
"""
WandB集成测试脚本

验证Scene Leap Plus项目的WandB集成功能，包括：
1. 配置文件加载测试
2. WandB导入测试
3. 离线模式测试
4. PyTorch Lightning集成测试
5. 带宽使用估算

运行方法：
    python tests/test_wandb_integration.py
"""

import os
import sys
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_config_loading():
    """测试配置文件加载"""
    try:
        from omegaconf import OmegaConf
        
        config_path = Path("config/config.yaml")
        if not config_path.exists():
            logger.error(f"配置文件不存在: {config_path}")
            return False
            
        cfg = OmegaConf.load(config_path)
        
        # 检查wandb配置是否存在
        if not cfg.get("wandb"):
            logger.error("配置文件中缺少wandb配置块")
            return False
            
        wandb_cfg = cfg.wandb
        
        # 检查必要的配置项
        required_keys = ["enabled", "project", "optimization", "experiment"]
        for key in required_keys:
            if key not in wandb_cfg:
                logger.error(f"wandb配置中缺少必要项: {key}")
                return False
        
        # 检查项目名称
        expected_project = "scene-leap-plus-diffusion-grasp"
        if wandb_cfg.project != expected_project:
            logger.warning(f"项目名称不匹配，期望: {expected_project}, 实际: {wandb_cfg.project}")
        
        logger.info("✅ 配置文件加载测试通过")
        logger.info(f"   项目名称: {wandb_cfg.project}")
        logger.info(f"   启用状态: {wandb_cfg.enabled}")
        logger.info(f"   可视化启用: {wandb_cfg.optimization.enable_visualization}")
        logger.info(f"   直方图启用: {wandb_cfg.optimization.log_histograms}")
        
        return True
        
    except Exception as e:
        logger.error(f"配置文件加载失败: {e}")
        return False


def test_wandb_import():
    """测试WandB导入"""
    try:
        import wandb
        from pytorch_lightning.loggers import WandbLogger
        
        logger.info("✅ WandB导入测试通过")
        logger.info(f"   WandB版本: {wandb.__version__}")
        
        return True
        
    except ImportError as e:
        logger.error(f"WandB导入失败: {e}")
        logger.error("请安装wandb: pip install wandb")
        return False


def test_wandb_offline_mode():
    """测试WandB离线模式"""
    try:
        import wandb
        
        # 设置离线模式
        os.environ["WANDB_MODE"] = "offline"
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 初始化wandb项目
            run = wandb.init(
                project="test-scene-leap-plus",
                name="test-run",
                dir=temp_dir,
                mode="offline"
            )
            
            # 记录一些测试数据
            wandb.log({"test_metric": 0.5, "epoch": 1})
            
            # 结束运行
            wandb.finish()
        
        logger.info("✅ WandB离线模式测试通过")
        return True
        
    except Exception as e:
        logger.error(f"WandB离线模式测试失败: {e}")
        return False
    finally:
        # 清理环境变量
        if "WANDB_MODE" in os.environ:
            del os.environ["WANDB_MODE"]


def test_pytorch_lightning_integration():
    """测试PyTorch Lightning集成"""
    try:
        import torch
        import pytorch_lightning as pl
        from pytorch_lightning.loggers import WandbLogger
        from utils.wandb_callbacks import WandbVisualizationCallback, WandbMetricsCallback
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置离线模式
            os.environ["WANDB_MODE"] = "offline"
            
            # 创建WandB logger
            wandb_logger = WandbLogger(
                project="test-scene-leap-plus",
                name="test-lightning-integration",
                save_dir=temp_dir,
                offline=True
            )
            
            # 创建回调
            viz_callback = WandbVisualizationCallback(
                log_model_graph=False,  # 简化测试
                log_sample_predictions=False,
                sample_log_freq=10
            )
            
            metrics_callback = WandbMetricsCallback(
                log_histograms=False,  # 简化测试
                histogram_freq=10
            )
            
            logger.info("✅ PyTorch Lightning集成测试通过")
            logger.info("   WandbLogger创建成功")
            logger.info("   WandbVisualizationCallback创建成功")
            logger.info("   WandbMetricsCallback创建成功")
            
            return True
            
    except Exception as e:
        logger.error(f"PyTorch Lightning集成测试失败: {e}")
        return False
    finally:
        # 清理环境变量
        if "WANDB_MODE" in os.environ:
            del os.environ["WANDB_MODE"]


def estimate_bandwidth_usage():
    """估算带宽使用情况"""
    try:
        from omegaconf import OmegaConf
        
        config_path = Path("config/config.yaml")
        cfg = OmegaConf.load(config_path)
        wandb_cfg = cfg.wandb
        
        # 基础指标大小估算
        base_metrics_size = 250  # 字节
        log_freq = wandb_cfg.get("log_freq", 100)
        
        # 假设训练参数
        steps_per_epoch = 1000  # 假设值
        total_epochs = 1000
        total_steps = steps_per_epoch * total_epochs
        
        # 计算基础指标流量
        base_logs_count = total_steps // log_freq
        base_traffic = base_logs_count * base_metrics_size / (1024 * 1024)  # MB
        
        logger.info("✅ 带宽使用估算完成")
        logger.info(f"   训练配置: {total_epochs} epochs, {steps_per_epoch} steps/epoch")
        logger.info(f"   日志频率: 每 {log_freq} 步记录一次")
        logger.info(f"   基础指标流量: ~{base_traffic:.1f} MB")
        
        # 可选功能流量估算
        opt_cfg = wandb_cfg.optimization
        
        if opt_cfg.get("enable_visualization", False):
            viz_freq = opt_cfg.get("visualization_freq", 20)
            viz_count = total_epochs // viz_freq
            viz_traffic = viz_count * 2  # MB per visualization
            logger.info(f"   可视化流量: ~{viz_traffic:.1f} MB ({viz_count} 次)")
        else:
            logger.info("   可视化: 已禁用")
            
        if opt_cfg.get("log_histograms", False):
            hist_freq = opt_cfg.get("histogram_freq", 50)
            hist_count = total_epochs // hist_freq
            hist_traffic = hist_count * 10  # MB per histogram log
            logger.info(f"   直方图流量: ~{hist_traffic:.1f} MB ({hist_count} 次)")
        else:
            logger.info("   直方图: 已禁用")
            
        if opt_cfg.get("log_gradients", False):
            grad_freq = opt_cfg.get("gradient_freq", 1000)
            grad_count = total_steps // grad_freq
            grad_traffic = grad_count * 0.01  # MB per gradient log
            logger.info(f"   梯度信息流量: ~{grad_traffic:.1f} MB ({grad_count} 次)")
        else:
            logger.info("   梯度信息: 已禁用")
        
        return True
        
    except Exception as e:
        logger.error(f"带宽使用估算失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 WandB配置验证...")
    logger.info("=" * 50)
    
    tests = [
        ("📋 配置文件加载", test_config_loading),
        ("📋 WandB导入", test_wandb_import),
        ("📋 WandB离线模式", test_wandb_offline_mode),
        ("📋 PyTorch Lightning集成", test_pytorch_lightning_integration),
        ("📋 带宽使用估算", estimate_bandwidth_usage),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"{test_name} 失败")
        except Exception as e:
            logger.error(f"{test_name} 异常: {e}")
    
    logger.info("=" * 50)
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 WandB配置验证通过！")
        logger.info("\n使用指南:")
        logger.info("1. 安装WandB: pip install wandb")
        logger.info("2. 登录WandB: wandb login")
        logger.info("3. 启动训练: python train_lightning.py")
        logger.info("4. 查看结果: https://wandb.ai")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
