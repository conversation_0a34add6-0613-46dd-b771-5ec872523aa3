#!/usr/bin/env python3
"""
配置文件验证测试：验证更新后的配置文件正确性
测试多抓取验证/测试配置是否能正确加载和使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from omegaconf import OmegaConf
from pathlib import Path


def test_diffuser_config_loading():
    """测试diffuser配置文件加载"""
    print("🔄 测试diffuser配置文件加载...")
    
    config_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'diffuser.yaml'
    
    try:
        # 加载配置文件
        config = OmegaConf.load(config_path)
        
        # 验证基本配置
        assert 'name' in config
        assert 'multi_grasp' in config
        assert 'validation_test' in config.multi_grasp
        
        print(f"  ✅ 基本配置加载成功")
        
        # 验证多抓取配置
        multi_grasp_config = config.multi_grasp
        assert multi_grasp_config.enabled == True
        assert 'validation_test' in multi_grasp_config
        
        print(f"  ✅ 多抓取配置验证通过")
        
        # 验证验证/测试配置
        val_test_config = multi_grasp_config.validation_test
        assert val_test_config.multi_grasp_mode == True
        assert val_test_config.backward_compatibility == True
        assert 'inference' in val_test_config
        assert 'matcher' in val_test_config
        assert 'evaluation' in val_test_config
        
        print(f"  ✅ 验证/测试配置验证通过")
        
        # 验证推理配置
        inference_config = val_test_config.inference
        assert inference_config.parallel_decode == True
        assert 'num_inference_grasps' in inference_config
        assert 'k_samples' in inference_config
        
        print(f"  ✅ 推理配置验证通过")
        
        # 验证匹配器配置
        matcher_config = val_test_config.matcher
        assert matcher_config.multi_grasp_matching == True
        assert 'cost_aggregation' in matcher_config
        assert 'hungarian_algorithm' in matcher_config
        
        print(f"  ✅ 匹配器配置验证通过")
        
        print("✅ diffuser配置文件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ diffuser配置文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_loss_config_loading():
    """测试loss配置文件加载"""
    print("🔄 测试loss配置文件加载...")
    
    config_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'criterion' / 'loss.yaml'
    
    try:
        # 加载配置文件
        config = OmegaConf.load(config_path)
        
        # 验证基本配置
        assert 'loss_weights' in config
        assert 'cost_weights' in config
        assert 'matcher' in config
        assert 'evaluation' in config
        
        print(f"  ✅ 基本配置加载成功")
        
        # 验证匹配器配置
        matcher_config = config.matcher
        assert matcher_config.multi_grasp_matching == True
        assert 'cost_weights' in matcher_config
        assert 'algorithm' in matcher_config
        assert 'multi_to_multi' in matcher_config
        assert 'backward_compatibility' in matcher_config
        
        print(f"  ✅ 匹配器配置验证通过")
        
        # 验证算法配置
        algorithm_config = matcher_config.algorithm
        assert algorithm_config.type == "hungarian"
        assert 'cost_threshold' in algorithm_config
        
        print(f"  ✅ 算法配置验证通过")
        
        # 验证多对多匹配配置
        multi_to_multi_config = matcher_config.multi_to_multi
        assert multi_to_multi_config.enable_partial_matching == True
        assert 'unmatched_penalty' in multi_to_multi_config
        assert 'duplicate_penalty' in multi_to_multi_config
        
        print(f"  ✅ 多对多匹配配置验证通过")
        
        # 验证向后兼容配置
        compat_config = matcher_config.backward_compatibility
        assert compat_config.support_single_grasp == True
        assert compat_config.auto_expand_dimensions == True
        
        print(f"  ✅ 向后兼容配置验证通过")
        
        # 验证评估配置
        eval_config = config.evaluation
        assert 'validation_test' in eval_config
        
        val_test_eval = eval_config.validation_test
        assert 'matching_evaluation' in val_test_eval
        assert 'distribution_evaluation' in val_test_eval
        assert 'performance_evaluation' in val_test_eval
        
        print(f"  ✅ 评估配置验证通过")
        
        print("✅ loss配置文件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ loss配置文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_integration():
    """测试配置文件集成"""
    print("🔄 测试配置文件集成...")
    
    try:
        # 模拟配置文件使用场景
        diffuser_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'diffuser.yaml'
        loss_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'criterion' / 'loss.yaml'
        
        diffuser_config = OmegaConf.load(diffuser_path)
        loss_config = OmegaConf.load(loss_path)
        
        # 验证配置一致性
        # 检查多抓取模式是否一致
        diffuser_multi_grasp = diffuser_config.multi_grasp.validation_test.multi_grasp_mode
        loss_multi_grasp = loss_config.matcher.multi_grasp_matching
        
        assert diffuser_multi_grasp == loss_multi_grasp, "多抓取模式配置不一致"
        print(f"  ✅ 多抓取模式配置一致")
        
        # 检查成本权重配置
        diffuser_matcher = diffuser_config.multi_grasp.validation_test.matcher
        loss_matcher = loss_config.matcher
        
        assert diffuser_matcher.multi_grasp_matching == loss_matcher.multi_grasp_matching
        print(f"  ✅ 匹配器配置一致")
        
        # 验证评估配置兼容性
        diffuser_eval = diffuser_config.multi_grasp.validation_test.evaluation
        loss_eval = loss_config.evaluation.validation_test
        
        # 检查一些关键配置项
        assert diffuser_eval.compute_per_grasp_metrics == True
        assert loss_eval.matching_evaluation.compute_matching_accuracy == True
        
        print(f"  ✅ 评估配置兼容")
        
        print("✅ 配置文件集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_parameter_validation():
    """测试配置参数有效性"""
    print("🔄 测试配置参数有效性...")
    
    try:
        diffuser_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'diffuser.yaml'
        loss_path = Path(__file__).parent.parent / 'config' / 'model' / 'diffuser' / 'criterion' / 'loss.yaml'
        
        diffuser_config = OmegaConf.load(diffuser_path)
        loss_config = OmegaConf.load(loss_path)
        
        # 验证数值参数范围
        val_test_config = diffuser_config.multi_grasp.validation_test
        
        # 检查推理参数
        inference_config = val_test_config.inference
        assert inference_config.num_inference_grasps > 0, "推理抓取数量必须大于0"
        assert inference_config.k_samples > 0, "采样数量必须大于0"
        assert inference_config.timestep >= -1, "时间步参数无效"
        
        print(f"  ✅ 推理参数有效")
        
        # 检查匹配器参数
        matcher_config = loss_config.matcher
        algorithm_config = matcher_config.algorithm
        
        assert algorithm_config.cost_threshold > 0, "成本阈值必须大于0"
        assert matcher_config.multi_to_multi.unmatched_penalty >= 0, "未匹配惩罚必须非负"
        assert matcher_config.multi_to_multi.duplicate_penalty >= 0, "重复匹配惩罚必须非负"
        
        print(f"  ✅ 匹配器参数有效")
        
        # 检查评估参数
        eval_config = loss_config.evaluation
        assert len(eval_config.compute_topk) > 0, "Top-K评估列表不能为空"
        assert all(k > 0 for k in eval_config.compute_topk), "Top-K值必须大于0"
        assert all(0 <= t <= 1 for t in eval_config.success_thresholds), "成功阈值必须在[0,1]范围内"
        
        print(f"  ✅ 评估参数有效")
        
        print("✅ 配置参数有效性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置参数有效性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_config_validation_tests():
    """运行所有配置验证测试"""
    print("🚀 开始配置文件验证测试...")
    
    tests = [
        test_diffuser_config_loading,
        test_loss_config_loading,
        test_config_integration,
        test_config_parameter_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_func.__name__} 失败")
        except Exception as e:
            print(f"❌ {test_func.__name__} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 配置文件验证测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = run_config_validation_tests()
    sys.exit(0 if success else 1)
