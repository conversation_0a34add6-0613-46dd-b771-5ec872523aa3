#!/usr/bin/env python3
"""
测试 hand_helper.py 修复后的归一化函数是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    normalize_rot_torch, denormalize_rot_torch,
    norm_hand_pose_robust, denorm_hand_pose_robust,
    NORM_UPPER, NORM_LOWER
)

def test_normalization_range():
    """测试归一化后的值是否在正确的范围内"""
    print("=== 测试归一化范围 ===")
    
    # 创建测试数据
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_trans = torch.randn(10, 3, device=device, dtype=torch.float32)
    test_param = torch.randn(10, 16, device=device, dtype=torch.float32)
    test_rot = torch.randn(10, 4, device=device, dtype=torch.float32)  # quat
    
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # 测试平移归一化
        norm_trans = normalize_trans_torch(test_trans, mode)
        print(f"平移归一化范围: [{norm_trans.min().item():.4f}, {norm_trans.max().item():.4f}]")
        print(f"期望范围: [{NORM_LOWER}, {NORM_UPPER}]")
        
        # 测试参数归一化
        norm_param = normalize_param_torch(test_param, mode)
        print(f"参数归一化范围: [{norm_param.min().item():.4f}, {norm_param.max().item():.4f}]")
        
        # 测试旋转归一化（quat不归一化，应该返回原值）
        norm_rot = normalize_rot_torch(test_rot, 'quat', mode)
        print(f"四元数归一化（应该不变）: {torch.allclose(test_rot, norm_rot)}")
        
        return True
        
    except Exception as e:
        print(f"归一化测试失败: {e}")
        return False

def test_normalization_inverse():
    """测试归一化和反归一化是否互为逆操作"""
    print("\n=== 测试归一化逆操作 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_trans = torch.randn(5, 3, device=device, dtype=torch.float32) * 0.1  # 小范围测试
    mode = "camera_centric_obj_mean_normalized"
    
    try:
        # 测试平移
        norm_trans = normalize_trans_torch(test_trans, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_trans - denorm_trans).max().item()
        print(f"平移逆操作误差: {trans_error:.6f}")
        
        # 测试参数
        test_param = torch.randn(5, 16, device=device, dtype=torch.float32) * 0.5
        norm_param = normalize_param_torch(test_param, mode)
        denorm_param = denormalize_param_torch(norm_param, mode)
        param_error = torch.abs(test_param - denorm_param).max().item()
        print(f"参数逆操作误差: {param_error:.6f}")
        
        return trans_error < 1e-5 and param_error < 1e-5
        
    except Exception as e:
        print(f"逆操作测试失败: {e}")
        return False

def test_multi_grasp_support():
    """测试多抓取格式支持"""
    print("\n=== 测试多抓取支持 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    B, num_grasps = 4, 8
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # 创建多抓取数据 [B, num_grasps, pose_dim]
    multi_grasp_pose = torch.randn(B, num_grasps, pose_dim, device=device, dtype=torch.float32)
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # 测试多抓取归一化
        norm_multi_pose = norm_hand_pose_robust(multi_grasp_pose, rot_type, mode)
        print(f"多抓取输入形状: {multi_grasp_pose.shape}")
        print(f"多抓取输出形状: {norm_multi_pose.shape}")
        
        # 测试多抓取反归一化
        denorm_multi_pose = denorm_hand_pose_robust(norm_multi_pose, rot_type, mode)
        print(f"多抓取反归一化形状: {denorm_multi_pose.shape}")
        
        # 检查形状一致性
        shape_match = (multi_grasp_pose.shape == norm_multi_pose.shape == denorm_multi_pose.shape)
        print(f"形状一致性: {shape_match}")
        
        # 检查数值一致性（对于quat部分应该完全一致）
        quat_part_orig = multi_grasp_pose[:, :, -4:]
        quat_part_denorm = denorm_multi_pose[:, :, -4:]
        quat_error = torch.abs(quat_part_orig - quat_part_denorm).max().item()
        print(f"四元数部分误差: {quat_error:.6f}")
        
        return shape_match and quat_error < 1e-5
        
    except Exception as e:
        print(f"多抓取测试失败: {e}")
        return False

def test_single_grasp_compatibility():
    """测试单抓取向后兼容性"""
    print("\n=== 测试单抓取兼容性 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    pose_dim = 3 + 16 + 4  # trans + joint + quat
    
    # 创建单抓取数据 [B, pose_dim]
    single_grasp_pose = torch.randn(4, pose_dim, device=device, dtype=torch.float32)
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # 测试单抓取归一化
        norm_single_pose = norm_hand_pose_robust(single_grasp_pose, rot_type, mode)
        print(f"单抓取输入形状: {single_grasp_pose.shape}")
        print(f"单抓取输出形状: {norm_single_pose.shape}")
        
        # 测试单抓取反归一化
        denorm_single_pose = denorm_hand_pose_robust(norm_single_pose, rot_type, mode)
        print(f"单抓取反归一化形状: {denorm_single_pose.shape}")
        
        # 检查形状一致性
        shape_match = (single_grasp_pose.shape == norm_single_pose.shape == denorm_single_pose.shape)
        print(f"形状一致性: {shape_match}")
        
        return shape_match
        
    except Exception as e:
        print(f"单抓取测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试 hand_helper.py 修复...")
    
    tests = [
        ("归一化范围测试", test_normalization_range),
        ("归一化逆操作测试", test_normalization_inverse),
        ("多抓取支持测试", test_multi_grasp_support),
        ("单抓取兼容性测试", test_single_grasp_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name}: ❌ 异常 - {e}")
    
    print("\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！hand_helper.py 修复成功。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
