#!/usr/bin/env python3
"""
最终测试归一化代码的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    norm_hand_pose_robust, denorm_hand_pose_robust,
    NORM_UPPER, NORM_LOWER
)

def test_all_normalization_functions():
    """测试所有归一化函数"""
    print("=== 最终归一化测试 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # 测试数据
    test_trans = torch.tensor([[-0.1, 0.0, -0.05]], device=device, dtype=torch.float32)
    test_params = torch.randn(1, 16, device=device, dtype=torch.float32) * 0.5
    
    results = []
    
    try:
        # 1. 测试平移归一化
        norm_trans = normalize_trans_torch(test_trans, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_trans - denorm_trans).max().item()
        
        print(f"平移往返误差: {trans_error:.8f}")
        results.append(("平移", trans_error < 1e-5))
        
        # 2. 测试参数归一化
        norm_params = normalize_param_torch(test_params, mode)
        denorm_params = denormalize_param_torch(norm_params, mode)
        param_error = torch.abs(test_params - denorm_params).max().item()
        
        print(f"参数往返误差: {param_error:.8f}")
        results.append(("参数", param_error < 1e-5))
        
        # 3. 测试多抓取支持
        B, num_grasps = 2, 4
        pose_dim = 3 + 16 + 4  # trans + joint + quat
        multi_grasp_pose = torch.randn(B, num_grasps, pose_dim, device=device, dtype=torch.float32) * 0.1
        
        norm_multi = norm_hand_pose_robust(multi_grasp_pose, 'quat', mode)
        denorm_multi = denorm_hand_pose_robust(norm_multi, 'quat', mode)
        multi_error = torch.abs(multi_grasp_pose - denorm_multi).max().item()
        
        print(f"多抓取往返误差: {multi_error:.8f}")
        results.append(("多抓取", multi_error < 1e-4))
        
        # 4. 测试边界值
        from utils.hand_helper import get_min_max_from_stats
        labels = ['translation_x', 'translation_y', 'translation_z']
        t_min, t_max = get_min_max_from_stats(mode, labels, device, torch.float32)
        
        boundary_values = torch.stack([t_min, t_max], dim=0)
        norm_boundary = normalize_trans_torch(boundary_values, mode)
        
        min_correct = torch.allclose(norm_boundary[0], torch.full_like(norm_boundary[0], NORM_LOWER), atol=1e-6)
        max_correct = torch.allclose(norm_boundary[1], torch.full_like(norm_boundary[1], NORM_UPPER), atol=1e-6)
        
        print(f"边界值归一化: min={min_correct}, max={max_correct}")
        results.append(("边界值", min_correct and max_correct))
        
        return results
        
    except Exception as e:
        print(f"测试异常: {e}")
        return [("异常", False)]

def test_code_style_consistency():
    """检查代码风格一致性"""
    print("\n=== 代码风格检查 ===")
    
    # 读取源代码文件
    with open('utils/hand_helper.py', 'r') as f:
        content = f.read()
    
    # 检查是否所有归一化都使用统一的公式
    old_style_count = content.count('- ((NORM_UPPER - NORM_LOWER) / 2.0)')
    new_style_count = content.count('+ NORM_LOWER')
    
    print(f"旧风格公式数量: {old_style_count}")
    print(f"新风格公式数量: {new_style_count}")
    
    if old_style_count == 0:
        print("✅ 所有归一化函数已统一使用新风格")
        return True
    else:
        print("⚠️  仍有函数使用旧风格")
        return False

def main():
    """主函数"""
    print("进行最终的归一化代码检查...")
    
    # 功能测试
    test_results = test_all_normalization_functions()
    
    # 风格检查
    style_consistent = test_code_style_consistency()
    
    print(f"\n=== 最终总结 ===")
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅" if passed else "❌"
        print(f"{status} {test_name}: {'通过' if passed else '失败'}")
        if not passed:
            all_passed = False
    
    style_status = "✅" if style_consistent else "⚠️"
    print(f"{style_status} 代码风格: {'一致' if style_consistent else '不一致'}")
    
    if all_passed and style_consistent:
        print("\n🎉 你的归一化代码完全正确且风格一致！")
        print("✅ 数学正确性: 通过")
        print("✅ 多抓取支持: 完整")
        print("✅ 代码风格: 统一")
        print("✅ 可以安全用于多抓取生成框架")
    elif all_passed:
        print("\n✅ 你的归一化代码数学上完全正确！")
        print("⚠️  建议统一代码风格以提高可读性")
    else:
        print("\n❌ 归一化代码存在问题，需要修复")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
