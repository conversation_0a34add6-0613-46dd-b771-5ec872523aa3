#!/usr/bin/env python3
"""
Final comprehensive tests for hand_helper.py with proper usage
"""

import sys
import os
import torch
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    normalize_rot_torch, denormalize_rot_torch,
    norm_hand_pose_robust, denorm_hand_pose_robust,
    normalize_rot6d_torch,
    normalize_trans_numpy, denormalize_trans_numpy,
    normalize_param_numpy, denormalize_param_numpy,
    normalize_rot_numpy, denormalize_rot_numpy,
    norm_hand_pose, denorm_hand_pose,
    NORM_UPPER, NORM_LOWER
)

def test_torch_functions():
    """Test all PyTorch normalization functions with proper usage"""
    print("=== Testing PyTorch functions ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # Create test data within statistical ranges
    test_trans = torch.tensor([
        [0.1, 0.15, 0.05],   # Within range
        [-0.1, -0.15, -0.1], # Within range
    ], device=device, dtype=torch.float32)
    
    # Parameter values within ranges from statistics
    test_param = torch.tensor([
        [0.8, 0.0, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 1.7, 0.1, 0.0, -0.2],
        [0.6, 0.1, 0.4, 0.2, 0.9, -0.3, 0.5, 0.0, 0.6, -0.2, 0.6, 0.2, 1.5, 0.2, 0.1, -0.1],
    ], device=device, dtype=torch.float32)
    
    # Test inverse operations first (these should always work)
    try:
        # Translation
        norm_trans = normalize_trans_torch(test_trans, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_trans - denorm_trans).max().item()
        print(f"Translation inverse error: {trans_error:.8f}")
        
        # Parameters
        norm_param = normalize_param_torch(test_param, mode)
        denorm_param = denormalize_param_torch(norm_param, mode)
        param_error = torch.abs(test_param - denorm_param).max().item()
        print(f"Parameter inverse error: {param_error:.8f}")
        
        # Rotations (quaternion - no statistical normalization)
        test_rot_quat = torch.tensor([
            [1.0, 0.0, 0.0, 0.0],  # Identity
            [0.923, 0.0, 0.0, 0.383],  # Some rotation
        ], device=device, dtype=torch.float32)
        
        norm_rot_quat = normalize_rot_torch(test_rot_quat, 'quat', mode)
        denorm_rot_quat = denormalize_rot_torch(norm_rot_quat, 'quat', mode)
        quat_error = torch.abs(test_rot_quat - denorm_rot_quat).max().item()
        print(f"Quaternion inverse error: {quat_error:.8f}")
        
        # R6D - test with proper statistical normalization
        # First orthogonalize, then apply statistical normalization
        test_rot_r6d_raw = torch.tensor([
            [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Identity
        ], device=device, dtype=torch.float32)
        
        # Orthogonalize first
        test_rot_r6d_ortho = normalize_rot6d_torch(test_rot_r6d_raw)
        print(f"Orthogonalized R6D: {test_rot_r6d_ortho}")
        
        # Then apply statistical normalization
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d_ortho, 'r6d', mode)
        denorm_rot_r6d = denormalize_rot_torch(norm_rot_r6d, 'r6d', mode)
        r6d_error = torch.abs(test_rot_r6d_ortho - denorm_rot_r6d).max().item()
        print(f"R6D inverse error: {r6d_error:.8f}")
        
        # Check that all errors are small
        tolerance = 1e-6
        inverse_success = (trans_error < tolerance and 
                          param_error < tolerance and 
                          quat_error < tolerance and 
                          r6d_error < tolerance)
        print(f"Inverse operations success: {inverse_success}")
        
        return inverse_success
        
    except Exception as e:
        print(f"PyTorch functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numpy_functions():
    """Test all NumPy normalization functions with proper usage"""
    print("\n=== Testing NumPy functions ===")
    
    mode = "camera_centric_obj_mean_normalized"
    
    # Create test data within statistical ranges
    test_trans = np.array([
        [0.1, 0.15, 0.05],   # Within range
        [-0.1, -0.15, -0.1], # Within range
    ], dtype=np.float32)
    
    # Parameter values within ranges from statistics
    test_param = np.array([
        [0.8, 0.0, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 1.7, 0.1, 0.0, -0.2],
        [0.6, 0.1, 0.4, 0.2, 0.9, -0.3, 0.5, 0.0, 0.6, -0.2, 0.6, 0.2, 1.5, 0.2, 0.1, -0.1],
    ], dtype=np.float32)
    
    try:
        # Translation
        norm_trans = normalize_trans_numpy(test_trans, mode)
        denorm_trans = denormalize_trans_numpy(norm_trans, mode)
        trans_error = np.abs(test_trans - denorm_trans).max()
        print(f"Translation inverse error: {trans_error:.8f}")
        
        # Parameters
        norm_param = normalize_param_numpy(test_param, mode)
        denorm_param = denormalize_param_numpy(norm_param, mode)
        param_error = np.abs(test_param - denorm_param).max()
        print(f"Parameter inverse error: {param_error:.8f}")
        
        # Rotations (quaternion - no statistical normalization)
        test_rot_quat = np.array([
            [1.0, 0.0, 0.0, 0.0],  # Identity
            [0.923, 0.0, 0.0, 0.383],  # Some rotation
        ], dtype=np.float32)
        
        norm_rot_quat = normalize_rot_numpy(test_rot_quat, 'quat', mode)
        denorm_rot_quat = denormalize_rot_numpy(norm_rot_quat, 'quat', mode)
        quat_error = np.abs(test_rot_quat - denorm_rot_quat).max()
        print(f"Quaternion inverse error: {quat_error:.8f}")
        
        # Check that all errors are small
        tolerance = 1e-6
        inverse_success = (trans_error < tolerance and 
                          param_error < tolerance and 
                          quat_error < tolerance)
        print(f"Inverse operations success: {inverse_success}")
        
        return inverse_success
        
    except Exception as e:
        print(f"NumPy functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pose_functions():
    """Test pose-level normalization functions"""
    print("\n=== Testing pose functions ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    rot_type = 'quat'
    
    try:
        # Single grasp pose [B, 23]
        B = 2
        pose_dim = 3 + 16 + 4  # trans + joint + quat
        single_pose = torch.randn(B, pose_dim, device=device, dtype=torch.float32) * 0.1
        
        # Multi-grasp pose [B, num_grasps, 23]
        multi_pose = torch.randn(B, 3, pose_dim, device=device, dtype=torch.float32) * 0.1
        
        # Test single grasp
        norm_single = norm_hand_pose_robust(single_pose, rot_type, mode)
        denorm_single = denorm_hand_pose_robust(norm_single, rot_type, mode)
        single_error = torch.abs(single_pose - denorm_single).max().item()
        print(f"Single grasp inverse error: {single_error:.8f}")
        
        # Test multi-grasp
        norm_multi = norm_hand_pose_robust(multi_pose, rot_type, mode)
        denorm_multi = denorm_hand_pose_robust(norm_multi, rot_type, mode)
        multi_error = torch.abs(multi_pose - denorm_multi).max().item()
        print(f"Multi-grasp inverse error: {multi_error:.8f}")
        
        # Check errors
        tolerance = 1e-6
        pose_success = (single_error < tolerance and multi_error < tolerance)
        print(f"Pose functions success: {pose_success}")
        
        return pose_success
        
    except Exception as e:
        print(f"Pose functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Starting final comprehensive tests for hand_helper.py...")
    
    tests = [
        ("PyTorch functions", test_torch_functions),
        ("NumPy functions", test_numpy_functions),
        ("Pose functions", test_pose_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name}: ❌ EXCEPTION - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*40)
    print("FINAL TEST SUMMARY")
    print("="*40)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! hand_helper.py is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)