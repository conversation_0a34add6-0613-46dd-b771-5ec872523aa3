"""
测试重构后的扩散模型
验证模块化设计的正确性
"""

import torch
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_diffusion_imports():
    """测试扩散模块的导入"""
    try:
        from models.diffusion import DDPMModel, DDPMCore, DDPMSampling
        from models.diffusion.utils import handle_multi_grasp_operation, prepare_cfg_data
        print("✅ 扩散模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 扩散模块导入失败: {e}")
        return False

def test_lightning_imports():
    """测试Lightning模块的导入"""
    try:
        from models.lightning import DDPMTrainingMixin, DDPMValidationMixin
        print("✅ Lightning模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Lightning模块导入失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        from models.diffuser_lightning import DDPMLightning
        print("✅ 向后兼容接口导入成功")
        return True
    except ImportError as e:
        print(f"❌ 向后兼容接口导入失败: {e}")
        return False

def test_ddpm_core_basic():
    """测试DDPMCore基本功能"""
    try:
        from models.diffusion.core import DDPMCore
        
        # 创建一个简单的噪声预测网络
        class SimpleEpsModel(torch.nn.Module):
            def __init__(self, input_dim=25):
                super().__init__()
                self.linear = torch.nn.Linear(input_dim, input_dim)
            
            def forward(self, x, t, data):
                return self.linear(x)
        
        eps_model = SimpleEpsModel()
        schedule_cfg = {
            'schedule': 'linear',
            'beta_start': 0.0001,
            'beta_end': 0.02
        }
        
        core = DDPMCore(
            eps_model=eps_model,
            timesteps=100,
            schedule_cfg=schedule_cfg,
            pred_x0=False
        )
        
        # 测试前向扩散
        B, pose_dim = 2, 25
        x0 = torch.randn(B, pose_dim)
        t = torch.randint(0, 100, (B,))
        noise = torch.randn_like(x0)
        
        x_t = core.q_sample(x0, t, noise)
        assert x_t.shape == x0.shape, f"q_sample输出形状错误: {x_t.shape} vs {x0.shape}"
        
        # 测试模型预测
        data = {'dummy': torch.randn(B, 10)}
        pred_noise, pred_x0 = core.model_predict(x_t, t, data)
        assert pred_noise.shape == x0.shape, f"pred_noise形状错误: {pred_noise.shape}"
        assert pred_x0.shape == x0.shape, f"pred_x0形状错误: {pred_x0.shape}"
        
        print("✅ DDPMCore基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ DDPMCore基本功能测试失败: {e}")
        return False

def test_multi_grasp_support():
    """测试多抓取格式支持"""
    try:
        from models.diffusion.core import DDPMCore
        from models.diffusion.utils import handle_multi_grasp_operation
        
        # 创建简单的操作函数
        def simple_op(x, t):
            return x * 2
        
        # 测试单抓取格式
        x_single = torch.randn(2, 25)
        t = torch.randint(0, 100, (2,))
        result_single = handle_multi_grasp_operation(x_single, t, simple_op)
        assert result_single.shape == x_single.shape, "单抓取格式处理错误"
        
        # 测试多抓取格式
        x_multi = torch.randn(2, 5, 25)  # [B, num_grasps, pose_dim]
        result_multi = handle_multi_grasp_operation(x_multi, t, simple_op)
        assert result_multi.shape == x_multi.shape, "多抓取格式处理错误"
        
        print("✅ 多抓取格式支持测试通过")
        return True
    except Exception as e:
        print(f"❌ 多抓取格式支持测试失败: {e}")
        return False

def test_cfg_data_preparation():
    """测试CFG数据准备"""
    try:
        from models.diffusion.utils import prepare_cfg_data, prepare_cfg_data_with_negative
        
        # 准备测试数据
        B = 2
        data = {
            'rgb': torch.randn(B, 3, 224, 224),
            'norm_pose': torch.randn(B, 25),
            'condition_mask': torch.ones(B, dtype=torch.bool)
        }
        
        # 测试标准CFG数据准备
        cfg_data = prepare_cfg_data(data, B)
        assert cfg_data['rgb'].shape[0] == 2 * B, "CFG数据准备错误"
        assert cfg_data['norm_pose'].shape[0] == 2 * B, "CFG姿态数据准备错误"
        
        # 测试带负向引导的CFG数据准备
        data_with_neg = data.copy()
        data_with_neg['negative_data'] = {
            'rgb': torch.randn(B, 3, 224, 224),
            'norm_pose': torch.randn(B, 25)
        }
        
        cfg_data_neg = prepare_cfg_data_with_negative(data_with_neg, B)
        assert cfg_data_neg['rgb'].shape[0] == 3 * B, "负向CFG数据准备错误"
        
        print("✅ CFG数据准备测试通过")
        return True
    except Exception as e:
        print(f"❌ CFG数据准备测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试重构后的扩散模型...")
    print("=" * 50)
    
    tests = [
        test_diffusion_imports,
        test_lightning_imports,
        test_backward_compatibility,
        test_ddpm_core_basic,
        test_multi_grasp_support,
        test_cfg_data_preparation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    main()
