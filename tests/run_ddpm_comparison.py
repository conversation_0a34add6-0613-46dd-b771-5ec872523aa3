#!/usr/bin/env python3
"""
DDPM Lightning 模型对比测试运行脚本

使用方法:
python tests/run_ddpm_comparison.py

或者指定参数:
python tests/run_ddpm_comparison.py --tolerance 1e-4 --device cuda
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import logging
from test_ddpm_lightning_comparison import DDPMComparisonTester


def setup_logging():
    """设置日志格式"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('ddpm_comparison_test.log', mode='w', encoding='utf-8')
        ]
    )


def check_environment():
    """检查运行环境"""
    logger = logging.getLogger(__name__)
    
    # 检查 PyTorch 版本
    logger.info(f"PyTorch 版本: {torch.__version__}")
    
    # 检查 CUDA 可用性
    if torch.cuda.is_available():
        logger.info(f"CUDA 可用，设备数量: {torch.cuda.device_count()}")
        logger.info(f"当前 CUDA 设备: {torch.cuda.current_device()}")
        logger.info(f"设备名称: {torch.cuda.get_device_name()}")
    else:
        logger.info("CUDA 不可用，将使用 CPU")
    
    # 检查内存
    if torch.cuda.is_available():
        memory_allocated = torch.cuda.memory_allocated() / 1024**3
        memory_reserved = torch.cuda.memory_reserved() / 1024**3
        logger.info(f"GPU 内存使用: {memory_allocated:.2f}GB / {memory_reserved:.2f}GB")


def run_quick_test():
    """运行快速测试（较小的数据规模）"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 运行快速对比测试")
    
    # 创建测试器（使用较小的容差）
    tester = DDPMComparisonTester(tolerance=1e-4)
    
    # 重写创建测试数据方法以使用更小的数据
    original_create_test_data = tester.create_test_data
    
    def create_small_test_data(batch_size=2, num_grasps=2):
        return original_create_test_data(batch_size, num_grasps)
    
    tester.create_test_data = create_small_test_data
    
    # 运行测试
    results = tester.run_all_tests()
    
    return results


def run_full_test():
    """运行完整测试"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 运行完整对比测试")
    
    # 创建测试器
    tester = DDPMComparisonTester(tolerance=1e-5)
    
    # 运行测试
    results = tester.run_all_tests()
    
    return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='DDPM Lightning 模型对比测试运行脚本')
    parser.add_argument('--mode', type=str, default='quick',
                       choices=['quick', 'full'],
                       help='测试模式: quick(快速) 或 full(完整)')
    parser.add_argument('--tolerance', type=float, default=None,
                       help='数值比较容差')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备')
    parser.add_argument('--log-file', type=str, default='ddpm_comparison_test.log',
                       help='日志文件路径')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 80)
    logger.info("DDPM Lightning 模型对比测试")
    logger.info("=" * 80)
    
    # 检查环境
    check_environment()
    
    try:
        # 根据模式运行测试
        if args.mode == 'quick':
            results = run_quick_test()
        else:
            results = run_full_test()
        
        # 统计结果
        passed_tests = sum(1 for result in results.values() if result)
        total_tests = len(results)
        
        logger.info("=" * 80)
        logger.info("🏁 测试完成")
        logger.info("=" * 80)
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！")
            logger.info("✅ 重构版本与原始版本功能一致，可以安全替换")
            return 0
        else:
            logger.warning(f"⚠️  {total_tests - passed_tests} 个测试失败")
            logger.info("❌ 建议在修复问题后再进行替换")
            return 1
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
