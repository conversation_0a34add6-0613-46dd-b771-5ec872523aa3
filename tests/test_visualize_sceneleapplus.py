#!/usr/bin/env python3
"""
测试SceneLeapPlusDataset可视化功能
"""

import os
import sys
import torch
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_dataset import SceneLeapPlusDataset
from visualize_sceneleapplus_dataset import (
    create_coordinate_frame,
    create_point_cloud_from_sample,
    create_highlighted_point_cloud,
    analyze_hand_pose_format,
    create_hand_meshes,
    create_object_mesh
)

def test_dataset_loading():
    """测试数据集加载"""
    print("测试数据集加载...")
    
    # 数据路径配置
    root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15"
    succ_grasp_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
    obj_root_dir = "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
    
    try:
        # 创建数据集实例
        dataset = SceneLeapPlusDataset(
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            num_grasps=4,  # 测试用较小的数量
            mode="camera_centric",
            max_grasps_per_object=50,
            mesh_scale=0.1,
            num_neg_prompts=2,
            enable_cropping=True,
            max_points=10000,
            grasp_sampling_strategy="random"
        )
        
        print(f"✓ 数据集加载成功，包含 {len(dataset)} 个样本")
        
        if len(dataset) > 0:
            # 测试获取样本
            sample = dataset[0]
            print(f"✓ 样本获取成功")
            print(f"  - 场景ID: {sample['scene_id']}")
            print(f"  - 物体代码: {sample['obj_code']}")
            print(f"  - 点云形状: {sample['scene_pc'].shape}")
            print(f"  - 手部姿态形状: {sample['hand_model_pose'].shape}")
            print(f"  - 物体顶点形状: {sample['obj_verts'].shape}")
            
            return dataset, sample
        else:
            print("✗ 数据集为空")
            return None, None
            
    except Exception as e:
        print(f"✗ 数据集加载失败: {e}")
        return None, None

def test_hand_pose_analysis():
    """测试手部姿态分析"""
    print("\n测试手部姿态分析...")
    
    # 创建测试数据
    single_pose = torch.randn(23)
    multi_poses = torch.randn(4, 23)
    
    # 测试单个姿态
    format1 = analyze_hand_pose_format(single_pose)
    print(f"✓ 单个姿态格式分析: {format1}")
    
    # 测试多个姿态
    format2 = analyze_hand_pose_format(multi_poses)
    print(f"✓ 多个姿态格式分析: {format2}")

def test_point_cloud_creation():
    """测试点云创建"""
    print("\n测试点云创建...")
    
    # 创建测试点云数据 (N, 6) - xyz + rgb
    N = 1000
    xyz = np.random.rand(N, 3) * 2 - 1  # [-1, 1]范围
    rgb = np.random.rand(N, 3)  # [0, 1]范围
    scene_pc = torch.from_numpy(np.concatenate([xyz, rgb], axis=1)).float()
    
    # 创建测试掩码
    object_mask = torch.rand(N) > 0.7  # 30%的点为目标物体
    
    try:
        # 测试普通点云创建
        pcd = create_point_cloud_from_sample(scene_pc)
        print(f"✓ 普通点云创建成功: {len(pcd.points)} 个点")
        
        # 测试高亮点云创建
        bg_pcd, obj_pcd = create_highlighted_point_cloud(scene_pc, object_mask)
        if bg_pcd is not None and obj_pcd is not None:
            print(f"✓ 高亮点云创建成功: 背景 {len(bg_pcd.points)} 个点, 目标 {len(obj_pcd.points)} 个点")
        else:
            print("✗ 高亮点云创建失败")
            
    except Exception as e:
        print(f"✗ 点云创建失败: {e}")

def test_object_mesh_creation():
    """测试物体mesh创建"""
    print("\n测试物体mesh创建...")
    
    # 创建简单的立方体mesh数据
    vertices = torch.tensor([
        [0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0],  # 底面
        [0, 0, 1], [1, 0, 1], [1, 1, 1], [0, 1, 1]   # 顶面
    ], dtype=torch.float32)
    
    faces = torch.tensor([
        [0, 1, 2], [0, 2, 3],  # 底面
        [4, 7, 6], [4, 6, 5],  # 顶面
        [0, 4, 5], [0, 5, 1],  # 前面
        [2, 6, 7], [2, 7, 3],  # 后面
        [0, 3, 7], [0, 7, 4],  # 左面
        [1, 5, 6], [1, 6, 2]   # 右面
    ], dtype=torch.long)
    
    try:
        # 测试实体mesh
        solid_mesh = create_object_mesh(vertices, faces, color=(0, 1, 0), wireframe=False)
        if solid_mesh is not None:
            print(f"✓ 实体mesh创建成功: {len(solid_mesh.vertices)} 个顶点")
        else:
            print("✗ 实体mesh创建失败")
        
        # 测试线框mesh
        wire_mesh = create_object_mesh(vertices, faces, color=(1, 0, 0), wireframe=True)
        if wire_mesh is not None:
            print(f"✓ 线框mesh创建成功")
        else:
            print("✗ 线框mesh创建失败")
            
    except Exception as e:
        print(f"✗ 物体mesh创建失败: {e}")

def test_coordinate_frame():
    """测试坐标轴创建"""
    print("\n测试坐标轴创建...")
    
    try:
        frame = create_coordinate_frame(size=0.1)
        print(f"✓ 坐标轴创建成功")
    except Exception as e:
        print(f"✗ 坐标轴创建失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("SceneLeapPlusDataset 可视化功能测试")
    print("=" * 60)
    
    # 测试各个组件
    test_coordinate_frame()
    test_hand_pose_analysis()
    test_point_cloud_creation()
    test_object_mesh_creation()
    
    # 测试数据集加载
    dataset, sample = test_dataset_loading()
    
    if dataset is not None and sample is not None:
        print("\n✓ 所有基础测试通过！")
        print("\n可以运行以下命令进行完整可视化测试:")
        print("python visualize_sceneleapplus_dataset.py")
    else:
        print("\n✗ 数据集加载失败，请检查数据路径配置")
        print("请确保以下路径存在并包含有效数据:")
        print("  - /home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15")
        print("  - /home/<USER>/source/grasp/SceneLeapPro/data/succ_collect")
        print("  - /home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models")

if __name__ == "__main__":
    main()
