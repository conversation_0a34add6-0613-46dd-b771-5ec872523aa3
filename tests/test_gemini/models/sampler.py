import torch
from typing import Dict, Tuple

class DiffusionSampler:
    def __init__(self, noise_scheduler):
        """
        Initializes the DiffusionSampler.

        Args:
            noise_scheduler: A dictionary-like object containing the diffusion schedule parameters.
        """
        for k, v in noise_scheduler.items():
            setattr(self, k, v)

    def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        """
        Forward diffusion process: adds noise to the clean data.
        Supports single grasp format [B, pose_dim] and multi-grasp format [B, num_grasps, pose_dim].
        """
        if x0.dim() == 2:
            # Single grasp format
            B, *x_shape = x0.shape
            x_t = self.sqrt_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * x0 + \\\
                  self.sqrt_one_minus_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * noise
            return x_t
        elif x0.dim() == 3:
            # Multi-grasp format
            B, num_grasps, pose_dim = x0.shape
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)
            alpha_cumprod = self.sqrt_alphas_cumprod[t_expanded].unsqueeze(-1)
            one_minus_alpha = self.sqrt_one_minus_alphas_cumprod[t_expanded].unsqueeze(-1)
            x_t = alpha_cumprod * x0 + one_minus_alpha * noise
            return x_t
        else:
            raise ValueError(f"Unsupported input dimension: {x0.dim()}. Expected 2 or 3.")

    def _compute_pred_x0_from_noise(self, x_t: torch.Tensor, t: torch.Tensor, pred_noise: torch.Tensor) -> torch.Tensor:
        """Computes the predicted clean data from the predicted noise."""
        if x_t.dim() == 2:
            B, *x_shape = x_t.shape
            pred_x0 = self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * x_t - \\\
                      self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * pred_noise
        elif x_t.dim() == 3:
            B, num_grasps, pose_dim = x_t.shape
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)
            pred_x0 = sqrt_recip * x_t - sqrt_recipm1 * pred_noise
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}")
        return pred_x0

    def model_predict(self, eps_model, x_t: torch.Tensor, t: torch.Tensor, data: Dict, pred_x0: bool) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Model prediction: predicts noise or clean data from noisy data.
        """
        if pred_x0:
            pred_x0_out = eps_model(x_t, t, data)
            pred_noise = (self.sqrt_recip_alphas_cumprod[t].reshape(x_t.shape[0], *((1,) * (x_t.dim() - 1))) * x_t - pred_x0_out) / \\\
                         self.sqrt_recipm1_alphas_cumprod[t].reshape(x_t.shape[0], *((1,) * (x_t.dim() - 1)))
        else:
            pred_noise = eps_model(x_t, t, data)
            pred_x0_out = self._compute_pred_x0_from_noise(x_t, t, pred_noise)
        return pred_noise, pred_x0_out

    def p_mean_variance(self, eps_model, x_t: torch.Tensor, t: torch.Tensor, data: Dict, pred_x0: bool) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Calculates the mean and variance of the posterior distribution.
        """
        pred_noise, pred_x0_out = self.model_predict(eps_model, x_t, t, data, pred_x0)
        
        reshape_dims = (x_t.shape[0],) + (1,) * (x_t.dim() - 1)
        
        model_mean = self.posterior_mean_coef1[t].reshape(*reshape_dims) * pred_x0_out + \\\
                     self.posterior_mean_coef2[t].reshape(*reshape_dims) * x_t
        posterior_variance = self.posterior_variance[t].reshape(*reshape_dims)
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(*reshape_dims)

        return model_mean, posterior_variance, posterior_log_variance

    @torch.no_grad()
    def p_sample(self, eps_model, x_t: torch.Tensor, t: int, data: Dict, pred_x0: bool, use_cfg: bool, guidance_scale: float, use_negative_guidance: bool, negative_guidance_scale: float) -> torch.Tensor:
        B, *_ = x_t.shape
        batch_timestep = torch.full((B,), t, device=x_t.device, dtype=torch.long)

        if use_cfg:
            model_mean, model_variance, model_log_variance = self.p_mean_variance_cfg(eps_model, x_t, batch_timestep, data, pred_x0, guidance_scale, use_negative_guidance, negative_guidance_scale)
        else:
            model_mean, model_variance, model_log_variance = self.p_mean_variance(eps_model, x_t, batch_timestep, data, pred_x0)

        noise = torch.randn_like(x_t) if t > 0 else 0.
        pred_x = model_mean + (0.5 * model_log_variance).exp() * noise
        return pred_x

    @torch.no_grad()
    def p_sample_loop(self, eps_model, data: Dict, pred_x0: bool, use_cfg: bool, guidance_scale: float, use_negative_guidance: bool, negative_guidance_scale: float) -> torch.Tensor:
        """
        Sampling loop: gradually denoises from noise to generate clean data.
        """
        if 'norm_pose' in data and isinstance(data['norm_pose'], torch.Tensor):
            x_t = torch.randn_like(data['norm_pose'], device=data['norm_pose'].device)
        else:
            raise ValueError("norm_pose not found in data or is not a tensor")

        condition_dict = eps_model.condition(data)
        data.update(condition_dict)

        all_x_t = [x_t]
        for t in reversed(range(0, self.timesteps)):
            x_t = self.p_sample(eps_model, x_t, t, data, pred_x0, use_cfg, guidance_scale, use_negative_guidance, negative_guidance_scale)
            all_x_t.append(x_t)

        return torch.stack(all_x_t, dim=1)
