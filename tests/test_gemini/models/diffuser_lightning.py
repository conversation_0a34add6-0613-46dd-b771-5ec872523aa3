import pytorch_lightning as pl
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any, List
from models.decoder import build_decoder
from models.loss import GraspLossPose
from models.utils.diffusion_utils import make_schedule_ddpm
from utils.hand_helper import process_hand_pose, process_hand_pose_test, denorm_hand_pose_robust
from statistics import mean
import logging
from .utils.log_colors import HEADER, BLUE, GREEN, YELLOW, RED, ENDC, BOLD, UNDERLINE
from .sampler import DiffusionSampler

class DDPMLightning(pl.LightningModule):
    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning model{ENDC}")
        self.save_hyperparameters()
        
        self.eps_model = build_decoder(cfg.decoder)
        self.criterion = GraspLossPose(cfg.criterion)
        self.loss_weights = cfg.criterion.loss_weights
        self.rot_type = cfg.rot_type
        self.batch_size = cfg.batch_size
        self.print_freq = cfg.print_freq
        self.use_score = cfg.get('use_score', False)
        self.score_pretrain = cfg.get('score_pretrain', False)
        
        self.timesteps = cfg.steps
        self.schedule_cfg = cfg.schedule_cfg
        self.rand_t_type = cfg.rand_t_type
        self.pred_x0 = cfg.pred_x0
        self.mode = cfg.mode
        
        self.schedule = make_schedule_ddpm(self.timesteps, **self.schedule_cfg)
        for k, v in self.schedule.items():
            self.register_buffer(k, v)
            
        self.sampler = DiffusionSampler(self.schedule)
        
        self.optimizer_cfg = cfg.optimizer
        self.scheduler = cfg.scheduler

        self.use_cfg = cfg.get('use_cfg', False)
        self.guidance_scale = cfg.get('guidance_scale', 7.5)
        self.use_negative_guidance = cfg.get('use_negative_guidance', False)
        self.negative_guidance_scale = cfg.get('negative_guidance_scale', 1.0)

    def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        模型预测：根据噪声数据预测噪声或干净数据
        支持单抓取格式 [B, pose_dim] 和多抓取格式 [B, num_grasps, pose_dim]
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._model_predict_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._model_predict_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _model_predict_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """单抓取格式的模型预测"""
        B, *x_shape = x_t.shape
        if self.pred_x0:
            pred_x0 = self.eps_model(x_t, t, data)
            pred_noise = (self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - pred_x0) \
                            / self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape)))
        else:
            pred_noise = self.eps_model(x_t, t, data)
            pred_x0 = self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                        self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * pred_noise
        return pred_noise, pred_x0

    def _model_predict_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """多抓取格式的模型预测"""
        B, num_grasps, pose_dim = x_t.shape

        if self.pred_x0:
            # 预测干净数据
            pred_x0 = self.eps_model(x_t, t, data)  # [B, num_grasps, pose_dim]

            # 计算预测噪声
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

            pred_noise = (sqrt_recip * x_t - pred_x0) / sqrt_recipm1
        else:
            # 预测噪声
            pred_noise = self.eps_model(x_t, t, data)  # [B, num_grasps, pose_dim]

            # 计算预测干净数据
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

            pred_x0 = sqrt_recip * x_t - sqrt_recipm1 * pred_noise

        return pred_noise, pred_x0

    def _compute_pred_x0_from_noise(self, x_t: torch.Tensor, t: torch.Tensor, pred_noise: torch.Tensor) -> torch.Tensor:
        """从预测噪声计算预测的干净数据"""
        if x_t.dim() == 2:
            # 单抓取格式
            B, *x_shape = x_t.shape
            pred_x0 = self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                     self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * pred_noise
        elif x_t.dim() == 3:
            # 多抓取格式
            B, num_grasps, pose_dim = x_t.shape
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            pred_x0 = sqrt_recip * x_t - sqrt_recipm1 * pred_noise
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}")
        return pred_x0

    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算后验分布的均值和方差
        支持单抓取和多抓取格式
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._p_mean_variance_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._p_mean_variance_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _p_mean_variance_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """单抓取格式的后验分布计算"""
        B, *x_shape = x_t.shape
        pred_noise, pred_x0 = self.model_predict(x_t, t, data)

        model_mean = self.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape))) * pred_x0 + \
            self.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape))) * x_t
        posterior_variance = self.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))

        return model_mean, posterior_variance, posterior_log_variance

    def _p_mean_variance_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """多抓取格式的后验分布计算"""
        B, num_grasps, pose_dim = x_t.shape
        pred_noise, pred_x0 = self.model_predict(x_t, t, data)

        # 时间步扩展
        t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]

        # 获取后验分布系数并扩展维度
        coef1 = self.posterior_mean_coef1[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        coef2 = self.posterior_mean_coef2[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        # 计算后验均值
        model_mean = coef1 * pred_x0 + coef2 * x_t

        # 计算后验方差
        posterior_variance = self.posterior_variance[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        posterior_log_variance = self.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        return model_mean, posterior_variance, posterior_log_variance

    def p_mean_variance_cfg(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        分类器自由引导的后验分布计算
        支持单抓取和多抓取格式
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._p_mean_variance_cfg_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._p_mean_variance_cfg_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _p_mean_variance_cfg_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """单抓取格式的CFG"""
        B, *x_shape = x_t.shape

        guidance_scale = getattr(self, 'guidance_scale', 7.5)
        negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        x_t_expanded = x_t.repeat(3, 1)
        t_expanded = t.repeat(3)

        data_expanded = self._prepare_cfg_data(data, B)

        pred_noise_all, pred_x0_all = self.model_predict(x_t_expanded, t_expanded, data_expanded)

        pred_noise_uncond = pred_noise_all[:B]
        pred_noise_pos = pred_noise_all[B:2*B]
        pred_noise_neg = pred_noise_all[2*B:3*B]

        pred_x0_uncond = pred_x0_all[:B]
        pred_x0_pos = pred_x0_all[B:2*B]
        pred_x0_neg = pred_x0_all[2*B:3*B]

        if hasattr(self, 'use_negative_guidance') and self.use_negative_guidance:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                          negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                        negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
        else:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond)

        model_mean = self.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape))) * guided_x0 + \
            self.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape))) * x_t
        posterior_variance = self.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))

        return model_mean, posterior_variance, posterior_log_variance

    def _p_mean_variance_cfg_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """多抓取格式的CFG"""
        B, num_grasps, pose_dim = x_t.shape

        guidance_scale = getattr(self, 'guidance_scale', 7.5)
        negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        # 扩展输入: [B, num_grasps, pose_dim] -> [3*B, num_grasps, pose_dim]
        x_t_expanded = x_t.repeat(3, 1, 1)
        t_expanded = t.repeat(3)

        data_expanded = self._prepare_cfg_data(data, B)

        pred_noise_all, pred_x0_all = self.model_predict(x_t_expanded, t_expanded, data_expanded)

        # 分离无条件、正向、负向预测
        pred_noise_uncond = pred_noise_all[:B]
        pred_noise_pos = pred_noise_all[B:2*B]
        pred_noise_neg = pred_noise_all[2*B:3*B]

        pred_x0_uncond = pred_x0_all[:B]
        pred_x0_pos = pred_x0_all[B:2*B]
        pred_x0_neg = pred_x0_all[2*B:3*B]

        # CFG计算
        if hasattr(self, 'use_negative_guidance') and self.use_negative_guidance:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                          negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                        negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
        else:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond)

        # 计算后验分布参数（适配多抓取维度）
        t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
        coef1 = self.posterior_mean_coef1[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        coef2 = self.posterior_mean_coef2[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        model_mean = coef1 * guided_x0 + coef2 * x_t
        posterior_variance = self.posterior_variance[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        posterior_log_variance = self.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        return model_mean, posterior_variance, posterior_log_variance

    def _prepare_cfg_data(self, data: Dict, B: int) -> Dict:
        """
        为CFG准备数据，支持多抓取格式
        """
        cfg_data = {}

        # 处理张量数据
        for key in ['scene_pc', 'norm_pose', 'scene_cond']:
            if key in data and isinstance(data[key], torch.Tensor):
                if data[key].dim() == 2:
                    cfg_data[key] = data[key].repeat(3, 1)
                elif data[key].dim() == 3:
                    # 多抓取格式: [B, num_grasps, pose_dim] -> [3*B, num_grasps, pose_dim]
                    cfg_data[key] = data[key].repeat(3, 1, 1)
                elif data[key].dim() == 4:
                    cfg_data[key] = data[key].repeat(3, 1, 1, 1)
                else:
                    cfg_data[key] = data[key].repeat(3)
            elif key in data:
                # 非张量数据
                cfg_data[key] = data[key] * 3

        # 文本条件处理保持不变（每个场景一个文本条件）
        if 'text_cond' in data and data['text_cond'] is not None:
            text_cond = data['text_cond']

            uncond_text = torch.zeros_like(text_cond)
            pos_text = text_cond
            if 'neg_pred' in data and data['neg_pred'] is not None:
                neg_text = data['neg_pred']
            else:
                neg_text = torch.zeros_like(text_cond)

            cfg_data['text_cond'] = torch.cat([uncond_text, pos_text, neg_text], dim=0)
        else:
            cfg_data['text_cond'] = None

        for key in ['neg_pred', 'neg_text_features', 'text_mask']:
            if key in data and data[key] is not None:
                if isinstance(data[key], torch.Tensor):
                    if data[key].dim() == 2:
                        cfg_data[key] = data[key].repeat(3, 1)
                    elif data[key].dim() == 3:
                        cfg_data[key] = data[key].repeat(3, 1, 1)
                    else:
                        cfg_data[key] = data[key].repeat(3)
                else:
                    cfg_data[key] = data[key]

        return cfg_data

    @torch.no_grad()
    def p_sample(self, x_t: torch.Tensor, t: int, data: Dict) -> torch.Tensor:
        B, *_ = x_t.shape
        batch_timestep = torch.full((B, ), t, device=self.device, dtype=torch.long)

        if hasattr(self, 'use_cfg') and self.use_cfg and self.training == False:
            model_mean, model_variance, model_log_variance = self.p_mean_variance_cfg(x_t, batch_timestep, data)
        else:
            model_mean, model_variance, model_log_variance = self.p_mean_variance(x_t, batch_timestep, data)

        noise = torch.randn_like(x_t) if t > 0 else 0.
        pred_x = model_mean + (0.5 * model_log_variance).exp() * noise
        return pred_x
    
    @torch.no_grad()
    def p_sample_loop(self, data: Dict) -> torch.Tensor:
        """
        采样循环：从噪声逐步去噪生成干净数据
        支持单抓取和多抓取格式，实现 One-shot Parallel Decoding
        """
        # 根据数据格式确定初始噪声形状
        if 'norm_pose' in data:
            if isinstance(data['norm_pose'], torch.Tensor):
                if data['norm_pose'].dim() == 3:
                    # 多抓取格式: [B, num_grasps, pose_dim]
                    x_t = torch.randn_like(data['norm_pose'], device=self.device)
                elif data['norm_pose'].dim() == 2:
                    # 单抓取格式: [B, pose_dim]
                    x_t = torch.randn_like(data['norm_pose'], device=self.device)
                else:
                    raise ValueError(f"Unsupported norm_pose dimension: {data['norm_pose'].dim()}")
            else:
                # 列表格式处理（向后兼容）
                x_t = torch.randn(len(data['norm_pose']), data['norm_pose'][0].shape[-1], device=self.device)
        else:
            raise ValueError("norm_pose not found in data")

        # 条件编码
        condition_dict = self.eps_model.condition(data)
        data.update(condition_dict)

        # 逐步去噪
        all_x_t = [x_t]
        for t in reversed(range(0, self.timesteps)):
            x_t = self.p_sample(x_t, t, data)
            all_x_t.append(x_t)

        # 返回所有时间步的结果
        # 单抓取: [B, timesteps+1, pose_dim]
        # 多抓取: [B, timesteps+1, num_grasps, pose_dim]
        return torch.stack(all_x_t, dim=1)
    
    @torch.no_grad()
    def sample(self, data: Dict, k: int = 1, use_cfg: bool = None, guidance_scale: float = None,
               use_negative_guidance: bool = None, negative_guidance_scale: float = None) -> torch.Tensor:
        original_use_cfg = getattr(self, 'use_cfg', False)
        original_guidance_scale = getattr(self, 'guidance_scale', 7.5)
        original_use_negative_guidance = getattr(self, 'use_negative_guidance', False)
        original_negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        if use_cfg is not None:
            self.use_cfg = use_cfg
        if guidance_scale is not None:
            self.guidance_scale = guidance_scale
        if use_negative_guidance is not None:
            self.use_negative_guidance = use_negative_guidance
        if negative_guidance_scale is not None:
            self.negative_guidance_scale = negative_guidance_scale

        try:
            ksamples = []
            for _ in range(k):
                ksamples.append(self.p_sample_loop(data))
            ksamples = torch.stack(ksamples, dim=1)
            return ksamples
        finally:
            self.use_cfg = original_use_cfg
            self.guidance_scale = original_guidance_scale
            self.use_negative_guidance = original_use_negative_guidance
            self.negative_guidance_scale = original_negative_guidance_scale

    def forward_train(self, batch):
        batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)
        B = batch['norm_pose'].shape[0]

        ts = self._sample_timesteps(B)
        noise = torch.randn_like(batch['norm_pose'], device=self.device)
        x_t = self.q_sample(x0=batch['norm_pose'], t=ts, noise=noise)

        condition_dict = self.eps_model.condition(batch)
        batch.update(condition_dict)

        output = self.eps_model(x_t, ts, batch)

        if self.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            B, *x_shape = x_t.shape
            pred_x0 = self.sqrt_recip_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                     self.sqrt_recipm1_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * output
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }

        if 'neg_pred' in condition_dict and condition_dict['neg_pred'] is not None:
            pred_dict['neg_pred'] = condition_dict['neg_pred']
            pred_dict['neg_text_features'] = condition_dict['neg_text_features']

        loss_dict = self.criterion(pred_dict, batch)

        # loss = 0
        # for k, v in loss_dict.items():
        #     if k in self.loss_weights:
        #         loss += v * self.loss_weights[k]
        #     elif k == 'neg_loss' and hasattr(self.criterion, 'neg_loss_weight'):
        #         loss += v * self.criterion.neg_loss_weight
        loss = 0
        for k, v in loss_dict.items():
            if k in self.loss_weights:
                loss += v * self.loss_weights[k]

        return loss, loss_dict

    def training_step(self, batch, batch_idx):
        """
        训练步骤：支持单抓取和多抓取并行训练
        """
        # 手部姿态处理（已支持多抓取）
        batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)

        # 获取批次大小和抓取数量
        norm_pose = batch['norm_pose']
        if norm_pose.dim() == 3:
            # 多抓取格式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = norm_pose.shape
            total_samples = B * num_grasps  # 用于日志记录的有效样本数
        elif norm_pose.dim() == 2:
            # 单抓取格式: [B, pose_dim]
            B = norm_pose.shape[0]
            total_samples = B
        else:
            raise ValueError(f"Unsupported norm_pose dimension: {norm_pose.dim()}")

        # 时间步采样
        ts = self._sample_timesteps(B)

        # 噪声生成和加噪
        noise = torch.randn_like(norm_pose, device=self.device)
        x_t = self.q_sample(x0=norm_pose, t=ts, noise=noise)

        # 条件编码
        condition_dict = self.eps_model.condition(batch)
        batch.update(condition_dict)

        # 模型前向传播
        output = self.eps_model(x_t, ts, batch)

        # 构建预测字典
        if self.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            # 计算pred_x0（需要适配多抓取格式）
            pred_x0 = self._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }

        # 添加负向条件（如果存在）
        if 'neg_pred' in condition_dict and condition_dict['neg_pred'] is not None:
            pred_dict['neg_pred'] = condition_dict['neg_pred']
            pred_dict['neg_text_features'] = condition_dict['neg_text_features']

        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='train')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        # 日志记录（使用总样本数）
        self.log_dict(loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=False, batch_size=total_samples)
        self.log("train/lr", self.optimizers().param_groups[0]['lr'], batch_size=total_samples)

        if batch_idx % self.print_freq == 0:
            empty_formatter = logging.Formatter('')
            root_logger = logging.getLogger()
            original_formatters = [handler.formatter for handler in root_logger.handlers]

            for handler in root_logger.handlers:
                handler.setFormatter(empty_formatter)
            logging.info("")

            for handler, formatter in zip(root_logger.handlers, original_formatters):
                handler.setFormatter(formatter)

            logging.info(f'{HEADER}Epoch {self.current_epoch} - Batch [{batch_idx}/{len(self.trainer.train_dataloader)}]{ENDC}')
            logging.info(f'{GREEN}{"Loss:":<21s} {loss.item():.4f}{ENDC}')
            for k, v in loss_dict.items():
                logging.info(f'{BLUE}{k.title() + ":":<21s} {v.item():.4f}{ENDC}')

        return loss

    def on_validation_epoch_start(self):
        self.validation_step_outputs = []
        return

    def validation_step(self, batch, batch_idx):
        """
        验证步骤：支持多抓取并行推理
        """
        # 数据预处理保持不变（已支持多抓取）
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

        # 推理模式重构
        pred_x0 = self.sample(batch)  # 返回 [B, k, T+1, num_grasps, pose_dim] 或 [B, k, T+1, pose_dim]
        pred_x0 = pred_x0[:,0,-1]     # 取第一个采样的最后时间步

        # 根据数据维度确定处理方式
        if pred_x0.dim() == 3:
            # 多抓取模式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = pred_x0.shape
            batch_size = B * num_grasps

            # 构建多抓取预测字典
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],  # 关节角度
                "translation_norm": pred_x0[..., :3],  # 平移
                "rotation": pred_x0[..., 19:],  # 旋转
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式（向后兼容）
            pred_x0 = pred_x0 if pred_x0.dim() == 2 else pred_x0.squeeze(1)  # 确保是 [B, pose_dim]
            batch_size = pred_x0.shape[0]
            pred_dict = self._build_single_grasp_pred_dict(pred_x0)

        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='val')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        # 日志记录使用正确的batch_size
        self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
        self.validation_step_outputs.append({
            "loss": loss.item(),
            "loss_dict": {k: v.item() for k, v in loss_dict.items()}
        })

        return {"loss": loss, "loss_dict": loss_dict}

    def _build_single_grasp_pred_dict(self, pred_x0):
        """构建单抓取预测字典（向后兼容）"""
        pose_dim = pred_x0.shape[-1]
        return {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],  # 关节角度: [3:19]
            "translation_norm": pred_x0[..., :3],  # 平移: [0:3]
            "rotation": pred_x0[..., 19:],  # 旋转: [19:]
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
        
    def on_validation_epoch_end(self):
        val_loss = [x["loss"] for x in self.validation_step_outputs]
        avg_loss = mean(val_loss)
        
        val_detailed_loss = {}
        for k in self.validation_step_outputs[0]["loss_dict"].keys():
            val_detailed_loss[k] = mean([x["loss_dict"][k] for x in self.validation_step_outputs])
        
        empty_formatter = logging.Formatter('')
        root_logger = logging.getLogger()
        original_formatters = [handler.formatter for handler in root_logger.handlers]
        
        for handler in root_logger.handlers:
            handler.setFormatter(empty_formatter)
        logging.info("")
        
        for handler, formatter in zip(root_logger.handlers, original_formatters):
            handler.setFormatter(formatter)
        logging.info(f'{GREEN}Epoch {self.current_epoch} - Validation completed{ENDC}')
        
        avg_loss_str = f"{avg_loss:.4f}"
        emoji_loss = self._convert_number_to_emoji(avg_loss_str)
        logging.info(f'{BLUE}{"Loss:":<21s} {avg_loss_str} {emoji_loss}{ENDC}')
        
        for k, v in val_detailed_loss.items():
            v_str = f"{v:.4f}"
            emoji_v = self._convert_number_to_emoji(v_str)
            logging.info(f'{BLUE}{k.title() + ":":<21s} {v_str} {emoji_v}{ENDC}')
        
        self.log('val_loss', avg_loss, batch_size=self.batch_size, sync_dist=True)
        
        self.validation_step_outputs.clear()

    def on_test_start(self):
        self.metric_results = []
        return

    def test_step(self, batch, batch_idx):
        # 数据预处理
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

        # 推理
        pred_x0 = self.sample(batch)
        pred_x0 = pred_x0[:,0,-1]  # 取第一个采样的最后时间步

        # 根据维度确定处理方式
        if pred_x0.dim() == 3:
            # 多抓取模式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = pred_x0.shape
            batch_size = B * num_grasps
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式（向后兼容）
            pred_x0 = pred_x0 if pred_x0.dim() == 2 else pred_x0.squeeze(1)
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        # 指标计算
        metric_dict, metric_details = self.criterion(pred_dict, batch, mode='test')
        self.metric_results.append(metric_details)

        return metric_dict

    def test_step_teaser(self, batch):
        B = self.batch_size
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(batch)
        pred_x0 = pred_x0[:,0,-1]
        
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
        metric_dict, metric_details = self.criterion.forward_metric(pred_dict, batch)

        return metric_dict, metric_details

    def configure_optimizers(self):
        if self.optimizer_cfg.name.lower() == "adam":
            optimizer = torch.optim.Adam(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        elif self.optimizer_cfg.name.lower() == "adamw":
            optimizer = torch.optim.AdamW(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        else:
            raise NotImplementedError(f"No such optimizer: {self.optimizer_cfg.name}")
        
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            logging.info("Using score model without pretrain, will not load optimizer state")
            self.trainer.fit_loop.epoch_progress.current.completed = 0
            self.last_epoch = -1
        else:
            self.last_epoch = self.current_epoch - 1 if self.current_epoch else -1
        
        logging.info(f"Setting last_epoch to: {self.last_epoch}")
        
        if self.scheduler.name.lower() == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.scheduler.t_max,
                eta_min=self.scheduler.min_lr,
                last_epoch=self.last_epoch
            )
        elif self.scheduler.name.lower() == "steplr":
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                self.scheduler.step_size,
                gamma=self.scheduler.step_gamma,
                last_epoch=self.last_epoch
            )
            
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss"
            }
        }

    def forward_infer(self, data: Dict, k=4, timestep=-1):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, timestep]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]

        if pred_x0.dim() == 4:
            # 多抓取模式: [B, k, num_grasps, pose_dim]
            B, k, num_grasps, pose_dim = pred_x0.shape
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式: [B, k, pose_dim]
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
        return preds_hand, targets_hand
    
    def forward_infer_step(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)

        results = []
        for timestep in timesteps:
            pred_x0_t = pred_x0[:, :, timestep]
            pred_dict = {
                    "pred_pose_norm": pred_x0_t,
                    "qpos_norm": pred_x0_t[..., 3:19],
                    "translation_norm": pred_x0_t[..., :3],
                    "rotation": pred_x0_t[..., 19:],
                    "pred_noise": torch.tensor([1.0], device=self.device),
                    "noise": torch.tensor([1.0], device=self.device)
                }
            
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
            results.append(preds_hand)

        return results

    def forward_get_pose(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]
        print(f"pred_x0.shape {pred_x0.shape}")

        if pred_x0.dim() == 4:
            # 多抓取模式
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        outputs, targets = self.criterion.infer_norm_process_dict_get_pose(pred_dict, data)
        return outputs, targets

    def forward_get_pose_matched(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        print(f"pred_x0.shape {pred_x0.shape}")

        # 构建pred_dict（支持多抓取和单抓取）
        pred_dict = self._build_pred_dict_adaptive(pred_x0)

        matched_pred, matched_targets, outputs, targets = self.criterion.infer_norm_process_dict_get_pose_matched(pred_dict, data)
        return matched_pred, matched_targets, outputs, targets

    def _build_pred_dict_adaptive(self, pred_x0):
        """自适应构建预测字典"""
        return {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }

    def forward_get_pose_raw(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_pose_norm = self.sample(data, k=k)
        pred_pose_norm = pred_pose_norm[:, :, -1]
        print(f"pred_pose_norm.shape {pred_pose_norm.shape}")

        hand_model_pose = denorm_hand_pose_robust(pred_pose_norm, self.rot_type, self.mode)

        return hand_model_pose

    def forward_train_instance(self, data: Dict):
        data = process_hand_pose(data, rot_type=self.rot_type, mode=self.mode)
        B = data['norm_pose'].shape[0]
        
        if self.rand_t_type == 'all':
            ts = torch.randint(0, self.timesteps, (B, ), device=self.device).long()
        elif self.rand_t_type == 'half':
            ts = torch.randint(0, self.timesteps, ((B + 1) // 2, ), device=self.device)
            if B % 2 == 1:
                ts = torch.cat([ts, self.timesteps - ts[:-1] - 1], dim=0).long()
            else:
                ts = torch.cat([ts, self.timesteps - ts - 1], dim=0).long()
        else:
            raise Exception('Unsupported rand ts type.')
        
        noise = torch.randn_like(data['norm_pose'], device=self.device)
        x_t = self.q_sample(x0=data['norm_pose'], t=ts, noise=noise)

        condition = self.eps_model.condition(data)
        data["cond"] = condition

        output = self.eps_model(x_t, ts, data)

        if self.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            B, *x_shape = x_t.shape
            pred_x0 = self.sqrt_recip_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                        self.sqrt_recipm1_alphas_cumprod[ts].reshape(B, *((1, ) * len(x_shape))) * output
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }
        outputs, targets = self.criterion.get_hand_model_pose(pred_dict, data)
        outputs, targets = self.criterion.get_hand(outputs, targets)

        return outputs, targets

    def _sample_timesteps(self, batch_size):
        if self.rand_t_type == 'all':
            return torch.randint(0, self.timesteps, (batch_size,), device=self.device).long()
        elif self.rand_t_type == 'half':
            ts = torch.randint(0, self.timesteps, ((batch_size + 1) // 2,), device=self.device)
            if batch_size % 2 == 1:
                return torch.cat([ts, self.timesteps - ts[:-1] - 1], dim=0).long()
            else:
                return torch.cat([ts, self.timesteps - ts - 1], dim=0).long()
        else:
            raise ValueError(f'Unsupported rand_t_type: {self.rand_t_type}. Expected "all" or "half".')

    def on_load_checkpoint(self, checkpoint: Dict[str, Any]) -> None:
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            model_state_dict = self.state_dict()
            checkpoint_state_dict = checkpoint['state_dict']
            
            new_state_dict = {}
            for key, value in checkpoint_state_dict.items():
                if 'score_heads' not in key:
                    new_state_dict[key] = value
                    
            model_state_dict.update(new_state_dict)
            self.load_state_dict(model_state_dict)
            logging.info("Loaded checkpoint weights (excluding score_heads)")
        else:
            current_state = self.state_dict()
            different = self._check_state_dict(checkpoint['state_dict'], current_state)
            
            if different:
                logging.warning("State dict inconsistency detected!")
                input("Press ENTER to continue or Ctrl-C to interrupt")
            else:
                logging.info("Checkpoint state dict is CONSISTENT with model state dict")
                
            logging.info("Loading full checkpoint")

    def _check_state_dict(self, dict1, dict2):
        if dict1.keys() != dict2.keys():
            logging.warning("Keys mismatch!")
            logging.warning(f"Only in dict1: {set(dict1.keys()) - set(dict2.keys())}")
            logging.warning(f"Only in dict2: {set(dict2.keys()) - set(dict1.keys())}")
            return True
        
        for key in dict1:
            if dict1[key].shape != dict2[key].shape:
                logging.warning(f"Shape mismatch for {key}!")
                logging.warning(f"Shape in dict1: {dict1[key].shape}")
                logging.warning(f"Shape in dict2: {dict2[key].shape}")
                return True
        
        return False

    def _convert_number_to_emoji(self, number_str: str) -> str:
        number_emoji_map = {
            '0': '0️⃣ ',
            '1': '1️⃣ ',
            '2': '2️⃣ ',
            '3': '3️⃣ ',
            '4': '4️⃣ ',
            '5': '5️⃣ ',
            '6': '6️⃣ ',
            '7': '7️⃣ ',
            '8': '8️⃣ ',
            '9': '9️⃣ ',
            '.': '🔸'
        }
        return ''.join(number_emoji_map.get(char, char) for char in number_str)