#!/usr/bin/env python3
"""
分析反归一化公式的错误
"""

import torch
import numpy as np

# 常量定义
NORM_UPPER = 1.0
NORM_LOWER = -1.0

def analyze_denormalization_error():
    """分析反归一化公式的错误"""
    print("=== 反归一化公式分析 ===")
    
    # 模拟统计数据
    data_min = -0.2
    data_max = 0.3
    data_range = data_max - data_min
    
    print(f"原始数据范围: [{data_min}, {data_max}]")
    print(f"归一化目标范围: [{NORM_LOWER}, {NORM_UPPER}]")
    print()
    
    # 测试一个归一化后的值
    normalized_value = 0.5  # 在[-1, 1]范围内的某个值
    
    print(f"测试归一化值: {normalized_value}")
    print()
    
    # 正确的反归一化公式
    print("正确的反归一化公式:")
    print("1. 从[-1,1]缩放回[0,1]: scaled_back = (norm_val - NORM_LOWER) / (NORM_UPPER - NORM_LOWER)")
    print("2. 从[0,1]缩放回原始范围: original = scaled_back * data_range + data_min")
    
    scaled_back_correct = (normalized_value - NORM_LOWER) / (NORM_UPPER - NORM_LOWER)
    original_correct = scaled_back_correct * data_range + data_min
    
    print(f"  步骤1: ({normalized_value} - ({NORM_LOWER})) / ({NORM_UPPER} - ({NORM_LOWER})) = {scaled_back_correct}")
    print(f"  步骤2: {scaled_back_correct} * {data_range} + {data_min} = {original_correct}")
    print()
    
    # 错误的反归一化公式（原始代码）
    print("错误的反归一化公式（原始代码）:")
    print("1. t = hand_t + ((NORM_UPPER - NORM_LOWER) / 2.0)")
    print("2. t /= (NORM_UPPER - NORM_LOWER)")
    print("3. t = t * t_range + t_min")
    
    step1_wrong = normalized_value + ((NORM_UPPER - NORM_LOWER) / 2.0)
    step2_wrong = step1_wrong / (NORM_UPPER - NORM_LOWER)
    step3_wrong = step2_wrong * data_range + data_min
    
    print(f"  步骤1: {normalized_value} + ({NORM_UPPER} - ({NORM_LOWER})) / 2 = {normalized_value} + 1.0 = {step1_wrong}")
    print(f"  步骤2: {step1_wrong} / ({NORM_UPPER} - ({NORM_LOWER})) = {step1_wrong} / 2 = {step2_wrong}")
    print(f"  步骤3: {step2_wrong} * {data_range} + {data_min} = {step3_wrong}")
    print()
    
    print(f"正确结果: {original_correct}")
    print(f"错误结果: {step3_wrong}")
    print(f"误差: {abs(original_correct - step3_wrong)}")
    print()

def demonstrate_round_trip_error():
    """演示往返转换的错误"""
    print("=== 往返转换错误演示 ===")
    
    # 模拟数据
    data_min = -0.1
    data_max = 0.2
    data_range = data_max - data_min
    
    original_values = [data_min, data_max, 0.0, 0.1]
    
    for original in original_values:
        print(f"原始值: {original}")
        
        # 正确的归一化
        scaled_0_1 = (original - data_min) / data_range
        normalized = scaled_0_1 * (NORM_UPPER - NORM_LOWER) + NORM_LOWER
        print(f"  归一化后: {normalized}")
        
        # 错误的反归一化
        step1 = normalized + ((NORM_UPPER - NORM_LOWER) / 2.0)
        step2 = step1 / (NORM_UPPER - NORM_LOWER)
        wrong_result = step2 * data_range + data_min
        print(f"  错误反归一化: {wrong_result}")
        
        # 正确的反归一化
        correct_scaled_back = (normalized - NORM_LOWER) / (NORM_UPPER - NORM_LOWER)
        correct_result = correct_scaled_back * data_range + data_min
        print(f"  正确反归一化: {correct_result}")
        
        print(f"  误差: {abs(original - wrong_result):.6f}")
        print()

def analyze_wrong_formula_step_by_step():
    """逐步分析错误公式"""
    print("=== 错误公式逐步分析 ===")
    
    print("错误的反归一化公式:")
    print("t = hand_t + ((NORM_UPPER - NORM_LOWER) / 2.0)")
    print("t /= (NORM_UPPER - NORM_LOWER)")
    print("t = t * t_range + t_min")
    print()
    
    print("让我们简化这个公式:")
    print("设 norm_val 为归一化后的值（在[-1,1]范围内）")
    print("设 range_size = NORM_UPPER - NORM_LOWER = 2")
    print()
    
    print("步骤1: t = norm_val + (range_size / 2) = norm_val + 1")
    print("步骤2: t = t / range_size = (norm_val + 1) / 2")
    print("步骤3: t = t * data_range + data_min")
    print()
    
    print("合并: result = ((norm_val + 1) / 2) * data_range + data_min")
    print()
    
    print("但是正确的公式应该是:")
    print("result = ((norm_val - NORM_LOWER) / (NORM_UPPER - NORM_LOWER)) * data_range + data_min")
    print("result = ((norm_val - (-1)) / 2) * data_range + data_min")
    print("result = ((norm_val + 1) / 2) * data_range + data_min")
    print()
    
    print("等等...这两个公式是一样的！")
    print("让我重新检查...")

def main():
    """主函数"""
    analyze_denormalization_error()
    demonstrate_round_trip_error()
    analyze_wrong_formula_step_by_step()
    
    print("我需要重新检查代码，可能我又理解错了...")

if __name__ == "__main__":
    main()
