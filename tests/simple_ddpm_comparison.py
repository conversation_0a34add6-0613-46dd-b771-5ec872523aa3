#!/usr/bin/env python3
"""
简化的 DDPM Lightning 模型对比测试

这个脚本直接使用硬编码的配置来避免配置文件的复杂性，
专注于验证重构后的模型与原始模型的功能一致性。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any
from omegaconf import OmegaConf
import warnings
warnings.filterwarnings("ignore")

# 导入模型
from models.diffuser_lightning import DDPMLightning as DDPMLightningRefactored
from models.diffuser_lightning_copy import DDPMLightning as DDPMLightningOriginal

# 导入工具函数
from utils.hand_helper import process_hand_pose, process_hand_pose_test


def create_simple_config():
    """创建简化的测试配置"""
    cfg = OmegaConf.create({
        # 解码器配置
        'decoder': {
            'name': 'unet',
            'rot_type': 'r6d',
            'd_model': 128,  # 更小的模型
            'time_embed_mult': 2,
            'nblocks': 1,  # 只用1个块
            'resblock_dropout': 0.0,
            'transformer_num_heads': 2,
            'transformer_dim_head': 32,
            'transformer_dropout': 0.1,
            'transformer_depth': 1,
            'transformer_mult_ff': 2,
            'context_dim': 128,
            'backbone': {
                'name': 'pointnet2',
                'use_pooling': False,
                'layer1': {
                    'npoint': 256,
                    'radius_list': [0.04],
                    'nsample_list': [16],
                    'mlp_list': [3, 16, 16, 32]
                },
                'layer2': {
                    'npoint': 128,
                    'radius_list': [0.1],
                    'nsample_list': [8],
                    'mlp_list': [32, 32, 32, 64]
                },
                'layer3': {
                    'npoint': 64,
                    'radius_list': [0.2],
                    'nsample_list': [8],
                    'mlp_list': [64, 32, 32, 64]
                },
                'layer4': {
                    'npoint': 32,
                    'radius_list': [0.3],
                    'nsample_list': [4],
                    'mlp_list': [64, 128, 128]
                },
                'use_xyz': True,
                'normalize_xyz': True
            },
            'use_position_embedding': False,
            'use_text_condition': False,
            'text_dropout_prob': 0.1
        },
        
        # 损失函数配置
        'criterion': {
            'mode': 'multi_grasp',
            'device': 'cuda:0',
            'rot_type': 'r6d',
            'hand_model': {
                'n_surface_points': 256,  # 更少的表面点
                'rot_type': 'r6d'
            },
            'loss_weights': {
                'translation': 10.0,
                'rotation': 10.0,
                'qpos': 1.0,
                'neg_loss': 0.0,  # 禁用负向损失
                'hand_chamfer': 0.0
            },
            'multi_grasp': {
                'loss_aggregation': 'mean',
                'use_consistency_loss': False,
                'consistency_loss_weight': 0.0,
                'diversity_loss_weight': 0.0
            },
            'cost_weights': {
                'translation': 2.0,
                'rotation': 2.0,
                'qpos': 1.0
            },
            'scale': 0.1,
            'q1': {
                'lambda_torque': 10,
                'm': 8,
                'mu': 1,
                'nms': True,
                'thres_contact': 0.01,
                'thres_pen': 0.005,
                'thres_tpen': 0.01,
                'rot_type': 'r6d'
            }
        },
        
        # 扩散模型配置
        'steps': 20,  # 很少的步数
        'schedule_cfg': {
            'beta': [0.0001, 0.02],
            'beta_schedule': 'linear',
            's': 0.008
        },
        
        # 其他配置
        'pred_x0': True,
        'use_cfg': False,
        'guidance_scale': 7.5,
        'use_negative_guidance': False,
        'negative_guidance_scale': 1.0,
        'rot_type': 'r6d',
        'loss_weights': {
            'translation': 10.0,
            'rotation': 10.0,
            'qpos': 1.0,
            'neg_loss': 0.0,
            'hand_chamfer': 0.0
        },
        'batch_size': 2,
        'print_freq': 1,
        'use_score': False,
        'score_pretrain': False,
        'mode': 'multi_grasp',
        'rand_t_type': 'all',
        'optimizer': {
            'name': 'adamw',
            'lr': 1e-4,
            'weight_decay': 1e-4
        },
        'scheduler': {
            'name': 'cosine',
            't_max': 100,
            'min_lr': 1e-6
        }
    })
    
    return cfg


def create_test_data(batch_size=2, num_grasps=2, device='cuda'):
    """创建简单的测试数据"""
    torch.manual_seed(42)
    np.random.seed(42)
    
    test_data = {
        # 场景点云 [B, N, 3]
        'scene_pc': torch.randn(batch_size, 256, 3, device=device),  # 更少的点
        
        # 多抓取手部姿态 [B, num_grasps, pose_dim]
        'hand_model_pose': torch.randn(batch_size, num_grasps, 25, device=device),
        
        # 场景条件 [B, condition_dim]
        'scene_cond': torch.randn(batch_size, 128, device=device),  # 更小的条件维度
        
        # 文本条件（设为None）
        'text_cond': None,
        
        # 其他元数据
        'scene_id': [f'scene_{i}' for i in range(batch_size)],
        'grasp_id': [[f'grasp_{i}_{j}' for j in range(num_grasps)] for i in range(batch_size)]
    }
    
    return test_data


def test_model_initialization():
    """测试模型初始化"""
    print("=" * 60)
    print("测试 1: 模型初始化对比")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    cfg = create_simple_config()
    
    try:
        # 设置相同的随机种子
        torch.manual_seed(42)
        model_refactored = DDPMLightningRefactored(cfg).to(device)
        
        torch.manual_seed(42)
        model_original = DDPMLightningOriginal(cfg).to(device)
        
        # 比较参数数量
        params_refactored = sum(p.numel() for p in model_refactored.parameters())
        params_original = sum(p.numel() for p in model_original.parameters())
        
        print(f"重构版本参数数量: {params_refactored:,}")
        print(f"原始版本参数数量: {params_original:,}")
        
        success = (params_refactored == params_original)
        print(f"参数数量匹配: {'✅' if success else '❌'}")
        
        return success, model_refactored, model_original
        
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        return False, None, None


def test_forward_pass(model_refactored, model_original, tolerance=1e-3):
    """测试前向传播一致性"""
    print("=" * 60)
    print("测试 2: 前向传播一致性")
    print("=" * 60)
    
    device = next(model_refactored.parameters()).device
    
    try:
        # 创建测试数据
        test_data = create_test_data(batch_size=1, num_grasps=2, device=device)
        
        # 预处理数据
        batch_refactored = process_hand_pose_test(test_data.copy(), 
                                                rot_type='r6d', 
                                                mode='multi_grasp')
        batch_original = process_hand_pose_test(test_data.copy(), 
                                               rot_type='r6d', 
                                               mode='multi_grasp')
        
        model_refactored.eval()
        model_original.eval()
        
        with torch.no_grad():
            # 设置相同的随机种子
            torch.manual_seed(123)
            
            # 重构版本前向传播
            output_refactored = model_refactored.forward(batch_refactored)
            
            # 重新设置随机种子
            torch.manual_seed(123)
            
            # 原始版本采样
            samples_original = model_original.sample(batch_original, k=1)
            output_original = samples_original[:, 0, -1]  # 取最后一个时间步
        
        # 比较输出
        pred_refactored = output_refactored["pred_pose_norm"]
        pred_original = output_original
        
        print(f"重构版本输出形状: {pred_refactored.shape}")
        print(f"原始版本输出形状: {pred_original.shape}")
        
        if pred_refactored.shape == pred_original.shape:
            diff = torch.abs(pred_refactored - pred_original)
            max_diff = torch.max(diff).item()
            mean_diff = torch.mean(diff).item()
            
            print(f"最大差异: {max_diff:.6f}")
            print(f"平均差异: {mean_diff:.6f}")
            print(f"容差: {tolerance}")
            
            success = (max_diff < tolerance)
            print(f"数值一致性: {'✅' if success else '❌'}")
            
            return success
        else:
            print("❌ 输出形状不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_sampling_consistency(model_refactored, model_original, tolerance=1e-3):
    """测试采样一致性"""
    print("=" * 60)
    print("测试 3: 采样一致性")
    print("=" * 60)
    
    device = next(model_refactored.parameters()).device
    
    try:
        # 创建测试数据
        test_data = create_test_data(batch_size=1, num_grasps=2, device=device)
        
        # 预处理数据
        batch_refactored = process_hand_pose_test(test_data.copy(), 
                                                rot_type='r6d', 
                                                mode='multi_grasp')
        batch_original = process_hand_pose_test(test_data.copy(), 
                                               rot_type='r6d', 
                                               mode='multi_grasp')
        
        model_refactored.eval()
        model_original.eval()
        
        with torch.no_grad():
            # 设置相同的随机种子
            torch.manual_seed(456)
            
            # 重构版本采样
            samples_refactored = model_refactored.diffusion_model.sample(batch_refactored, k=1)
            
            # 重新设置随机种子
            torch.manual_seed(456)
            
            # 原始版本采样
            samples_original = model_original.sample(batch_original, k=1)
        
        # 比较最终结果
        final_refactored = samples_refactored[:, 0, -1]
        final_original = samples_original[:, 0, -1]
        
        print(f"重构版本采样形状: {final_refactored.shape}")
        print(f"原始版本采样形状: {final_original.shape}")
        
        if final_refactored.shape == final_original.shape:
            diff = torch.abs(final_refactored - final_original)
            max_diff = torch.max(diff).item()
            mean_diff = torch.mean(diff).item()
            
            print(f"最大差异: {max_diff:.6f}")
            print(f"平均差异: {mean_diff:.6f}")
            print(f"容差: {tolerance}")
            
            success = (max_diff < tolerance)
            print(f"采样一致性: {'✅' if success else '❌'}")
            
            return success
        else:
            print("❌ 采样输出形状不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 采样测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 简化的 DDPM Lightning 模型对比测试")
    print("=" * 80)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    # 测试模型初始化
    init_success, model_refactored, model_original = test_model_initialization()
    
    if not init_success:
        print("❌ 模型初始化失败，终止测试")
        return 1
    
    # 测试前向传播
    forward_success = test_forward_pass(model_refactored, model_original, tolerance=1e-2)
    
    # 测试采样一致性
    sampling_success = test_sampling_consistency(model_refactored, model_original, tolerance=1e-2)
    
    # 总结结果
    print("=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    tests = [
        ("模型初始化", init_success),
        ("前向传播一致性", forward_success),
        ("采样一致性", sampling_success)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20s}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！重构版本与原始版本基本一致")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
