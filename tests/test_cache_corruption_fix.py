#!/usr/bin/env python3
"""
测试缓存损坏问题的修复脚本

这个脚本用于：
1. 检查缓存文件的状态
2. 清理损坏的缓存文件
3. 验证缓存系统的正确性
"""

import os
import h5py
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datasets.utils.cache_utils import validate_cache_file

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

def check_cache_file_status(cache_path: str, expected_items: int = None):
    """检查缓存文件的状态"""
    logger.info(f"检查缓存文件: {cache_path}")
    
    if not os.path.exists(cache_path):
        logger.info("缓存文件不存在")
        return False, 0
    
    try:
        with h5py.File(cache_path, 'r') as hf:
            actual_items = len(hf)
            logger.info(f"缓存文件存在，包含 {actual_items} 个项目")
            
            if expected_items is not None:
                if actual_items == expected_items:
                    logger.info(f"缓存文件有效：项目数量匹配 ({actual_items}/{expected_items})")
                    return True, actual_items
                else:
                    logger.warning(f"缓存文件无效：项目数量不匹配 ({actual_items}/{expected_items})")
                    return False, actual_items
            else:
                return actual_items > 0, actual_items
                
    except Exception as e:
        logger.error(f"检查缓存文件时出错: {e}")
        return False, 0

def clean_corrupted_cache(cache_path: str):
    """清理损坏的缓存文件"""
    if os.path.exists(cache_path):
        try:
            os.remove(cache_path)
            logger.info(f"已删除损坏的缓存文件: {cache_path}")
            return True
        except Exception as e:
            logger.error(f"删除缓存文件失败: {e}")
            return False
    else:
        logger.info("缓存文件不存在，无需删除")
        return True

def check_cache_directory_permissions(cache_dir: str):
    """检查缓存目录的权限"""
    logger.info(f"检查缓存目录权限: {cache_dir}")
    
    if not os.path.exists(cache_dir):
        logger.info("缓存目录不存在，尝试创建...")
        try:
            os.makedirs(cache_dir, exist_ok=True)
            logger.info("缓存目录创建成功")
        except Exception as e:
            logger.error(f"创建缓存目录失败: {e}")
            return False
    
    # 检查读写权限
    if os.access(cache_dir, os.R_OK | os.W_OK):
        logger.info("缓存目录权限正常")
        return True
    else:
        logger.error("缓存目录权限不足")
        return False

def main():
    """主函数"""
    logger.info("开始缓存损坏问题诊断...")
    
    # 从日志中提取的缓存文件路径
    cache_paths = [
        "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed/cache_sceneleappro/sceneleappro_ff457216b59f323b0a08248a2f38f7e0_camera_centric_scene_mean_normalized_160.h5",
        "/home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15/cache_sceneleappro/sceneleappro_test_360a1a3a09d0fe7fab114e8480b7a1b5_camera_centric_scene_mean_normalized_512.h5"
    ]
    
    expected_items = [35365, 5310]  # 从日志中提取的期望项目数
    
    for i, cache_path in enumerate(cache_paths):
        logger.info(f"\n{'='*60}")
        logger.info(f"检查缓存文件 {i+1}: {os.path.basename(cache_path)}")
        logger.info(f"{'='*60}")
        
        # 检查缓存目录权限
        cache_dir = os.path.dirname(cache_path)
        if not check_cache_directory_permissions(cache_dir):
            continue
        
        # 检查缓存文件状态
        is_valid, actual_items = check_cache_file_status(cache_path, expected_items[i])
        
        if not is_valid:
            logger.warning(f"发现损坏的缓存文件: {cache_path}")
            logger.info(f"期望项目数: {expected_items[i]}, 实际项目数: {actual_items}")
            
            # 询问是否清理
            response = input(f"是否删除损坏的缓存文件? (y/n): ").lower().strip()
            if response == 'y':
                if clean_corrupted_cache(cache_path):
                    logger.info("缓存文件已清理，下次训练时将重新创建")
                else:
                    logger.error("清理缓存文件失败")
            else:
                logger.info("跳过清理缓存文件")
        else:
            logger.info("缓存文件状态正常")
    
    logger.info(f"\n{'='*60}")
    logger.info("诊断完成")
    logger.info(f"{'='*60}")
    
    # 提供解决方案建议
    print("\n解决方案建议:")
    print("1. 如果缓存文件已被清理，下次训练时会自动重新创建")
    print("2. 确保训练过程不被中断，让缓存完全创建完成")
    print("3. 如果问题持续存在，可以考虑:")
    print("   - 检查磁盘空间是否充足")
    print("   - 检查文件系统权限")
    print("   - 使用更小的数据集进行测试")

if __name__ == "__main__":
    main()
