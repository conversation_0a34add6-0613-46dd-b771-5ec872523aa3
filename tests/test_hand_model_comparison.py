#!/usr/bin/env python3
"""
测试脚本：比较三个不同hand_model文件生成的手部mesh
使用4*23的手部姿态参数，分别测试：
- utils/hand_model.py
- utils/hand_model_origin.py  
- bin/hand_model.py

设置参数：HandModelType = HandModelType.LEAP, rot_type="quat"
"""

import os
import sys
import torch
import trimesh as tm
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from utils.hand_types import HandModelType

def create_sample_hand_pose():
    """
    创建4*23的示例手部姿态数据
    使用现成的手部姿态参数
    """
    # 使用预定义的手部姿态数据
    hand_pose = torch.tensor([
        [-1.1026e-01, -1.3769e-01,  8.0413e-01,  8.8674e-01,  3.8066e-02,
          7.0243e-01,  1.3439e-01,  5.8323e-01, -6.1394e-02,  5.7122e-01,
          4.3707e-02,  1.0560e+00, -1.8491e-01,  7.2484e-01,  1.2363e-01,
          1.8445e+00, -9.1464e-03,  2.7950e-01, -1.9807e-01,  1.6705e-01,
          9.0394e-01, -3.6478e-01, -1.4804e-01],
        [-1.1026e-01, -1.3769e-01,  8.0413e-01,  8.8674e-01,  3.8066e-02,
          7.0243e-01,  1.3439e-01,  5.8323e-01, -6.1394e-02,  5.7122e-01,
          4.3707e-02,  1.0560e+00, -1.8491e-01,  7.2484e-01,  1.2363e-01,
          1.8445e+00, -9.1464e-03,  2.7950e-01, -1.9807e-01,  1.6705e-01,
          9.0394e-01, -3.6478e-01, -1.4804e-01],
        [-1.1026e-01, -1.3769e-01,  8.0413e-01,  8.8674e-01,  3.8066e-02,
          7.0243e-01,  1.3439e-01,  5.8323e-01, -6.1394e-02,  5.7122e-01,
          4.3707e-02,  1.0560e+00, -1.8491e-01,  7.2484e-01,  1.2363e-01,
          1.8445e+00, -9.1464e-03,  2.7950e-01, -1.9807e-01,  1.6705e-01,
          9.0394e-01, -3.6478e-01, -1.4804e-01],
        [-2.5141e-02, -1.8350e-01,  7.9878e-01,  6.7809e-01,  1.0822e-01,
          6.3650e-01,  1.2085e-01,  8.8029e-01, -4.9106e-02,  7.4475e-01,
          1.9364e-01,  9.2978e-01, -1.2784e-01,  7.5892e-01,  1.0337e-01,
          1.8497e+00,  4.1726e-02, -3.4217e-04, -1.7800e-01,  2.8632e-01,
          9.3682e-01,  1.7805e-01, -9.3251e-02]
    ])
    
    return hand_pose

def test_hand_model_utils():
    """测试 utils/hand_model.py"""
    print("测试 utils/hand_model.py...")
    
    try:
        from utils.hand_model import HandModel
        from utils.hand_types import HandModelType
        
        # 创建手部模型
        hand_model = HandModel(
            hand_model_type=HandModelType.LEAP,
            rot_type="quat",
            device="cpu"
        )
        
        # 创建示例手部姿态
        hand_pose = create_sample_hand_pose()  # [4, 23]
        print(f"手部姿态形状: {hand_pose.shape}")
        
        # 设置参数
        hand_model.set_parameters(hand_pose)
        
        # 获取mesh数据
        result = hand_model(hand_pose, with_meshes=True)
        vertices = result["vertices"]  # [B*num_grasps, N_vertices, 3]
        faces = result["faces"]        # [N_faces, 3]
        
        print(f"顶点形状: {vertices.shape}")
        print(f"面片形状: {faces.shape}")
        
        # 保存mesh文件
        output_dir = Path("tests/mesh_comparison")
        output_dir.mkdir(exist_ok=True)
        
        meshes = []
        for i in range(vertices.shape[0]):
            mesh = tm.Trimesh(
                vertices=vertices[i].detach().cpu().numpy(),
                faces=faces.detach().cpu().numpy()
            )
            mesh_path = output_dir / f"utils_hand_model_pose_{i}.obj"
            mesh.export(str(mesh_path))
            meshes.append(mesh)
            print(f"保存mesh到: {mesh_path}")
        
        return meshes, "utils/hand_model.py"
        
    except Exception as e:
        print(f"测试 utils/hand_model.py 失败: {e}")
        import traceback
        traceback.print_exc()
        return None, "utils/hand_model.py"

def test_hand_model_origin():
    """测试 utils/hand_model_origin.py"""
    print("\n测试 utils/hand_model_origin.py...")
    
    try:
        from utils.hand_model_origin import HandModel, HandModelType
        
        # 创建手部模型
        hand_model = HandModel(
            hand_model_type=HandModelType.LEAP,
            device="cpu"
        )
        
        # 创建示例手部姿态
        hand_pose = create_sample_hand_pose()  # [4, 23]
        print(f"手部姿态形状: {hand_pose.shape}")
        
        # 设置参数 - 使用set_parameters_quat方法
        hand_model.set_parameters_quat(hand_pose)
        
        # 获取mesh数据
        output_dir = Path("tests/mesh_comparison")
        output_dir.mkdir(exist_ok=True)
        
        meshes = []
        for i in range(hand_pose.shape[0]):
            mesh = hand_model.get_trimesh_data(i)
            mesh_path = output_dir / f"utils_hand_model_origin_pose_{i}.obj"
            mesh.export(str(mesh_path))
            meshes.append(mesh)
            print(f"保存mesh到: {mesh_path}")
        
        return meshes, "utils/hand_model_origin.py"
        
    except Exception as e:
        print(f"测试 utils/hand_model_origin.py 失败: {e}")
        import traceback
        traceback.print_exc()
        return None, "utils/hand_model_origin.py"

def test_hand_model_bin():
    """测试 bin/hand_model.py"""
    print("\n测试 bin/hand_model.py...")
    
    try:
        from bin.hand_model import HandModel, HandModelType
        
        # 创建手部模型
        hand_model = HandModel(
            hand_model_type=HandModelType.LEAP,
            rot_type="quat",
            device="cpu"
        )
        
        # 创建示例手部姿态
        hand_pose = create_sample_hand_pose()  # [4, 23]
        print(f"手部姿态形状: {hand_pose.shape}")
        
        # 设置参数
        hand_model.set_parameters(hand_pose)
        
        # 获取mesh数据
        result = hand_model(hand_pose, with_meshes=True)
        vertices = result["vertices"]  # [B*num_grasps, N_vertices, 3]
        faces = result["faces"]        # [N_faces, 3]
        
        print(f"顶点形状: {vertices.shape}")
        print(f"面片形状: {faces.shape}")
        
        # 保存mesh文件
        output_dir = Path("tests/mesh_comparison")
        output_dir.mkdir(exist_ok=True)
        
        meshes = []
        for i in range(vertices.shape[0]):
            mesh = tm.Trimesh(
                vertices=vertices[i].detach().cpu().numpy(),
                faces=faces.detach().cpu().numpy()
            )
            mesh_path = output_dir / f"bin_hand_model_pose_{i}.obj"
            mesh.export(str(mesh_path))
            meshes.append(mesh)
            print(f"保存mesh到: {mesh_path}")
        
        return meshes, "bin/hand_model.py"
        
    except Exception as e:
        print(f"测试 bin/hand_model.py 失败: {e}")
        import traceback
        traceback.print_exc()
        return None, "bin/hand_model.py"

def compare_meshes(meshes_list):
    """比较不同模型生成的mesh是否一致"""
    print("\n=== Mesh比较结果 ===")
    
    if len(meshes_list) < 2:
        print("需要至少两个模型的结果才能进行比较")
        return
    
    # 比较每个姿态的mesh
    for pose_idx in range(4):
        print(f"\n姿态 {pose_idx} 的比较:")
        
        valid_meshes = []
        valid_names = []
        
        for meshes, name in meshes_list:
            if meshes is not None and len(meshes) > pose_idx:
                valid_meshes.append(meshes[pose_idx])
                valid_names.append(name)
        
        if len(valid_meshes) < 2:
            print(f"  姿态 {pose_idx}: 没有足够的有效mesh进行比较")
            continue
        
        # 比较顶点数和面片数
        for i in range(len(valid_meshes)):
            mesh = valid_meshes[i]
            name = valid_names[i]
            print(f"  {name}: {len(mesh.vertices)} 顶点, {len(mesh.faces)} 面片")
        
        # 比较顶点坐标（如果顶点数相同）
        if len(set(len(mesh.vertices) for mesh in valid_meshes)) == 1:
            # 所有mesh的顶点数相同
            vertices_arrays = [mesh.vertices for mesh in valid_meshes]
            
            # 计算顶点坐标的差异
            max_diff = 0
            for i in range(1, len(vertices_arrays)):
                diff = np.max(np.abs(vertices_arrays[0] - vertices_arrays[i]))
                max_diff = max(max_diff, diff)
                print(f"  {valid_names[0]} vs {valid_names[i]}: 最大顶点差异 = {diff:.6f}")
            
            if max_diff < 1e-5:
                print(f"  ✅ 姿态 {pose_idx}: 所有mesh基本一致 (差异 < 1e-5)")
            else:
                print(f"  ❌ 姿态 {pose_idx}: mesh存在显著差异 (最大差异 = {max_diff:.6f})")
        else:
            print(f"  ❌ 姿态 {pose_idx}: mesh的顶点数不同，无法直接比较坐标")

def main():
    """主函数"""
    print("开始测试三个不同的hand_model文件...")
    print("=" * 60)
    
    # 测试三个模型
    results = []
    
    # 测试 utils/hand_model.py
    meshes1, name1 = test_hand_model_utils()
    results.append((meshes1, name1))
    
    # 测试 utils/hand_model_origin.py
    meshes2, name2 = test_hand_model_origin()
    results.append((meshes2, name2))
    
    # 测试 bin/hand_model.py
    meshes3, name3 = test_hand_model_bin()
    results.append((meshes3, name3))
    
    # 比较结果
    compare_meshes(results)
    
    print("\n" + "=" * 60)
    print("测试完成！所有mesh文件已保存到 tests/mesh_comparison/ 目录")

if __name__ == "__main__":
    main()
