#!/usr/bin/env python3
"""
测试统一的多抓取格式实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel
from utils.hand_types import HandModelType

# 检查CUDA是否可用
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

def test_unified_format_conversion():
    """测试输入格式的统一转换"""
    print("Testing unified format conversion...")
    
    hand_model = HandModel(device=DEVICE)
    pose_dim = 3 + hand_model.n_dofs + 4  # translation + joints + quaternion
    
    # 测试1: 1D输入 -> [1, 1, pose_dim]
    pose_1d = torch.randn(pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_1d)
    assert hand_model.batch_size_original == 1
    assert hand_model.num_grasps == 1
    assert hand_model.global_translation.shape == (1, 1, 3)
    assert hand_model.global_rotation.shape == (1, 1, 3, 3)
    
    # 测试2: 2D输入 -> [B, 1, pose_dim]
    batch_size = 3
    pose_2d = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_2d)
    assert hand_model.batch_size_original == batch_size
    assert hand_model.num_grasps == 1
    assert hand_model.global_translation.shape == (batch_size, 1, 3)
    assert hand_model.global_rotation.shape == (batch_size, 1, 3, 3)
    
    # 测试3: 3D输入 -> [B, num_grasps, pose_dim]
    num_grasps = 4
    pose_3d = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_3d)
    assert hand_model.batch_size_original == batch_size
    assert hand_model.num_grasps == num_grasps
    assert hand_model.global_translation.shape == (batch_size, num_grasps, 3)
    assert hand_model.global_rotation.shape == (batch_size, num_grasps, 3, 3)
    
    print("✓ Unified format conversion test passed")

def test_batch_size_consistency():
    """测试批次大小的一致性"""
    print("Testing batch size consistency...")
    
    hand_model = HandModel(device=DEVICE)
    pose_dim = 3 + hand_model.n_dofs + 4
    
    # 单抓取情况
    batch_size = 2
    pose_2d = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_2d)
    
    # batch_size 应该返回展平后的大小
    assert hand_model.batch_size == batch_size * 1  # B * num_grasps
    assert hand_model.hand_pose.shape[0] == batch_size * 1
    
    # 多抓取情况
    num_grasps = 3
    pose_3d = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_3d)
    
    # batch_size 应该返回展平后的大小
    assert hand_model.batch_size == batch_size * num_grasps
    assert hand_model.hand_pose.shape[0] == batch_size * num_grasps
    
    print("✓ Batch size consistency test passed")

def test_single_as_multi_grasp():
    """测试单抓取作为多抓取的特例"""
    print("Testing single grasp as special case of multi-grasp...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=50)
    pose_dim = 3 + hand_model.n_dofs + 4
    batch_size = 2
    
    # 创建相同的姿态数据
    torch.manual_seed(42)
    pose_data = torch.randn(batch_size, pose_dim, device=DEVICE)
    
    # 方式1: 作为单抓取输入 [B, pose_dim]
    torch.manual_seed(42)
    pose_single = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_single)
    
    surface_points_single = hand_model.get_surface_points()
    contact_candidates_single = hand_model.get_contact_candidates()
    
    # 方式2: 作为多抓取输入 [B, 1, pose_dim]
    torch.manual_seed(42)
    pose_multi = torch.randn(batch_size, pose_dim, device=DEVICE).unsqueeze(1)  # [B, 1, pose_dim]
    hand_model.set_parameters(pose_multi)
    
    surface_points_multi = hand_model.get_surface_points()
    contact_candidates_multi = hand_model.get_contact_candidates()
    
    # 结果应该完全一致
    assert torch.allclose(surface_points_single, surface_points_multi, atol=1e-6), "Surface points mismatch"
    assert torch.allclose(contact_candidates_single, contact_candidates_multi, atol=1e-6), "Contact candidates mismatch"
    
    print("✓ Single as multi-grasp test passed")

def test_multi_grasp_functionality():
    """测试真正的多抓取功能"""
    print("Testing true multi-grasp functionality...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=30)
    pose_dim = 3 + hand_model.n_dofs + 4
    batch_size = 2
    num_grasps = 3
    
    # 创建多抓取数据
    pose_multi = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    hand_model.set_parameters(pose_multi)
    
    # 测试各种方法的输出形状
    surface_points = hand_model.get_surface_points()
    contact_candidates = hand_model.get_contact_candidates()
    penetration_keypoints = hand_model.get_penetration_keypoints()
    
    expected_batch_size = batch_size * num_grasps
    
    assert surface_points.shape[0] == expected_batch_size, f"Surface points batch size: {surface_points.shape[0]} != {expected_batch_size}"
    assert contact_candidates.shape[0] == expected_batch_size, f"Contact candidates batch size: {contact_candidates.shape[0]} != {expected_batch_size}"
    assert penetration_keypoints.shape[0] == expected_batch_size, f"Penetration keypoints batch size: {penetration_keypoints.shape[0]} != {expected_batch_size}"
    
    # 测试物理计算
    test_points = torch.randn(expected_batch_size, 50, 3, device=DEVICE)
    distances = hand_model.cal_distance(test_points)
    assert distances.shape == (expected_batch_size, 50), f"Distance shape: {distances.shape}"
    
    spen_energy = hand_model.cal_self_penetration_energy()
    assert spen_energy.shape == (expected_batch_size,), f"Self-penetration energy shape: {spen_energy.shape}"
    
    print("✓ Multi-grasp functionality test passed")

def test_call_method_unified():
    """测试__call__方法的统一处理"""
    print("Testing __call__ method with unified format...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=20)
    pose_dim = 3 + hand_model.n_dofs + 4
    batch_size = 2
    num_grasps = 2
    
    # 测试多抓取格式
    pose_multi = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    scene_pc = torch.randn(batch_size, 100, 4, device=DEVICE)
    
    result = hand_model(
        pose_multi,
        scene_pc=scene_pc,
        with_surface_points=True,
        with_penetration_keypoints=True,
        with_meshes=True,
        with_fingertip_keypoints=True
    )
    
    expected_batch_size = batch_size * num_grasps
    
    # 检查所有输出的批次维度
    assert 'surface_points' in result
    assert 'penetration_keypoints' in result
    assert 'vertices' in result
    assert 'faces' in result
    assert 'fingertip_keypoints' in result
    
    assert result['surface_points'].shape[0] == expected_batch_size
    assert result['penetration_keypoints'].shape[0] == expected_batch_size
    assert result['vertices'].shape[0] == expected_batch_size
    assert result['fingertip_keypoints'].shape[0] == expected_batch_size
    
    print("✓ __call__ method unified test passed")

def test_backward_compatibility():
    """测试向后兼容性"""
    print("Testing backward compatibility...")
    
    hand_model = HandModel(device=DEVICE)
    pose_dim = 3 + hand_model.n_dofs + 4
    
    # 旧的调用方式应该仍然工作
    batch_size = 3
    old_style_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    
    # 这些调用应该不会出错
    hand_model.set_parameters(old_style_pose)
    
    # 检查内部状态
    assert hand_model.batch_size_original == batch_size
    assert hand_model.num_grasps == 1
    assert hand_model.batch_size == batch_size  # 展平后的大小
    
    # 各种方法应该正常工作
    surface_points = hand_model.get_surface_points()
    contact_candidates = hand_model.get_contact_candidates()
    
    assert surface_points.shape[0] == batch_size
    assert contact_candidates.shape[0] == batch_size
    
    print("✓ Backward compatibility test passed")

def main():
    """运行所有统一多抓取格式测试"""
    print("Starting unified multi-grasp format tests...\n")
    
    try:
        test_unified_format_conversion()
        test_batch_size_consistency()
        test_single_as_multi_grasp()
        test_multi_grasp_functionality()
        test_call_method_unified()
        test_backward_compatibility()
        
        print("\n🎉 All unified multi-grasp tests passed!")
        print("✅ Single grasp is now treated as num_grasps=1 special case")
        print("✅ All inputs are normalized to [B, num_grasps, pose_dim] format")
        print("✅ Internal processing uses unified multi-grasp logic")
        print("✅ Backward compatibility is maintained")
        
    except Exception as e:
        print(f"\n❌ Unified multi-grasp test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
