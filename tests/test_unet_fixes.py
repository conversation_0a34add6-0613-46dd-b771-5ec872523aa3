#!/usr/bin/env python3
"""
测试UNet修复后的功能
"""
import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.decoder.unet import UNetModel
from models.utils.diffusion_utils import GraspNet, CrossAttentionFusion

class MockConfig:
    """模拟配置类"""
    def __init__(self):
        self.rot_type = 'quat'
        self.d_model = 512
        self.nblocks = 4
        self.resblock_dropout = 0.1
        self.use_position_embedding = True
        self.transformer_num_heads = 8
        self.transformer_dim_head = 64
        self.transformer_dropout = 0.1
        self.transformer_depth = 6
        self.transformer_mult_ff = 4
        self.context_dim = 512
        self.time_embed_mult = 2
        self.use_text_condition = True
        self.text_dropout_prob = 0.1
        
        # Mock backbone config
        self.backbone = MockBackboneConfig()

class MockBackboneConfig:
    def __init__(self):
        self.type = 'pointnet'

class MockBackbone(torch.nn.Module):
    def __init__(self, cfg):
        super().__init__()
        self.conv = torch.nn.Conv1d(3, 512, 1)
    
    def forward(self, x):
        # x: (B, N, 3) -> (B, 3, N) -> (B, 512, N)
        x = x.transpose(1, 2)
        feat = self.conv(x)
        return None, feat

def test_grasp_net():
    """测试GraspNet的单抓取和多抓取处理"""
    print("测试GraspNet...")
    
    grasp_net = GraspNet(input_dim=23, output_dim=512)
    
    # 测试单抓取格式
    single_grasp = torch.randn(4, 23)  # (B, input_dim)
    single_output = grasp_net(single_grasp)
    print(f"单抓取输入: {single_grasp.shape} -> 输出: {single_output.shape}")
    assert single_output.shape == (4, 512), f"期望 (4, 512), 得到 {single_output.shape}"
    
    # 测试多抓取格式
    multi_grasp = torch.randn(4, 8, 23)  # (B, num_grasps, input_dim)
    multi_output = grasp_net(multi_grasp)
    print(f"多抓取输入: {multi_grasp.shape} -> 输出: {multi_output.shape}")
    assert multi_output.shape == (4, 8, 512), f"期望 (4, 8, 512), 得到 {multi_output.shape}"
    
    print("✅ GraspNet测试通过")

def test_cross_attention_fusion():
    """测试CrossAttentionFusion的单抓取和多抓取处理"""
    print("测试CrossAttentionFusion...")
    
    fusion = CrossAttentionFusion(d_model=512, n_heads=8)
    scene_features = torch.randn(4, 1024, 512)  # (B, N_points, 512)
    
    # 测试单抓取格式
    single_grasp_text = torch.randn(4, 512)  # (B, 512)
    single_output = fusion(single_grasp_text, scene_features)
    print(f"单抓取输入: {single_grasp_text.shape} -> 输出: {single_output.shape}")
    assert single_output.shape == (4, 512), f"期望 (4, 512), 得到 {single_output.shape}"
    
    # 测试多抓取格式
    multi_grasp_text = torch.randn(4, 8, 512)  # (B, num_grasps, 512)
    multi_output = fusion(multi_grasp_text, scene_features)
    print(f"多抓取输入: {multi_grasp_text.shape} -> 输出: {multi_output.shape}")
    assert multi_output.shape == (4, 8, 512), f"期望 (4, 8, 512), 得到 {multi_output.shape}"
    
    print("✅ CrossAttentionFusion测试通过")

def test_unet_position_embedding():
    """测试UNet位置嵌入修复"""
    print("测试UNet位置嵌入...")

    # Mock build_backbone function
    import models.backbone
    original_build_backbone = models.backbone.build_backbone
    models.backbone.build_backbone = lambda cfg: MockBackbone(cfg)

    try:
        cfg = MockConfig()
        model = UNetModel(cfg)

        # 测试单抓取格式
        x_t_single = torch.randn(2, 23)  # (B, pose_dim)
        ts = torch.randint(0, 1000, (2,))  # (B,)

        # 准备数据
        scene_pc = torch.randn(2, 1024, 3)  # (B, N_points, 3)
        data = {'scene_pc': scene_pc}

        # 获取条件
        cond_data = model.condition(data)

        # 前向传播
        output_single = model(x_t_single, ts, cond_data)
        print(f"单抓取输入: {x_t_single.shape} -> 输出: {output_single.shape}")
        assert output_single.shape == (2, 23), f"期望 (2, 23), 得到 {output_single.shape}"

        # 测试多抓取格式
        x_t_multi = torch.randn(2, 8, 23)  # (B, num_grasps, pose_dim)
        output_multi = model(x_t_multi, ts, cond_data)
        print(f"多抓取输入: {x_t_multi.shape} -> 输出: {output_multi.shape}")
        assert output_multi.shape == (2, 8, 23), f"期望 (2, 8, 23), 得到 {output_multi.shape}"

        print("✅ UNet位置嵌入测试通过")

    finally:
        # 恢复原始函数
        models.backbone.build_backbone = original_build_backbone

def test_config_defaults():
    """测试配置默认值"""
    print("测试配置默认值...")

    # Mock build_backbone function
    import models.backbone
    original_build_backbone = models.backbone.build_backbone
    models.backbone.build_backbone = lambda cfg: MockBackbone(cfg)

    try:
        # 测试缺少time_embed_mult的配置
        class IncompleteConfig(MockConfig):
            def __init__(self):
                super().__init__()
                delattr(self, 'time_embed_mult')  # 移除这个属性

        cfg = IncompleteConfig()
        model = UNetModel(cfg)  # 应该使用默认值4

        # 检查time_embed层的维度
        expected_time_embed_dim = cfg.d_model * 4  # 默认值
        actual_time_embed_dim = model.time_embed[0].out_features
        assert actual_time_embed_dim == expected_time_embed_dim, \
            f"期望time_embed_dim={expected_time_embed_dim}, 得到{actual_time_embed_dim}"

        print("✅ 配置默认值测试通过")

    finally:
        models.backbone.build_backbone = original_build_backbone

def test_group_norm_fix():
    """测试GroupNorm参数修复"""
    print("测试GroupNorm参数修复...")

    # Mock build_backbone function
    import models.backbone
    original_build_backbone = models.backbone.build_backbone
    models.backbone.build_backbone = lambda cfg: MockBackbone(cfg)

    try:
        # 测试小的d_model值
        class SmallModelConfig(MockConfig):
            def __init__(self):
                super().__init__()
                self.d_model = 16  # 小于32的值

        cfg = SmallModelConfig()
        model = UNetModel(cfg)  # 应该能正常创建

        # 检查GroupNorm的num_groups
        group_norm = model.out_layers[0]
        expected_num_groups = min(32, cfg.d_model)  # 应该是16
        actual_num_groups = group_norm.num_groups
        assert actual_num_groups == expected_num_groups, \
            f"期望num_groups={expected_num_groups}, 得到{actual_num_groups}"

        print("✅ GroupNorm参数修复测试通过")

    finally:
        models.backbone.build_backbone = original_build_backbone

if __name__ == "__main__":
    print("开始测试UNet修复...")

    test_grasp_net()
    test_cross_attention_fusion()
    test_unet_position_embedding()
    test_config_defaults()
    test_group_norm_fix()

    print("\n🎉 所有测试通过！UNet修复成功。")
