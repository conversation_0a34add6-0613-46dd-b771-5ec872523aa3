"""
测试重构后的扩散模型
验证重构前后的功能一致性
"""

import torch
import pytest
from unittest.mock import Mock, MagicMock
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.diffusion import DDPMModel
from models.lightning import DDPMLightning
from models.diffuser_lightning import DDPMLightning as CompatDDPMLightning


class TestDiffuserRefactoring:
    """测试扩散模型重构"""
    
    def setup_method(self):
        """设置测试环境"""
        # 创建模拟的配置
        self.cfg = Mock()
        self.cfg.decoder = Mock()
        self.cfg.criterion = Mock()
        self.cfg.criterion.loss_weights = {'total_loss': 1.0}
        self.cfg.steps = 100
        self.cfg.schedule_cfg = {
            'beta': [0.0001, 0.02],
            'beta_schedule': 'linear'
        }
        self.cfg.pred_x0 = False
        self.cfg.rot_type = 'quat'
        self.cfg.batch_size = 4
        self.cfg.print_freq = 10
        self.cfg.mode = 'train'
        self.cfg.rand_t_type = 'all'
        self.cfg.optimizer = Mock()
        self.cfg.optimizer.name = 'adam'
        self.cfg.optimizer.lr = 1e-4
        self.cfg.optimizer.weight_decay = 1e-5
        self.cfg.scheduler = Mock()
        self.cfg.scheduler.name = 'cosine'
        self.cfg.scheduler.T_max = 100
        self.cfg.scheduler.eta_min = 1e-6
        
        # 创建模拟的网络
        self.mock_eps_model = Mock()
        self.mock_eps_model.return_value = torch.randn(4, 25)  # [B, pose_dim]
        self.mock_eps_model.condition.return_value = {}
        
        # 创建模拟的损失函数
        self.mock_criterion = Mock()
        self.mock_criterion.return_value = {'total_loss': torch.tensor(1.0)}
    
    def test_ddpm_model_creation(self):
        """测试 DDPMModel 创建"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=100,
            schedule_cfg=self.cfg.schedule_cfg,
            pred_x0=False
        )
        
        assert model.timesteps == 100
        assert model.pred_x0 == False
        assert hasattr(model, 'sqrt_alphas_cumprod')
        assert hasattr(model, 'sqrt_one_minus_alphas_cumprod')
    
    def test_ddpm_model_q_sample(self):
        """测试前向扩散过程"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=100,
            schedule_cfg=self.cfg.schedule_cfg
        )
        
        # 测试单抓取格式
        x0 = torch.randn(4, 25)  # [B, pose_dim]
        t = torch.randint(0, 100, (4,))
        noise = torch.randn_like(x0)
        
        x_t = model.q_sample(x0, t, noise)
        assert x_t.shape == x0.shape
        
        # 测试多抓取格式
        x0_multi = torch.randn(4, 8, 25)  # [B, num_grasps, pose_dim]
        noise_multi = torch.randn_like(x0_multi)
        
        x_t_multi = model.q_sample(x0_multi, t, noise_multi)
        assert x_t_multi.shape == x0_multi.shape
    
    def test_ddpm_model_predict(self):
        """测试模型预测"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=100,
            schedule_cfg=self.cfg.schedule_cfg
        )
        
        # 测试单抓取格式
        x_t = torch.randn(4, 25)
        t = torch.randint(0, 100, (4,))
        data = {}
        
        pred_noise, pred_x0 = model.model_predict(x_t, t, data)
        assert pred_noise.shape == x_t.shape
        assert pred_x0.shape == x_t.shape
    
    def test_lightning_model_creation(self):
        """测试 Lightning 模型创建"""
        # 模拟 build_decoder 和 GraspLossPose
        with pytest.MonkeyPatch().context() as m:
            m.setattr('models.decoder.build_decoder', lambda x: self.mock_eps_model)
            m.setattr('models.loss.GraspLossPose', lambda x: self.mock_criterion)
            
            try:
                model = DDPMLightning(self.cfg)
                assert hasattr(model, 'diffusion_model')
                assert isinstance(model.diffusion_model, DDPMModel)
            except ImportError:
                # 如果导入失败，跳过这个测试
                pytest.skip("Required modules not available")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试兼容类是否正确继承
        assert issubclass(CompatDDPMLightning, DDPMLightning)
        
        # 测试类的文档字符串
        assert "向后兼容" in CompatDDPMLightning.__doc__
    
    def test_ddpm_model_sampling_interface(self):
        """测试采样接口"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=10,  # 使用较小的步数以加快测试
            schedule_cfg=self.cfg.schedule_cfg
        )
        
        # 准备测试数据
        data = {
            'norm_pose': torch.randn(2, 25)  # [B, pose_dim]
        }
        
        # 测试采样方法是否存在
        assert hasattr(model, 'sample')
        assert hasattr(model, 'p_sample')
        assert hasattr(model, 'p_sample_loop')
    
    def test_cfg_data_preparation(self):
        """测试 CFG 数据准备"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=100,
            schedule_cfg=self.cfg.schedule_cfg,
            use_cfg=True
        )
        
        data = {
            'norm_pose': torch.randn(2, 25),
            'scene_pc': torch.randn(2, 1024, 3)
        }
        
        cfg_data = model._prepare_cfg_data(data, 2)
        
        # 检查数据是否正确扩展
        assert cfg_data['norm_pose'].shape[0] == 4  # 2 * 2 (条件 + 无条件)
        assert cfg_data['scene_pc'].shape[0] == 4
    
    def test_multi_grasp_support(self):
        """测试多抓取支持"""
        model = DDPMModel(
            eps_model=self.mock_eps_model,
            timesteps=100,
            schedule_cfg=self.cfg.schedule_cfg
        )
        
        # 设置多抓取输出
        model.eps_model.return_value = torch.randn(4, 8, 25)  # [B, num_grasps, pose_dim]
        
        # 测试多抓取前向扩散
        x0 = torch.randn(4, 8, 25)
        t = torch.randint(0, 100, (4,))
        noise = torch.randn_like(x0)
        
        x_t = model.q_sample(x0, t, noise)
        assert x_t.shape == (4, 8, 25)
        
        # 测试多抓取预测
        pred_noise, pred_x0 = model.model_predict(x_t, t, {})
        assert pred_noise.shape == (4, 8, 25)
        assert pred_x0.shape == (4, 8, 25)


def test_import_structure():
    """测试导入结构"""
    # 测试新模块可以正确导入
    from models.diffusion import DDPMModel
    from models.lightning import DDPMLightning
    from models.diffuser_lightning import DDPMLightning as CompatDDPMLightning
    
    assert DDPMModel is not None
    assert DDPMLightning is not None
    assert CompatDDPMLightning is not None


if __name__ == "__main__":
    # 运行测试
    test = TestDiffuserRefactoring()
    test.setup_method()
    
    print("🧪 开始测试重构后的扩散模型...")
    
    try:
        test.test_ddpm_model_creation()
        print("✅ DDPMModel 创建测试通过")
        
        test.test_ddpm_model_q_sample()
        print("✅ 前向扩散过程测试通过")
        
        test.test_ddpm_model_predict()
        print("✅ 模型预测测试通过")
        
        test.test_backward_compatibility()
        print("✅ 向后兼容性测试通过")
        
        test.test_ddpm_model_sampling_interface()
        print("✅ 采样接口测试通过")
        
        test.test_cfg_data_preparation()
        print("✅ CFG 数据准备测试通过")
        
        test.test_multi_grasp_support()
        print("✅ 多抓取支持测试通过")
        
        test_import_structure()
        print("✅ 导入结构测试通过")
        
        print("\n🎉 所有测试通过！重构成功完成。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
