#!/usr/bin/env python3
"""
对比原始版本和重构版本的功能一致性测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel
from utils.hand_types import HandModelType

# 检查CUDA是否可用
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

def test_interface_consistency():
    """测试接口一致性"""
    print("Testing interface consistency...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=100)
    
    # 检查所有原始接口是否存在
    required_methods = [
        'set_parameters',
        'decompose_hand_pose', 
        'cal_distance',
        'cal_self_penetration_energy',
        'cal_joint_limit_energy',
        'cal_finger_finger_distance_energy',
        'cal_finger_palm_distance_energy',
        'cal_table_penetration',
        'get_surface_points',
        'get_contact_candidates',
        'get_penetration_keypoints',
        'get_plotly_data',
        'get_trimesh_data',
        'sample_contact_points',
        '__call__'
    ]
    
    for method_name in required_methods:
        assert hasattr(hand_model, method_name), f"Missing method: {method_name}"
        assert callable(getattr(hand_model, method_name)), f"Method {method_name} is not callable"
    
    # 检查所有原始属性是否存在
    required_properties = [
        'n_fingers', 'batch_size', 'num_fingers', 'fingertip_keywords',
        'fingertip_names', 'joint_names', 'num_joints', 'default_joint_angles',
        'default_orientation', 'urdf_path', 'contact_points_path', 'penetration_points_path'
    ]
    
    # 设置参数以便访问batch_size属性
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(hand_pose)
    
    for prop_name in required_properties:
        assert hasattr(hand_model, prop_name), f"Missing property: {prop_name}"
    
    print("✓ Interface consistency test passed")

def test_module_separation():
    """测试模块分离是否正确"""
    print("Testing module separation...")
    
    hand_model = HandModel(device=DEVICE)
    
    # 检查子模块是否正确创建
    assert hasattr(hand_model, 'physics'), "Missing physics module"
    assert hasattr(hand_model, 'visualizer'), "Missing visualizer module"
    assert hasattr(hand_model, 'loader'), "Missing loader module"
    
    # 检查子模块类型
    from utils.hand_physics import HandPhysics
    from utils.hand_visualizer import HandVisualizer
    from utils.hand_loader import HandLoader
    
    assert isinstance(hand_model.physics, HandPhysics), "physics module has wrong type"
    assert isinstance(hand_model.visualizer, HandVisualizer), "visualizer module has wrong type"
    assert isinstance(hand_model.loader, HandLoader), "loader module has wrong type"
    
    print("✓ Module separation test passed")

def test_data_consistency():
    """测试数据一致性"""
    print("Testing data consistency...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=100)
    
    # 设置相同的随机种子以确保可重复性
    torch.manual_seed(42)
    batch_size = 3
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    
    # 测试set_parameters
    hand_model.set_parameters(hand_pose)
    
    # 检查基本数据结构
    assert hand_model.global_translation.shape == (batch_size, 3)
    assert hand_model.global_rotation.shape == (batch_size, 3, 3)
    assert hand_model.hand_pose.shape == (batch_size, pose_dim)
    
    # 测试多抓取格式
    torch.manual_seed(42)
    num_grasps = 2
    multi_hand_pose = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    hand_model.set_parameters(multi_hand_pose)
    
    assert hand_model.is_multi_grasp == True
    assert hand_model.batch_size_original == batch_size
    assert hand_model.num_grasps == num_grasps
    assert hand_model.global_translation.shape == (batch_size, num_grasps, 3)
    assert hand_model.global_rotation.shape == (batch_size, num_grasps, 3, 3)
    
    print("✓ Data consistency test passed")

def test_physics_calculations():
    """测试物理计算的正确性"""
    print("Testing physics calculations...")
    
    hand_model = HandModel(device=DEVICE)
    
    # 设置手部姿态
    torch.manual_seed(123)
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(hand_pose)
    
    # 测试各种能量计算
    spen_energy = hand_model.cal_self_penetration_energy()
    joint_energy = hand_model.cal_joint_limit_energy()
    
    # 检查输出形状和类型
    assert spen_energy.shape == (batch_size,)
    assert joint_energy.shape == (batch_size,)
    assert spen_energy.dtype == torch.float32
    assert joint_energy.dtype == torch.float32
    
    # 检查能量值的合理性（应该是非负的）
    assert torch.all(spen_energy >= 0), "Self-penetration energy should be non-negative"
    assert torch.all(joint_energy >= 0), "Joint limit energy should be non-negative"
    
    # 测试距离计算
    test_points = torch.randn(batch_size, 50, 3, device=DEVICE)
    distances = hand_model.cal_distance(test_points)
    assert distances.shape == (batch_size, 50)
    
    print("✓ Physics calculations test passed")

def test_visualization_integration():
    """测试可视化集成"""
    print("Testing visualization integration...")
    
    hand_model = HandModel(device=DEVICE, n_surface_points=50)
    
    # 设置手部姿态
    batch_size = 1
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(hand_pose)
    
    # 测试可视化方法
    plotly_data = hand_model.get_plotly_data(0)
    assert isinstance(plotly_data, list)
    assert len(plotly_data) > 0
    
    trimesh_data = hand_model.get_trimesh_data(0)
    assert hasattr(trimesh_data, 'vertices')
    assert hasattr(trimesh_data, 'faces')
    
    # 测试带各种选项的可视化
    plotly_data_full = hand_model.get_plotly_data(
        0, 
        with_surface_points=True,
        with_penetration_keypoints=True
    )
    assert isinstance(plotly_data_full, list)
    assert len(plotly_data_full) >= len(plotly_data)
    
    print("✓ Visualization integration test passed")

def test_error_handling():
    """测试错误处理"""
    print("Testing error handling...")
    
    hand_model = HandModel(device=DEVICE)
    
    # 测试在未设置参数时调用方法的错误处理
    try:
        hand_model.cal_distance(torch.randn(1, 10, 3, device=DEVICE))
        assert False, "Should raise error when parameters not set"
    except ValueError as e:
        assert "Hand parameters are not set" in str(e)
    
    # 测试错误的输入维度
    try:
        wrong_pose = torch.randn(2, 5, device=DEVICE)  # 错误的维度
        hand_model.set_parameters(wrong_pose)
        assert False, "Should raise error for wrong pose dimensions"
    except AssertionError:
        pass  # 预期的错误
    
    print("✓ Error handling test passed")

def main():
    """运行所有一致性测试"""
    print("Starting HandModel refactor consistency validation...\n")
    
    try:
        test_interface_consistency()
        test_module_separation()
        test_data_consistency()
        test_physics_calculations()
        test_visualization_integration()
        test_error_handling()
        
        print("\n🎉 All consistency tests passed! The refactor maintains full compatibility.")
        
    except Exception as e:
        print(f"\n❌ Consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
