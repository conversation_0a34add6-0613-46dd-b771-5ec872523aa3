#!/usr/bin/env python3
"""
验证数据利用率计算的正确性
"""

def verify_utilization_calculation():
    """验证数据利用率20%的计算过程"""
    
    print("=" * 60)
    print("验证数据利用率计算")
    print("=" * 60)
    
    # 从测试结果中的数据
    total_available_grasps = 17739  # 总可用抓取数
    dataset_length = 895           # 数据集长度
    num_grasps = 4                # 每个数据项的抓取数
    num_objects = 179             # 物体数量
    avg_grasps_per_object = 99.1  # 平均每个物体的抓取数
    
    print("输入数据:")
    print(f"  - 总可用抓取数: {total_available_grasps:,}")
    print(f"  - 数据集长度: {dataset_length:,}")
    print(f"  - num_grasps: {num_grasps}")
    print(f"  - 物体数量: {num_objects}")
    print(f"  - 平均每个物体抓取数: {avg_grasps_per_object}")
    
    # 计算实际使用的抓取次数
    actual_usage = dataset_length * num_grasps
    print(f"\n计算过程:")
    print(f"  实际使用抓取次数 = 数据集长度 × num_grasps")
    print(f"                    = {dataset_length} × {num_grasps}")
    print(f"                    = {actual_usage:,}")
    
    # 计算数据利用率
    utilization_rate = actual_usage / total_available_grasps
    print(f"\n  数据利用率 = 实际使用次数 ÷ 总可用抓取数")
    print(f"            = {actual_usage:,} ÷ {total_available_grasps:,}")
    print(f"            = {utilization_rate:.4f}")
    print(f"            = {utilization_rate * 100:.1f}%")
    
    # 验证物体和视角的关系
    views_per_object = dataset_length / num_objects
    print(f"\n数据集结构验证:")
    print(f"  每个物体的视角数 = 数据集长度 ÷ 物体数量")
    print(f"                  = {dataset_length} ÷ {num_objects}")
    print(f"                  = {views_per_object:.1f}")
    
    # 计算浪费的数据
    wasted_grasps = total_available_grasps - actual_usage
    waste_percentage = wasted_grasps / total_available_grasps
    
    print(f"\n数据浪费分析:")
    print(f"  浪费的抓取数 = {total_available_grasps:,} - {actual_usage:,} = {wasted_grasps:,}")
    print(f"  浪费比例 = {waste_percentage:.1%}")
    
    # 计算改进潜力
    potential_dataset_length = total_available_grasps // num_grasps
    improvement_factor = potential_dataset_length / dataset_length
    
    print(f"\n改进潜力分析:")
    print(f"  如果100%利用，可支持的数据集长度 = {total_available_grasps:,} ÷ {num_grasps}")
    print(f"                                = {potential_dataset_length:,}")
    print(f"  改进倍数 = {potential_dataset_length:,} ÷ {dataset_length}")
    print(f"          = {improvement_factor:.2f}倍")
    
    # 总结
    print(f"\n" + "="*60)
    print("结论验证:")
    print(f"✅ 数据利用率确实为 {utilization_rate * 100:.1f}%")
    print(f"✅ 约 {waste_percentage:.0%} 的抓取数据被浪费")
    print(f"✅ 存在 {improvement_factor:.1f}倍的改进空间")
    print("="*60)

if __name__ == "__main__":
    verify_utilization_calculation()
