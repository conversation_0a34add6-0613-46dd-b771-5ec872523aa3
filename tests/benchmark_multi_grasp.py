#!/usr/bin/env python3
"""
多抓取性能基准测试
比较单抓取vs多抓取的训练性能和推理质量
"""

import torch
import torch.nn as nn
import time
import psutil
import sys
import os
import json
import numpy as np
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.diffuser_lightning import DDPMLightning
from utils.hand_helper import process_hand_pose
from omegaconf import OmegaConf


@dataclass
class BenchmarkResult:
    """基准测试结果数据类"""
    name: str
    training_time: float
    inference_time: float
    memory_usage: float
    loss_value: float
    sample_quality: Dict[str, float]
    throughput: float  # samples per second


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self, device='cuda:0', batch_size=32, num_iterations=10):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.batch_size = batch_size
        self.num_iterations = num_iterations
        
    def create_single_grasp_config(self):
        """创建单抓取配置"""
        cfg = OmegaConf.create({
            'name': 'GraspDiffuser',
            'steps': 50,
            'pred_x0': True,
            'use_cfg': False,
            'rot_type': 'r6d',
            'mode': 'camera_centric',
            'multi_grasp': {'enabled': False},
            'decoder': {
                'd_model': 512,
                'nblocks': 4,
                'use_text_condition': True,
                'multi_grasp': {'grasp_self_attention': {'enabled': False}}
            },
            'criterion': {
                'loss_weights': {'diffusion_loss': 1.0, 'neg_loss': 0.1}
            }
        })
        return cfg
    
    def create_multi_grasp_config(self, num_grasps=8):
        """创建多抓取配置"""
        cfg = OmegaConf.create({
            'name': 'GraspDiffuser',
            'steps': 50,
            'pred_x0': True,
            'use_cfg': False,
            'rot_type': 'r6d',
            'mode': 'camera_centric',
            'multi_grasp': {
                'enabled': True,
                'num_grasps': num_grasps,
                'loss_aggregation': 'mean'
            },
            'decoder': {
                'd_model': 512,
                'nblocks': 4,
                'use_text_condition': True,
                'multi_grasp': {
                    'grasp_self_attention': {'enabled': True, 'num_heads': 8}
                }
            },
            'criterion': {
                'loss_weights': {'diffusion_loss': 1.0, 'neg_loss': 0.1}
            }
        })
        return cfg
    
    def create_single_grasp_batch(self):
        """创建单抓取批次数据"""
        B = self.batch_size
        batch = {
            'hand_model_pose': torch.randn(B, 23, device=self.device),
            'se3': torch.eye(4, device=self.device).unsqueeze(0).repeat(B, 1, 1),
            'scene_pc': torch.randn(B, 10000, 6, device=self.device),
            'positive_prompt': ['grasp object'] * B,
        }
        return batch
    
    def create_multi_grasp_batch(self, num_grasps=8):
        """创建多抓取批次数据"""
        B = self.batch_size
        batch = {
            'hand_model_pose': torch.randn(B, num_grasps, 23, device=self.device),
            'se3': torch.eye(4, device=self.device).unsqueeze(0).unsqueeze(0).repeat(B, num_grasps, 1, 1),
            'scene_pc': torch.randn(B, 10000, 6, device=self.device),
            'positive_prompt': ['grasp object'] * B,
        }
        return batch
    
    def measure_memory_usage(self):
        """测量内存使用情况"""
        if torch.cuda.is_available():
            return torch.cuda.max_memory_allocated() / 1024**3  # GB
        else:
            return psutil.Process().memory_info().rss / 1024**3  # GB
    
    def reset_memory_stats(self):
        """重置内存统计"""
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
    
    def benchmark_training_performance(self, model, batch, model_name):
        """基准测试训练性能"""
        model.train()
        
        # 预热
        for _ in range(3):
            loss = model.training_step(batch, 0)
            loss.backward()
            model.zero_grad()
        
        # 重置内存统计
        self.reset_memory_stats()
        
        # 测量训练时间
        start_time = time.time()
        total_loss = 0
        
        for i in range(self.num_iterations):
            loss = model.training_step(batch, i)
            loss.backward()
            model.zero_grad()
            total_loss += loss.item()
        
        training_time = (time.time() - start_time) / self.num_iterations
        avg_loss = total_loss / self.num_iterations
        memory_usage = self.measure_memory_usage()
        
        # 计算吞吐量
        if 'multi' in model_name.lower():
            # 多抓取：每个批次处理 batch_size * num_grasps 个样本
            num_grasps = batch['hand_model_pose'].shape[1]
            samples_per_batch = self.batch_size * num_grasps
        else:
            # 单抓取：每个批次处理 batch_size 个样本
            samples_per_batch = self.batch_size
        
        throughput = samples_per_batch / training_time
        
        return {
            'training_time': training_time,
            'loss_value': avg_loss,
            'memory_usage': memory_usage,
            'throughput': throughput
        }
    
    def benchmark_inference_performance(self, model, batch, model_name):
        """基准测试推理性能"""
        model.eval()
        
        # 预热
        with torch.no_grad():
            for _ in range(3):
                _ = model.sample(batch, k=1)
        
        # 重置内存统计
        self.reset_memory_stats()
        
        # 测量推理时间
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(self.num_iterations):
                samples = model.sample(batch, k=1)
        
        inference_time = (time.time() - start_time) / self.num_iterations
        memory_usage = self.measure_memory_usage()
        
        # 计算推理吞吐量
        throughput = self.batch_size / inference_time
        
        return {
            'inference_time': inference_time,
            'memory_usage': memory_usage,
            'throughput': throughput,
            'sample_shape': samples.shape if 'samples' in locals() else None
        }
    
    def evaluate_sample_quality(self, model, batch, ground_truth=None):
        """评估采样质量"""
        model.eval()
        
        with torch.no_grad():
            samples = model.sample(batch, k=4)  # 采样4个候选
        
        # 基础质量指标
        quality_metrics = {}
        
        if samples.dim() >= 3:
            # 多抓取或多采样格式
            if samples.dim() == 4:  # [B, k, num_grasps, pose_dim]
                # 计算多样性
                B, k, num_grasps, pose_dim = samples.shape
                diversity_scores = []
                
                for b in range(B):
                    sample_b = samples[b, 0]  # 取第一个采样 [num_grasps, pose_dim]
                    # 计算抓取间距离
                    distances = torch.cdist(sample_b, sample_b, p=2)
                    # 排除对角线
                    mask = ~torch.eye(num_grasps, dtype=torch.bool, device=distances.device)
                    avg_distance = distances[mask].mean()
                    diversity_scores.append(avg_distance.item())
                
                quality_metrics['diversity_score'] = np.mean(diversity_scores)
                quality_metrics['diversity_std'] = np.std(diversity_scores)
            
            # 计算采样一致性
            if samples.dim() >= 3:
                sample_std = torch.std(samples, dim=1).mean().item()
                quality_metrics['sample_consistency'] = 1.0 / (1.0 + sample_std)
        
        # 数值稳定性
        quality_metrics['numerical_stability'] = float(not (torch.isnan(samples).any() or torch.isinf(samples).any()))
        
        # 范围合理性（假设姿态应该在合理范围内）
        pose_range_ok = (samples.abs() < 10.0).float().mean().item()
        quality_metrics['pose_range_validity'] = pose_range_ok
        
        return quality_metrics
    
    def run_comprehensive_benchmark(self):
        """运行综合基准测试"""
        print("开始运行多抓取性能基准测试...")
        print("=" * 80)
        
        results = {}
        
        # 测试配置
        test_configs = [
            ('Single Grasp', self.create_single_grasp_config(), self.create_single_grasp_batch()),
            ('Multi Grasp (4)', self.create_multi_grasp_config(4), self.create_multi_grasp_batch(4)),
            ('Multi Grasp (8)', self.create_multi_grasp_config(8), self.create_multi_grasp_batch(8)),
            ('Multi Grasp (16)', self.create_multi_grasp_config(16), self.create_multi_grasp_batch(16)),
        ]
        
        for config_name, model_config, batch_data in test_configs:
            print(f"\n测试配置: {config_name}")
            print("-" * 40)
            
            try:
                # 创建模型
                model = DDPMLightning(model_config)
                model = model.to(self.device)
                
                # 处理数据
                processed_batch = process_hand_pose(batch_data, 
                                                  rot_type=model_config.rot_type,
                                                  mode=model_config.mode)
                
                # 训练性能测试
                print("测试训练性能...")
                train_results = self.benchmark_training_performance(model, processed_batch, config_name)
                
                # 推理性能测试
                print("测试推理性能...")
                inference_results = self.benchmark_inference_performance(model, processed_batch, config_name)
                
                # 质量评估
                print("评估采样质量...")
                quality_results = self.evaluate_sample_quality(model, processed_batch)
                
                # 汇总结果
                results[config_name] = BenchmarkResult(
                    name=config_name,
                    training_time=train_results['training_time'],
                    inference_time=inference_results['inference_time'],
                    memory_usage=max(train_results['memory_usage'], inference_results['memory_usage']),
                    loss_value=train_results['loss_value'],
                    sample_quality=quality_results,
                    throughput=train_results['throughput']
                )
                
                print(f"✅ {config_name} 测试完成")
                
            except Exception as e:
                print(f"❌ {config_name} 测试失败: {e}")
                continue
        
        return results

    def print_benchmark_results(self, results: Dict[str, BenchmarkResult]):
        """打印基准测试结果"""
        print("\n" + "=" * 80)
        print("基准测试结果汇总")
        print("=" * 80)

        # 表头
        print(f"{'配置':<20} {'训练时间(s)':<12} {'推理时间(s)':<12} {'内存(GB)':<10} {'吞吐量':<12} {'损失值':<10}")
        print("-" * 80)

        # 结果行
        for name, result in results.items():
            print(f"{name:<20} {result.training_time:<12.4f} {result.inference_time:<12.4f} "
                  f"{result.memory_usage:<10.2f} {result.throughput:<12.1f} {result.loss_value:<10.4f}")

        # 质量指标
        print("\n质量指标:")
        print("-" * 60)
        for name, result in results.items():
            print(f"\n{name}:")
            for metric, value in result.sample_quality.items():
                print(f"  {metric}: {value:.4f}")

    def save_results_to_json(self, results: Dict[str, BenchmarkResult], filepath: str):
        """保存结果到JSON文件"""
        json_results = {}
        for name, result in results.items():
            json_results[name] = {
                'name': result.name,
                'training_time': result.training_time,
                'inference_time': result.inference_time,
                'memory_usage': result.memory_usage,
                'loss_value': result.loss_value,
                'sample_quality': result.sample_quality,
                'throughput': result.throughput
            }

        with open(filepath, 'w') as f:
            json.dump(json_results, f, indent=2)

        print(f"结果已保存到: {filepath}")

    def plot_performance_comparison(self, results: Dict[str, BenchmarkResult], save_path: str = None):
        """绘制性能对比图"""
        try:
            import matplotlib.pyplot as plt

            names = list(results.keys())
            training_times = [results[name].training_time for name in names]
            inference_times = [results[name].inference_time for name in names]
            memory_usage = [results[name].memory_usage for name in names]
            throughputs = [results[name].throughput for name in names]

            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

            # 训练时间对比
            ax1.bar(names, training_times, color='skyblue')
            ax1.set_title('训练时间对比')
            ax1.set_ylabel('时间 (秒)')
            ax1.tick_params(axis='x', rotation=45)

            # 推理时间对比
            ax2.bar(names, inference_times, color='lightgreen')
            ax2.set_title('推理时间对比')
            ax2.set_ylabel('时间 (秒)')
            ax2.tick_params(axis='x', rotation=45)

            # 内存使用对比
            ax3.bar(names, memory_usage, color='salmon')
            ax3.set_title('内存使用对比')
            ax3.set_ylabel('内存 (GB)')
            ax3.tick_params(axis='x', rotation=45)

            # 吞吐量对比
            ax4.bar(names, throughputs, color='gold')
            ax4.set_title('吞吐量对比')
            ax4.set_ylabel('样本/秒')
            ax4.tick_params(axis='x', rotation=45)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"性能对比图已保存到: {save_path}")
            else:
                plt.show()

        except ImportError:
            print("matplotlib未安装，跳过绘图")

    def calculate_efficiency_metrics(self, results: Dict[str, BenchmarkResult]):
        """计算效率指标"""
        print("\n效率分析:")
        print("-" * 60)

        if 'Single Grasp' in results:
            single_result = results['Single Grasp']

            for name, result in results.items():
                if name == 'Single Grasp':
                    continue

                # 提取抓取数量
                if 'Multi Grasp' in name:
                    num_grasps = int(name.split('(')[1].split(')')[0])

                    # 理论上，多抓取应该比单抓取快 num_grasps 倍
                    theoretical_speedup = num_grasps
                    actual_speedup = (single_result.training_time * num_grasps) / result.training_time
                    efficiency = actual_speedup / theoretical_speedup

                    print(f"{name}:")
                    print(f"  理论加速比: {theoretical_speedup:.1f}x")
                    print(f"  实际加速比: {actual_speedup:.2f}x")
                    print(f"  效率: {efficiency:.2%}")
                    print(f"  内存开销: {result.memory_usage / single_result.memory_usage:.2f}x")


def run_quick_benchmark():
    """运行快速基准测试"""
    print("运行快速性能基准测试...")

    benchmark = PerformanceBenchmark(batch_size=16, num_iterations=5)

    # 只测试关键配置
    test_configs = [
        ('Single Grasp', benchmark.create_single_grasp_config(), benchmark.create_single_grasp_batch()),
        ('Multi Grasp (8)', benchmark.create_multi_grasp_config(8), benchmark.create_multi_grasp_batch(8)),
    ]

    results = {}

    for config_name, model_config, batch_data in test_configs:
        print(f"\n测试 {config_name}...")

        try:
            model = DDPMLightning(model_config)
            model = model.to(benchmark.device)

            processed_batch = process_hand_pose(batch_data,
                                              rot_type=model_config.rot_type,
                                              mode=model_config.mode)

            train_results = benchmark.benchmark_training_performance(model, processed_batch, config_name)
            quality_results = benchmark.evaluate_sample_quality(model, processed_batch)

            results[config_name] = BenchmarkResult(
                name=config_name,
                training_time=train_results['training_time'],
                inference_time=0.0,  # 快速测试跳过推理
                memory_usage=train_results['memory_usage'],
                loss_value=train_results['loss_value'],
                sample_quality=quality_results,
                throughput=train_results['throughput']
            )

            print(f"✅ {config_name} 完成")

        except Exception as e:
            print(f"❌ {config_name} 失败: {e}")

    return results


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='多抓取性能基准测试')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    parser.add_argument('--batch-size', type=int, default=32, help='批次大小')
    parser.add_argument('--iterations', type=int, default=10, help='测试迭代次数')
    parser.add_argument('--device', type=str, default='cuda:0', help='设备')
    parser.add_argument('--save-results', type=str, help='保存结果的文件路径')
    parser.add_argument('--save-plot', type=str, help='保存图表的文件路径')

    args = parser.parse_args()

    if args.quick:
        results = run_quick_benchmark()
    else:
        benchmark = PerformanceBenchmark(
            device=args.device,
            batch_size=args.batch_size,
            num_iterations=args.iterations
        )
        results = benchmark.run_comprehensive_benchmark()

    if results:
        benchmark = PerformanceBenchmark()
        benchmark.print_benchmark_results(results)
        benchmark.calculate_efficiency_metrics(results)

        if args.save_results:
            benchmark.save_results_to_json(results, args.save_results)

        if args.save_plot:
            benchmark.plot_performance_comparison(results, args.save_plot)


if __name__ == "__main__":
    main()
