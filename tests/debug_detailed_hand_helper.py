#!/usr/bin/env python3
"""
Detailed debugging of parameter and r6d normalization
"""

import sys
import os
import torch
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    get_min_max_from_stats, 
    POSE_STATS,
    NORM_UPPER, NORM_LOWER,
    normalize_rot6d_torch
)

def debug_parameters():
    """Debug parameter normalization in detail"""
    print("=== Debugging parameter normalization ===")
    
    mode = "camera_centric_obj_mean_normalized"
    
    # Print statistics for joint angles
    print("Joint angle statistics:")
    for i in range(16):
        label = f'joint_angle_{i}'
        if label in POSE_STATS[mode]:
            stats = POSE_STATS[mode][label]
            print(f"  {label}: min={stats['min']:.6f}, max={stats['max']:.6f}")
        else:
            print(f"  {label}: NOT FOUND")
    
    # Test with values that are clearly within ranges
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_param = torch.tensor([
        [0.8, 0.0, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 0.8, -0.1, 0.7, 0.1, 1.7, 0.1, 0.0, -0.2],  # Mix of values
    ], device=device, dtype=torch.float32)
    
    print(f"\nInput parameters: {test_param}")
    
    # Get min/max values for parameters
    labels = [f'joint_angle_{i}' for i in range(16)]
    p_min, p_max = get_min_max_from_stats(mode, labels, device, torch.float32)
    
    print(f"Min values: {p_min}")
    print(f"Max values: {p_max}")
    
    # Manual normalization calculation
    p_range = p_max - p_min
    p_range[p_range == 0] = 1e-8
    
    print(f"Range: {p_range}")
    
    # Apply normalization
    p_scaled_0_1 = (test_param - p_min) / p_range
    p_final_normalized = p_scaled_0_1 * (NORM_UPPER - NORM_LOWER) + NORM_LOWER
    
    print(f"Scaled to [0,1]: {p_scaled_0_1}")
    print(f"Final normalized: {p_final_normalized}")
    print(f"Range of normalized values: [{p_final_normalized.min().item():.4f}, {p_final_normalized.max().item():.4f}]")

def debug_r6d():
    """Debug R6D normalization"""
    print("\n=== Debugging R6D normalization ===")
    
    # Test with valid rotation 6D vectors
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Valid R6D representations (first two rows of rotation matrices)
    test_rot_r6d = torch.tensor([
        [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],      # Identity
        [0.0, 1.0, 0.0, -1.0, 0.0, 0.0],     # 90 degree rotation around z
        [0.707, 0.0, 0.707, 0.0, 1.0, 0.0],  # 45 degree rotation around y
    ], device=device, dtype=torch.float32)
    
    print(f"Input R6D: {test_rot_r6d}")
    
    # Apply R6D normalization (orthogonalization)
    norm_rot_r6d = normalize_rot6d_torch(test_rot_r6d)
    
    print(f"Normalized R6D: {norm_rot_r6d}")
    print(f"Range of normalized values: [{norm_rot_r6d.min().item():.4f}, {norm_rot_r6d.max().item():.4f}]")

if __name__ == "__main__":
    debug_parameters()
    debug_r6d()