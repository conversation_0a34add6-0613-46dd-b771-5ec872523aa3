#!/usr/bin/env python3
"""
Proper tests for hand_helper.py with values within expected ranges
"""

import sys
import os
import torch
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    normalize_trans_torch, denormalize_trans_torch,
    normalize_param_torch, denormalize_param_torch,
    normalize_rot_torch, denormalize_rot_torch,
    norm_hand_pose_robust, denorm_hand_pose_robust,
    normalize_rot6d_torch, normalize_rot6d_numpy,
    normalize_trans_numpy, denormalize_trans_numpy,
    normalize_param_numpy, denormalize_param_numpy,
    normalize_rot_numpy, denormalize_rot_numpy,
    norm_hand_pose, denorm_hand_pose,
    NORM_UPPER, NORM_LOWER
)

def test_normalization_with_valid_ranges():
    """Test normalization with values within the expected statistical ranges"""
    print("=== Testing normalization with valid ranges ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # Create test data within the statistical ranges
    # Translation ranges from JSON: x[-0.2108, 0.2162], y[-0.2149, 0.2071], z[-0.2118, 0.0730]
    test_trans = torch.tensor([
        [0.1, 0.15, 0.05],   # Within range
        [-0.1, -0.15, -0.1], # Within range
    ], device=device, dtype=torch.float32)
    
    # Parameter ranges: based on JSON, values are typically between 0.19-1.51 for most joints
    test_param = torch.tensor([
        [0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2],
        [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    ], device=device, dtype=torch.float32)
    
    # R6D rotation - create valid rotation matrices and convert to 6D representation
    test_rot_r6d = torch.tensor([
        [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Identity rotation
        [0.707, 0.707, 0.0, -0.707, 0.707, 0.0],  # 45-degree rotation around z-axis
    ], device=device, dtype=torch.float32)
    
    print(f"Input translation: {test_trans}")
    print(f"Input parameters: {test_param[0][:3]}...")  # Show first 3 values
    print(f"Input r6d rotation: {test_rot_r6d}")
    
    try:
        # Test translation normalization
        norm_trans = normalize_trans_torch(test_trans, mode)
        print(f"Normalized translation: {norm_trans}")
        print(f"Translation range: [{norm_trans.min().item():.4f}, {norm_trans.max().item():.4f}]")
        trans_in_range = (norm_trans.min() >= NORM_LOWER) and (norm_trans.max() <= NORM_UPPER)
        print(f"Translation in expected range [{NORM_LOWER}, {NORM_UPPER}]: {trans_in_range}")
        
        # Test parameter normalization
        norm_param = normalize_param_torch(test_param, mode)
        print(f"Normalized parameters (first 3 values): {norm_param[0][:3]}")
        print(f"Parameter range: [{norm_param.min().item():.4f}, {norm_param.max().item():.4f}]")
        param_in_range = (norm_param.min() >= NORM_LOWER) and (norm_param.max() <= NORM_UPPER)
        print(f"Parameters in expected range [{NORM_LOWER}, {NORM_UPPER}]: {param_in_range}")
        
        # Test r6d rotation normalization
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d, 'r6d', mode)
        print(f"Normalized r6d rotation: {norm_rot_r6d}")
        print(f"R6D rotation range: [{norm_rot_r6d.min().item():.4f}, {norm_rot_r6d.max().item():.4f}]")
        r6d_in_range = (norm_rot_r6d.min() >= NORM_LOWER) and (norm_rot_r6d.max() <= NORM_UPPER)
        print(f"R6D rotation in expected range [{NORM_LOWER}, {NORM_UPPER}]: {r6d_in_range}")
        
        return trans_in_range and param_in_range and r6d_in_range
        
    except Exception as e:
        print(f"Normalization test with valid ranges failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inverse_operations():
    """Test that normalization and denormalization are inverse operations"""
    print("\n=== Testing inverse operations ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    mode = "camera_centric_obj_mean_normalized"
    
    # Create test data within the statistical ranges
    test_trans = torch.tensor([
        [0.1, 0.15, 0.05],
        [-0.1, -0.15, -0.1],
    ], device=device, dtype=torch.float32)
    
    test_param = torch.tensor([
        [0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2],
        [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    ], device=device, dtype=torch.float32)
    
    test_rot_r6d = torch.tensor([
        [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
        [0.707, 0.707, 0.0, -0.707, 0.707, 0.0],
    ], device=device, dtype=torch.float32)
    
    try:
        # Test translation
        norm_trans = normalize_trans_torch(test_trans, mode)
        denorm_trans = denormalize_trans_torch(norm_trans, mode)
        trans_error = torch.abs(test_trans - denorm_trans).max().item()
        print(f"Translation inverse operation error: {trans_error:.6f}")
        
        # Test parameters
        norm_param = normalize_param_torch(test_param, mode)
        denorm_param = denormalize_param_torch(norm_param, mode)
        param_error = torch.abs(test_param - denorm_param).max().item()
        print(f"Parameter inverse operation error: {param_error:.6f}")
        
        # Test r6d rotation
        norm_rot_r6d = normalize_rot_torch(test_rot_r6d, 'r6d', mode)
        denorm_rot_r6d = denormalize_rot_torch(norm_rot_r6d, 'r6d', mode)
        rot_r6d_error = torch.abs(test_rot_r6d - denorm_rot_r6d).max().item()
        print(f"R6D rotation inverse operation error: {rot_r6d_error:.6f}")
        
        tolerance = 1e-4
        return (trans_error < tolerance) and (param_error < tolerance) and (rot_r6d_error < tolerance)
        
    except Exception as e:
        print(f"Inverse operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Starting proper tests for hand_helper.py...")
    
    tests = [
        ("Normalization with valid ranges", test_normalization_with_valid_ranges),
        ("Inverse operations", test_inverse_operations),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name}: ❌ EXCEPTION - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! hand_helper.py is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)