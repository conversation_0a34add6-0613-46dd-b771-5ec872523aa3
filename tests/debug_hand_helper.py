#!/usr/bin/env python3
"""
Debug script to understand normalization behavior
"""

import sys
import os
import torch
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_helper import (
    get_min_max_from_stats, 
    POSE_STATS,
    NORM_UPPER, NORM_LOWER
)

def debug_normalization():
    """Debug the normalization process"""
    print("=== Debugging normalization ===")
    
    # Test data
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_trans = torch.tensor([[0.1, 0.2, 0.3], [-0.1, -0.2, -0.3]], device=device, dtype=torch.float32)
    mode = "camera_centric_obj_mean_normalized"
    
    print(f"Input translation: {test_trans}")
    
    # Get min/max values for translation
    labels = ['translation_x', 'translation_y', 'translation_z']
    t_min, t_max = get_min_max_from_stats(mode, labels, device, torch.float32)
    
    print(f"Min values: {t_min}")
    print(f"Max values: {t_max}")
    
    # Manual normalization calculation
    t_range = t_max - t_min
    t_range[t_range == 0] = 1e-8
    
    print(f"Range: {t_range}")
    
    # Apply normalization
    t_scaled_0_1 = (test_trans - t_min) / t_range
    t_final_normalized = t_scaled_0_1 * (NORM_UPPER - NORM_LOWER) + NORM_LOWER
    
    print(f"Scaled to [0,1]: {t_scaled_0_1}")
    print(f"Final normalized: {t_final_normalized}")
    print(f"Range of normalized values: [{t_final_normalized.min().item():.4f}, {t_final_normalized.max().item():.4f}]")

def debug_pose_stats():
    """Debug the pose statistics"""
    print("\n=== Debugging pose statistics ===")
    
    mode = "camera_centric_obj_mean_normalized"
    
    # Print statistics for translation dimensions
    trans_labels = ['translation_x', 'translation_y', 'translation_z']
    for label in trans_labels:
        if label in POSE_STATS[mode]:
            stats = POSE_STATS[mode][label]
            print(f"{label}: min={stats['min']:.6f}, max={stats['max']:.6f}")
        else:
            print(f"{label}: NOT FOUND")

if __name__ == "__main__":
    debug_pose_stats()
    debug_normalization()