#!/usr/bin/env python3
"""
测试缓存修复
验证穷尽采样的缓存系统是否正常工作
"""

import sys
import os
import time
import torch
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached


def test_cache_system_fix():
    """测试缓存系统修复"""
    
    print("=" * 80)
    print("测试缓存系统修复")
    print("=" * 80)
    
    # 基础配置
    base_config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric_scene_mean_normalized",
        "max_grasps_per_object": 20,
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "random",
        "cache_mode": "train"
    }
    
    # 测试穷尽采样缓存
    print(f"\n{'='*60}")
    print("测试穷尽采样缓存系统")
    print(f"{'='*60}")
    
    config = {
        **base_config,
        "use_exhaustive_sampling": True,
        "exhaustive_sampling_strategy": "sequential",
        "cache_version": "v2.0_cache_fix_test"
    }
    
    try:
        print("创建穷尽采样数据集...")
        start_time = time.time()
        dataset = SceneLeapPlusDatasetCached(**config)
        init_time = time.time() - start_time
        
        print(f"✓ 数据集创建成功")
        print(f"  - 初始化时间: {init_time:.2f}s")
        print(f"  - 数据集长度: {len(dataset)}")
        print(f"  - 使用穷尽采样: {dataset.use_exhaustive_sampling}")
        print(f"  - 穷尽采样策略: {dataset.exhaustive_sampling_strategy}")
        
        # 获取缓存信息
        cache_info = dataset.get_cache_info()
        cache_path = cache_info.get('cache_path', '')
        cache_filename = os.path.basename(cache_path)
        
        print(f"  - 缓存文件: {cache_filename}")
        print(f"  - 缓存路径: {cache_path}")
        
        # 测试样本加载
        print(f"\n测试样本加载...")
        test_indices = [0, 1, 2] if len(dataset) > 2 else list(range(len(dataset)))
        
        for idx in test_indices:
            try:
                start_time = time.time()
                sample = dataset[idx]
                load_time = time.time() - start_time
                
                if 'error' in sample:
                    print(f"  样本 {idx}: 错误 - {sample['error']}")
                else:
                    hand_poses = sample.get('hand_model_pose')
                    se3_matrices = sample.get('se3')
                    
                    if isinstance(hand_poses, torch.Tensor):
                        print(f"  样本 {idx}: ✓ 加载成功 (时间={load_time:.3f}s, shape={hand_poses.shape})")
                    else:
                        print(f"  样本 {idx}: ⚠️ 数据格式异常")
                        
            except Exception as e:
                print(f"  样本 {idx}: ✗ 加载失败 - {str(e)}")
        
        # 验证缓存文件存在且有效
        if os.path.exists(cache_path):
            file_size = os.path.getsize(cache_path) / (1024 * 1024)  # MB
            print(f"  - 缓存文件大小: {file_size:.2f} MB")
            print(f"  ✓ 缓存文件存在且有效")
        else:
            print(f"  ✗ 缓存文件不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_cache_version_uniqueness():
    """测试缓存版本唯一性"""
    
    print(f"\n{'='*60}")
    print("测试缓存版本唯一性")
    print(f"{'='*60}")
    
    base_config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric_scene_mean_normalized",
        "max_grasps_per_object": 20,
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "random",
        "cache_mode": "train",
        "cache_version": "v2.0_uniqueness_test"
    }
    
    configs = [
        {
            "name": "传统采样",
            "config": {**base_config, "use_exhaustive_sampling": False}
        },
        {
            "name": "穷尽采样-Sequential",
            "config": {**base_config, "use_exhaustive_sampling": True, "exhaustive_sampling_strategy": "sequential"}
        },
        {
            "name": "穷尽采样-FPS",
            "config": {**base_config, "use_exhaustive_sampling": True, "exhaustive_sampling_strategy": "chunk_farthest_point"}
        }
    ]
    
    cache_files = []
    
    for test_config in configs:
        try:
            print(f"\n测试: {test_config['name']}")
            dataset = SceneLeapPlusDatasetCached(**test_config['config'])
            
            cache_info = dataset.get_cache_info()
            cache_path = cache_info.get('cache_path', '')
            cache_filename = os.path.basename(cache_path)
            
            cache_files.append({
                'name': test_config['name'],
                'filename': cache_filename,
                'path': cache_path
            })
            
            print(f"  缓存文件: {cache_filename}")
            
        except Exception as e:
            print(f"  ✗ 失败: {str(e)}")
    
    # 检查唯一性
    print(f"\n缓存文件唯一性检查:")
    filenames = [cf['filename'] for cf in cache_files]
    unique_filenames = set(filenames)
    
    if len(unique_filenames) == len(filenames):
        print(f"  ✓ 所有缓存文件名都是唯一的")
        for cf in cache_files:
            print(f"    {cf['name']}: {cf['filename']}")
        return True
    else:
        print(f"  ✗ 发现重复的缓存文件名")
        return False


if __name__ == "__main__":
    try:
        print("开始缓存系统修复测试...")
        
        # 测试缓存系统
        cache_test_result = test_cache_system_fix()
        
        # 测试缓存版本唯一性
        uniqueness_test_result = test_cache_version_uniqueness()
        
        # 总结
        print(f"\n{'='*80}")
        print("测试总结")
        print(f"{'='*80}")
        print(f"缓存系统测试: {'✓ 通过' if cache_test_result else '✗ 失败'}")
        print(f"唯一性测试: {'✓ 通过' if uniqueness_test_result else '✗ 失败'}")
        
        if cache_test_result and uniqueness_test_result:
            print(f"\n🎉 所有测试通过！缓存系统修复成功！")
        else:
            print(f"\n⚠️ 部分测试失败，需要进一步调试。")
        
    except Exception as e:
        print(f"测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
