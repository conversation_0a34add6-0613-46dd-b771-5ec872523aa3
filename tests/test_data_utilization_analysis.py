#!/usr/bin/env python3
"""
数据利用率分析测试
验证文档中提到的数据利用率问题
"""

import sys
import os
import time
import torch
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached


def analyze_data_utilization():
    """分析数据集的数据利用率问题"""
    
    print("=" * 80)
    print("数据利用率分析测试")
    print("=" * 80)
    
    # 使用真实路径进行测试 - 使用更大的数据集来重现文档中的问题
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,  # 使用较小的值进行测试
        "mode": "camera_centric_scene_mean_normalized",
        "max_grasps_per_object": 200,  # 使用更大的值来重现文档中的问题
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "random",
        "cache_version": "v1.0_plus_utilization_test_large",
        "cache_mode": "train"
    }
    
    print("创建数据集实例...")
    start_time = time.time()
    dataset = SceneLeapPlusDatasetCached(**config)
    init_time = time.time() - start_time
    
    print(f"数据集初始化时间: {init_time:.2f}秒")
    print(f"数据集长度: {len(dataset)}")
    print(f"num_grasps: {dataset.num_grasps}")
    print(f"max_grasps_per_object: {dataset.max_grasps_per_object}")
    print(f"grasp_sampling_strategy: {dataset.grasp_sampling_strategy}")
    
    # 统计总的可用抓取数
    print("\n" + "=" * 50)
    print("统计可用抓取数据")
    print("=" * 50)
    
    total_available_grasps = 0
    scene_object_stats = {}
    
    for scene_id, scene_data in dataset.hand_pose_data.items():
        for obj_code, poses_tensor in scene_data.items():
            if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                # 应用 max_grasps_per_object 限制
                max_grasps = dataset.max_grasps_per_object
                if max_grasps is not None:
                    actual_grasps = min(poses_tensor.shape[0], max_grasps)
                else:
                    actual_grasps = poses_tensor.shape[0]
                
                key = f"{scene_id}_{obj_code}"
                scene_object_stats[key] = {
                    'original_grasps': poses_tensor.shape[0],
                    'available_grasps': actual_grasps
                }
                total_available_grasps += actual_grasps
    
    print(f"总场景-物体组合数: {len(scene_object_stats)}")
    print(f"总可用抓取数: {total_available_grasps}")
    
    # 显示前10个场景-物体的统计
    print(f"\n前10个场景-物体的抓取统计:")
    for i, (key, stats) in enumerate(list(scene_object_stats.items())[:10]):
        print(f"  {key}: 原始={stats['original_grasps']}, 可用={stats['available_grasps']}")
    
    # 计算理论采样次数
    dataset_length = len(dataset)
    num_grasps = dataset.num_grasps
    theoretical_sampling_count = dataset_length * num_grasps
    
    print(f"\n" + "=" * 50)
    print("数据利用率分析")
    print("=" * 50)
    print(f"数据集长度: {dataset_length}")
    print(f"每个样本抓取数 (num_grasps): {num_grasps}")
    print(f"理论采样次数: {dataset_length} × {num_grasps} = {theoretical_sampling_count}")
    print(f"总可用抓取数: {total_available_grasps}")
    
    if total_available_grasps > 0:
        utilization_rate = theoretical_sampling_count / total_available_grasps
        print(f"数据利用率: {theoretical_sampling_count} / {total_available_grasps} = {utilization_rate:.1%}")
    else:
        print("数据利用率: 无法计算 (没有可用抓取)")
    
    # 分析数据集结构
    print(f"\n" + "=" * 50)
    print("数据集结构分析")
    print("=" * 50)
    
    # 统计场景、物体、视角
    scenes = set()
    objects = set()
    views_per_scene_object = {}
    
    for item_data in dataset.data:
        scene_id = item_data['scene_id']
        object_code = item_data['object_code']
        view_idx = item_data['depth_view_index']
        
        scenes.add(scene_id)
        objects.add(object_code)
        
        key = f"{scene_id}_{object_code}"
        if key not in views_per_scene_object:
            views_per_scene_object[key] = []
        views_per_scene_object[key].append(view_idx)
    
    print(f"唯一场景数: {len(scenes)}")
    print(f"唯一物体数: {len(objects)}")
    print(f"场景-物体组合数: {len(views_per_scene_object)}")
    
    # 计算平均视角数
    total_views = sum(len(views) for views in views_per_scene_object.values())
    avg_views_per_scene_object = total_views / len(views_per_scene_object) if views_per_scene_object else 0
    print(f"平均每个场景-物体的视角数: {avg_views_per_scene_object:.2f}")
    
    # 验证数据集长度计算公式
    expected_length = sum(len(views) for views in views_per_scene_object.values())
    print(f"预期数据集长度: {expected_length}")
    print(f"实际数据集长度: {dataset_length}")
    print(f"长度匹配: {'✓' if expected_length == dataset_length else '✗'}")
    
    # 测试几个样本
    print(f"\n" + "=" * 50)
    print("样本测试")
    print("=" * 50)
    
    test_indices = [0, 1, 2] if len(dataset) > 2 else list(range(len(dataset)))
    
    for idx in test_indices:
        try:
            start_time = time.time()
            sample = dataset[idx]
            load_time = time.time() - start_time
            
            if 'error' in sample:
                print(f"样本 {idx}: 错误 - {sample['error']}")
            else:
                hand_poses = sample.get('hand_model_pose')
                se3_matrices = sample.get('se3')
                
                print(f"样本 {idx}: 加载时间={load_time:.3f}s")
                if isinstance(hand_poses, torch.Tensor):
                    print(f"  hand_model_pose shape: {hand_poses.shape}")
                if isinstance(se3_matrices, torch.Tensor):
                    print(f"  se3 shape: {se3_matrices.shape}")
                    
        except Exception as e:
            print(f"样本 {idx}: 加载失败 - {str(e)}")
    
    print(f"\n" + "=" * 80)
    print("分析总结")
    print("=" * 80)
    
    if total_available_grasps > 0:
        print(f"1. 数据利用率: {utilization_rate:.1%}")
        if utilization_rate < 0.5:
            print("   ⚠️  数据利用率低于50%，存在严重的数据浪费")
        elif utilization_rate < 0.8:
            print("   ⚠️  数据利用率偏低，建议优化")
        else:
            print("   ✓  数据利用率良好")
    
    print(f"2. 数据集设计: 数据集长度由 (场景 × 物体 × 视角) 决定")
    print(f"3. 每个数据项包含 {num_grasps} 个抓取")
    print(f"4. 总共使用 {theoretical_sampling_count} 个抓取样本")
    print(f"5. 可用抓取总数: {total_available_grasps}")
    
    # 计算改进潜力
    if total_available_grasps > theoretical_sampling_count:
        improvement_factor = total_available_grasps / theoretical_sampling_count
        print(f"6. 改进潜力: 数据利用率可提升 {improvement_factor:.1f} 倍")
    
    return {
        'dataset_length': dataset_length,
        'num_grasps': num_grasps,
        'total_available_grasps': total_available_grasps,
        'theoretical_sampling_count': theoretical_sampling_count,
        'utilization_rate': utilization_rate if total_available_grasps > 0 else 0,
        'scene_object_combinations': len(views_per_scene_object),
        'unique_scenes': len(scenes),
        'unique_objects': len(objects)
    }


if __name__ == "__main__":
    try:
        results = analyze_data_utilization()
        print(f"\n测试完成！")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
