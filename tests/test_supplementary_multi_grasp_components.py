#!/usr/bin/env python3
"""
补充多抓取组件测试脚本
验证补充重构计划中的核心组件是否正确支持多抓取格式
"""

import torch
import torch.nn as nn
import sys
import os
import pytest
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.hand_model import HandModel, HandModelType
from models.diffuser_lightning import DDPMLightning
from utils.evaluate_utils import cal_pen
from omegaconf import OmegaConf


class TestSupplementaryMultiGraspComponents:
    """补充多抓取组件测试类"""
    
    def setup_method(self):
        """测试设置"""
        self.device = 'cpu'
        self.B = 4
        self.num_grasps = 8
        self.pose_dim = 25
        
        # 创建测试数据
        self.single_hand_pose = torch.randn(self.B, self.pose_dim)
        self.multi_hand_pose = torch.randn(self.B, self.num_grasps, self.pose_dim)
        self.scene_pc = torch.randn(self.B, 1000, 6)
    
    def test_hand_model_multi_grasp_support(self):
        """测试手部模型多抓取支持 - 任务A.1"""
        print("=" * 60)
        print("测试手部模型多抓取支持")
        print("=" * 60)
        
        try:
            # 初始化手部模型
            hand_model = HandModel(hand_model_type=HandModelType.LEAP, device=self.device)
            
            # 测试单抓取格式（向后兼容性）
            print("1. 测试单抓取格式...")
            result_single = hand_model(
                self.single_hand_pose,
                scene_pc=self.scene_pc,
                with_surface_points=True
            )
            
            assert 'surface_points' in result_single
            assert result_single['surface_points'].shape[0] == self.B
            print(f"✅ 单抓取格式: {result_single['surface_points'].shape}")
            
            # 测试多抓取格式
            print("2. 测试多抓取格式...")
            result_multi = hand_model(
                self.multi_hand_pose,
                scene_pc=self.scene_pc,
                with_surface_points=True
            )
            
            assert 'surface_points' in result_multi
            expected_shape = (self.B, self.num_grasps, 778, 3)  # LEAP手部模型的表面点数
            assert result_multi['surface_points'].shape == expected_shape
            print(f"✅ 多抓取格式: {result_multi['surface_points'].shape}")
            
            print("✅ 手部模型多抓取支持测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 手部模型测试失败: {e}")
            return False
    
    def test_diffusion_inference_multi_grasp(self):
        """测试扩散模型推理多抓取支持 - 任务A.3-A.4"""
        print("=" * 60)
        print("测试扩散模型推理多抓取支持")
        print("=" * 60)
        
        try:
            # 创建模拟配置
            cfg = self._create_mock_diffusion_config()
            model = DDPMLightning(cfg)
            
            # 准备测试数据
            test_data_single = {
                'norm_pose': self.single_hand_pose,
                'scene_pc': self.scene_pc,
                'positive_prompt': ['test prompt'] * self.B
            }
            
            test_data_multi = {
                'norm_pose': self.multi_hand_pose,
                'scene_pc': self.scene_pc,
                'positive_prompt': ['test prompt'] * self.B
            }
            
            # 测试单抓取推理（向后兼容性）
            print("1. 测试单抓取推理...")
            preds_single, targets_single = model.forward_infer(test_data_single, k=2)
            assert preds_single is not None
            assert targets_single is not None
            print("✅ 单抓取推理通过")
            
            # 测试多抓取推理
            print("2. 测试多抓取推理...")
            preds_multi, targets_multi = model.forward_infer(test_data_multi, k=2)
            assert preds_multi is not None
            assert targets_multi is not None
            print("✅ 多抓取推理通过")
            
            # 测试步骤推理
            print("3. 测试步骤推理...")
            results_steps = model.forward_infer_step(test_data_multi, k=2, timesteps=[1, 5, -1])
            assert len(results_steps) == 3
            print("✅ 步骤推理通过")
            
            print("✅ 扩散模型推理多抓取支持测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 扩散模型推理测试失败: {e}")
            return False
    
    def test_evaluation_utils_multi_grasp(self):
        """测试评估工具多抓取支持 - 任务A.5"""
        print("=" * 60)
        print("测试评估工具多抓取支持")
        print("=" * 60)
        
        try:
            # 创建模拟手部模型和物体网格
            hand_model = HandModel(hand_model_type=HandModelType.LEAP, device=self.device)
            
            # 创建简单的立方体网格用于测试
            vertices = torch.tensor([
                [-0.1, -0.1, -0.1], [0.1, -0.1, -0.1], [0.1, 0.1, -0.1], [-0.1, 0.1, -0.1],
                [-0.1, -0.1, 0.1], [0.1, -0.1, 0.1], [0.1, 0.1, 0.1], [-0.1, 0.1, 0.1]
            ], dtype=torch.float32)
            
            faces = torch.tensor([
                [0, 1, 2], [0, 2, 3], [4, 7, 6], [4, 6, 5],
                [0, 4, 5], [0, 5, 1], [2, 6, 7], [2, 7, 3],
                [0, 3, 7], [0, 7, 4], [1, 5, 6], [1, 6, 2]
            ], dtype=torch.long)
            
            cfg = {'penetration': {'enabled': True}}
            scale = 0.1
            
            # 测试单抓取碰撞检测（向后兼容性）
            print("1. 测试单抓取碰撞检测...")
            pen_single = cal_pen(cfg, hand_model, vertices, faces, scale, 
                               self.single_hand_pose[0:1], num_samples=100)
            assert isinstance(pen_single, (float, torch.Tensor))
            print(f"✅ 单抓取碰撞检测: {type(pen_single)}")
            
            # 测试多抓取碰撞检测
            print("2. 测试多抓取碰撞检测...")
            pen_multi = cal_pen(cfg, hand_model, vertices, faces, scale, 
                              self.multi_hand_pose[0:1], num_samples=100)
            assert isinstance(pen_multi, torch.Tensor)
            assert pen_multi.shape == (1, self.num_grasps)
            print(f"✅ 多抓取碰撞检测: {pen_multi.shape}")
            
            print("✅ 评估工具多抓取支持测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 评估工具测试失败: {e}")
            return False
    
    def test_visualization_multi_grasp(self):
        """测试可视化多抓取支持 - 任务B.1-B.2"""
        print("=" * 60)
        print("测试可视化多抓取支持")
        print("=" * 60)
        
        try:
            # 这里只测试函数接口，不实际显示
            from visualize_sceneleappro_dataset import create_hand_mesh, analyze_hand_pose_format
            
            hand_model = HandModel(hand_model_type=HandModelType.LEAP, device=self.device)
            
            # 测试单抓取可视化
            print("1. 测试单抓取可视化...")
            single_pose = torch.randn(23)  # 单个姿态
            pose_format = analyze_hand_pose_format(single_pose)
            assert pose_format in ['quaternion_pjq', 'unknown']
            print(f"✅ 单抓取格式检测: {pose_format}")
            
            # 测试多抓取可视化接口
            print("2. 测试多抓取可视化接口...")
            multi_pose = torch.randn(self.num_grasps, 23)  # 多个姿态
            
            # 测试创建手部网格（不实际渲染）
            try:
                hand_meshes = create_hand_mesh(multi_pose, hand_model, grasp_index=0)
                if hand_meshes is not None:
                    assert isinstance(hand_meshes, list)
                    print(f"✅ 多抓取网格创建: {len(hand_meshes)} 个网格")
                else:
                    print("⚠️ 网格创建返回None（可能是依赖问题）")
            except ImportError as e:
                print(f"⚠️ 可视化依赖缺失: {e}")
            
            print("✅ 可视化多抓取支持测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 可视化测试失败: {e}")
            return False
    
    def _create_mock_diffusion_config(self):
        """创建模拟扩散模型配置"""
        cfg = OmegaConf.create({
            'name': 'GraspDiffuser',
            'steps': 10,  # 减少步数用于测试
            'pred_x0': True,
            'use_cfg': False,
            'guidance_scale': 7.5,
            'use_negative_guidance': False,
            'negative_guidance_scale': 1.0,
            'rot_type': 'r6d',
            'mode': 'camera_centric',
            'batch_size': self.B,
            'rand_t_type': 'all',
            'loss_weights': {
                'diffusion_loss': 1.0,
                'neg_loss': 0.1
            },
            'optimizer': {
                'name': 'AdamW',
                'lr': 1e-4,
                'weight_decay': 1e-2
            },
            'scheduler': {
                'name': 'cosine',
                'warmup_steps': 100
            },
            'eps_model': {
                'rot_type': 'r6d',
                'd_model': 512,
                'time_embed_mult': 2,
                'nblocks': 2,
                'resblock_dropout': 0.0,
                'transformer_num_heads': 8,
                'transformer_dim_head': 64,
                'transformer_dropout': 0.1,
                'transformer_depth': 1,
                'transformer_mult_ff': 2,
                'context_dim': 512,
                'backbone': {
                    'name': 'pointnet2',
                    'use_pooling': False
                },
                'use_position_embedding': False,
                'use_text_condition': True,
                'text_dropout_prob': 0.1
            },
            'criterion': {
                'name': 'GraspLossPose',
                'loss_weights': {
                    'diffusion_loss': 1.0
                }
            }
        })
        return cfg


def run_all_tests():
    """运行所有补充组件测试"""
    print("🚀 开始补充多抓取组件测试")
    print("=" * 80)
    
    tester = TestSupplementaryMultiGraspComponents()
    tester.setup_method()
    
    tests = [
        ("手部模型多抓取支持", tester.test_hand_model_multi_grasp_support),
        ("扩散模型推理多抓取支持", tester.test_diffusion_inference_multi_grasp),
        ("评估工具多抓取支持", tester.test_evaluation_utils_multi_grasp),
        ("可视化多抓取支持", tester.test_visualization_multi_grasp),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有补充组件测试通过！系统已准备好进行多抓取架构升级。")
    else:
        print("⚠️ 部分测试失败，请检查相关组件的实现。")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
