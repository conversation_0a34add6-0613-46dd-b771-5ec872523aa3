#!/usr/bin/env python3
"""
测试配置兼容性 - 确保修改不影响现有的 sceneleappro_cached.py
"""

import os
import sys
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cached_dataset_config():
    """测试 CachedDatasetConfig 类的基本功能"""
    logger.info("测试 CachedDatasetConfig 类...")
    
    try:
        from datasets.utils.dataset_config import CachedDatasetConfig
        
        # 测试标准参数（sceneleappro_cached.py 使用的参数）
        config = CachedDatasetConfig(
            root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15",
            succ_grasp_dir="/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
            obj_root_dir="/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
            mode="camera_centric",
            max_grasps_per_object=10,
            mesh_scale=0.1,
            num_neg_prompts=4,
            enable_cropping=True,
            max_points=10000,
            cache_version="v1.0_test",
            cache_mode="train"
        )
        
        logger.info("✅ CachedDatasetConfig 创建成功")
        logger.info(f"   - mode: {config.mode}")
        logger.info(f"   - max_grasps_per_object: {config.max_grasps_per_object}")
        logger.info(f"   - cache_version: {config.cache_version}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ CachedDatasetConfig 测试失败: {e}")
        return False

def test_sceneleappro_cached_import():
    """测试 sceneleappro_cached.py 的导入"""
    logger.info("测试 sceneleappro_cached.py 导入...")
    
    try:
        from datasets.sceneleappro_cached import SceneLeapProDatasetCached, ForMatchSceneLeapProDatasetCached
        logger.info("✅ sceneleappro_cached.py 导入成功")
        return True
    except Exception as e:
        logger.error(f"✗ sceneleappro_cached.py 导入失败: {e}")
        return False

def test_sceneleapplus_cached_import():
    """测试 sceneleapplus_cached.py 的导入"""
    logger.info("测试 sceneleapplus_cached.py 导入...")
    
    try:
        from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
        logger.info("✅ sceneleapplus_cached.py 导入成功")
        return True
    except Exception as e:
        logger.error(f"✗ sceneleapplus_cached.py 导入失败: {e}")
        return False

def test_sceneleappro_cached_creation():
    """测试 SceneLeapProDatasetCached 的创建（不需要真实数据）"""
    logger.info("测试 SceneLeapProDatasetCached 创建...")
    
    try:
        from datasets.sceneleappro_cached import SceneLeapProDatasetCached
        
        # 这里只测试参数传递，不实际创建数据集（因为可能没有数据）
        # 我们可以检查类的 __init__ 方法是否接受正确的参数
        import inspect
        
        sig = inspect.signature(SceneLeapProDatasetCached.__init__)
        params = list(sig.parameters.keys())
        
        expected_params = [
            'self', 'root_dir', 'succ_grasp_dir', 'obj_root_dir', 'mode',
            'max_grasps_per_object', 'mesh_scale', 'num_neg_prompts',
            'enable_cropping', 'max_points', 'cache_version'
        ]
        
        for param in expected_params:
            if param in params:
                logger.info(f"   ✅ 参数 {param} 存在")
            else:
                logger.warning(f"   ⚠️ 参数 {param} 不存在")
        
        # 检查是否有意外的额外参数
        unexpected_params = [p for p in params if p not in expected_params]
        if unexpected_params:
            logger.info(f"   额外参数: {unexpected_params}")
        
        logger.info("✅ SceneLeapProDatasetCached 参数检查通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ SceneLeapProDatasetCached 创建测试失败: {e}")
        return False

def test_sceneleapplus_cached_creation():
    """测试 SceneLeapPlusDatasetCached 的创建（不需要真实数据）"""
    logger.info("测试 SceneLeapPlusDatasetCached 创建...")
    
    try:
        from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
        
        # 检查类的 __init__ 方法参数
        import inspect
        
        sig = inspect.signature(SceneLeapPlusDatasetCached.__init__)
        params = list(sig.parameters.keys())
        
        expected_params = [
            'self', 'root_dir', 'succ_grasp_dir', 'obj_root_dir', 'num_grasps',
            'mode', 'max_grasps_per_object', 'mesh_scale', 'num_neg_prompts',
            'enable_cropping', 'max_points', 'grasp_sampling_strategy',
            'cache_version', 'cache_mode'
        ]
        
        for param in expected_params:
            if param in params:
                logger.info(f"   ✅ 参数 {param} 存在")
            else:
                logger.warning(f"   ⚠️ 参数 {param} 不存在")
        
        # 检查是否有意外的额外参数
        unexpected_params = [p for p in params if p not in expected_params]
        if unexpected_params:
            logger.info(f"   额外参数: {unexpected_params}")
        
        logger.info("✅ SceneLeapPlusDatasetCached 参数检查通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ SceneLeapPlusDatasetCached 创建测试失败: {e}")
        return False

def test_config_parameter_compatibility():
    """测试配置参数兼容性"""
    logger.info("测试配置参数兼容性...")
    
    try:
        from datasets.utils.dataset_config import CachedDatasetConfig
        import inspect
        
        # 检查 CachedDatasetConfig 的参数
        sig = inspect.signature(CachedDatasetConfig.__init__)
        params = list(sig.parameters.keys())
        
        logger.info(f"CachedDatasetConfig 参数: {params}")
        
        # 确保不包含 SceneLeapPlus 特有的参数
        sceneleapplus_params = ['num_grasps', 'grasp_sampling_strategy']
        for param in sceneleapplus_params:
            if param in params:
                logger.error(f"   ✗ CachedDatasetConfig 不应该包含参数 {param}")
                return False
            else:
                logger.info(f"   ✅ CachedDatasetConfig 正确地不包含参数 {param}")
        
        logger.info("✅ 配置参数兼容性检查通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置参数兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("配置兼容性测试")
    logger.info("=" * 60)
    
    tests = [
        ("CachedDatasetConfig 基本功能", test_cached_dataset_config),
        ("sceneleappro_cached.py 导入", test_sceneleappro_cached_import),
        ("sceneleapplus_cached.py 导入", test_sceneleapplus_cached_import),
        ("SceneLeapProDatasetCached 创建", test_sceneleappro_cached_creation),
        ("SceneLeapPlusDatasetCached 创建", test_sceneleapplus_cached_creation),
        ("配置参数兼容性", test_config_parameter_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    logger.info(f"\n{'='*60}")
    logger.info("测试结果总结")
    logger.info(f"{'='*60}")
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        logger.info("🎉 所有测试通过！修改不影响现有功能。")
    else:
        logger.warning("⚠️ 有测试失败，需要检查兼容性问题。")

if __name__ == "__main__":
    main()
