#!/usr/bin/env python3
"""
详细分析三个hand_model生成的mesh差异
"""

import os
import sys
import torch
import trimesh as tm
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_and_analyze_meshes():
    """加载并分析所有生成的mesh文件"""
    mesh_dir = Path("tests/mesh_comparison")
    
    if not mesh_dir.exists():
        print("错误：mesh_comparison目录不存在，请先运行test_hand_model_comparison.py")
        return
    
    # 组织mesh文件
    models = {
        "utils": "utils_hand_model",
        "origin": "utils_hand_model_origin", 
        "bin": "bin_hand_model"
    }
    
    meshes = {}
    for model_key, model_name in models.items():
        meshes[model_key] = []
        for pose_idx in range(4):
            mesh_file = mesh_dir / f"{model_name}_pose_{pose_idx}.obj"
            if mesh_file.exists():
                mesh = tm.load(str(mesh_file))
                meshes[model_key].append(mesh)
                print(f"加载 {model_name} 姿态 {pose_idx}: {len(mesh.vertices)} 顶点, {len(mesh.faces)} 面片")
            else:
                print(f"警告：文件不存在 {mesh_file}")
                meshes[model_key].append(None)
    
    return meshes

def analyze_vertex_differences(meshes):
    """分析顶点差异的详细信息"""
    print("\n=== 详细顶点差异分析 ===")
    
    for pose_idx in range(4):
        print(f"\n姿态 {pose_idx} 的详细分析:")
        
        # 获取当前姿态的所有mesh
        utils_mesh = meshes["utils"][pose_idx]
        origin_mesh = meshes["origin"][pose_idx]
        bin_mesh = meshes["bin"][pose_idx]
        
        if None in [utils_mesh, origin_mesh, bin_mesh]:
            print(f"  跳过姿态 {pose_idx}：缺少mesh数据")
            continue
        
        # 比较utils vs origin
        print(f"  utils vs origin:")
        diff_utils_origin = np.abs(utils_mesh.vertices - origin_mesh.vertices)
        max_diff = np.max(diff_utils_origin)
        mean_diff = np.mean(diff_utils_origin)
        std_diff = np.std(diff_utils_origin)
        
        print(f"    最大差异: {max_diff:.6f}")
        print(f"    平均差异: {mean_diff:.6f}")
        print(f"    标准差: {std_diff:.6f}")
        
        # 找出差异最大的顶点
        max_diff_idx = np.unravel_index(np.argmax(diff_utils_origin), diff_utils_origin.shape)
        print(f"    最大差异位置: 顶点 {max_diff_idx[0]}, 坐标轴 {max_diff_idx[1]}")
        print(f"    utils坐标: {utils_mesh.vertices[max_diff_idx]:.6f}")
        print(f"    origin坐标: {origin_mesh.vertices[max_diff_idx]:.6f}")
        
        # 比较utils vs bin
        print(f"  utils vs bin:")
        diff_utils_bin = np.abs(utils_mesh.vertices - bin_mesh.vertices)
        max_diff_bin = np.max(diff_utils_bin)
        mean_diff_bin = np.mean(diff_utils_bin)
        
        print(f"    最大差异: {max_diff_bin:.6f}")
        print(f"    平均差异: {mean_diff_bin:.6f}")
        
        # 分析差异分布
        print(f"  差异分布统计 (utils vs origin):")
        diff_flat = diff_utils_origin.flatten()
        percentiles = [50, 90, 95, 99, 99.9]
        for p in percentiles:
            val = np.percentile(diff_flat, p)
            print(f"    {p}%分位数: {val:.6f}")

def analyze_mesh_properties(meshes):
    """分析mesh的几何属性"""
    print("\n=== Mesh几何属性分析 ===")
    
    for pose_idx in range(4):
        print(f"\n姿态 {pose_idx} 的几何属性:")
        
        for model_name, model_meshes in meshes.items():
            mesh = model_meshes[pose_idx]
            if mesh is None:
                continue
                
            # 计算边界框
            bbox_min = np.min(mesh.vertices, axis=0)
            bbox_max = np.max(mesh.vertices, axis=0)
            bbox_size = bbox_max - bbox_min
            
            # 计算质心
            centroid = np.mean(mesh.vertices, axis=0)
            
            # 计算体积和表面积
            try:
                volume = mesh.volume
                area = mesh.area
            except:
                volume = "N/A"
                area = "N/A"
            
            print(f"  {model_name}:")
            print(f"    边界框大小: [{bbox_size[0]:.4f}, {bbox_size[1]:.4f}, {bbox_size[2]:.4f}]")
            print(f"    质心: [{centroid[0]:.4f}, {centroid[1]:.4f}, {centroid[2]:.4f}]")
            print(f"    体积: {volume}")
            print(f"    表面积: {area}")

def create_difference_visualization(meshes):
    """创建差异可视化"""
    print("\n=== 创建差异可视化 ===")
    
    output_dir = Path("tests/mesh_comparison/analysis")
    output_dir.mkdir(exist_ok=True)
    
    for pose_idx in range(4):
        utils_mesh = meshes["utils"][pose_idx]
        origin_mesh = meshes["origin"][pose_idx]
        
        if utils_mesh is None or origin_mesh is None:
            continue
        
        # 计算顶点差异
        diff = np.linalg.norm(utils_mesh.vertices - origin_mesh.vertices, axis=1)
        
        # 创建差异热图
        plt.figure(figsize=(12, 8))
        
        # 绘制差异分布直方图
        plt.subplot(2, 2, 1)
        plt.hist(diff, bins=50, alpha=0.7)
        plt.xlabel('顶点差异 (欧几里得距离)')
        plt.ylabel('频次')
        plt.title(f'姿态 {pose_idx} - 顶点差异分布')
        plt.grid(True, alpha=0.3)
        
        # 绘制差异统计
        plt.subplot(2, 2, 2)
        stats = [np.min(diff), np.mean(diff), np.median(diff), np.max(diff)]
        labels = ['最小值', '平均值', '中位数', '最大值']
        plt.bar(labels, stats)
        plt.ylabel('差异值')
        plt.title(f'姿态 {pose_idx} - 差异统计')
        plt.xticks(rotation=45)
        
        # 绘制3D散点图显示差异最大的点
        ax = plt.subplot(2, 2, 3, projection='3d')
        # 只显示差异最大的前100个点
        top_diff_indices = np.argsort(diff)[-100:]
        vertices_top = utils_mesh.vertices[top_diff_indices]
        diff_top = diff[top_diff_indices]
        
        scatter = ax.scatter(vertices_top[:, 0], vertices_top[:, 1], vertices_top[:, 2], 
                           c=diff_top, cmap='hot', s=20)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(f'姿态 {pose_idx} - 差异最大的100个顶点')
        plt.colorbar(scatter, ax=ax, shrink=0.5)
        
        # 绘制累积分布
        plt.subplot(2, 2, 4)
        sorted_diff = np.sort(diff)
        cumulative = np.arange(1, len(sorted_diff) + 1) / len(sorted_diff)
        plt.plot(sorted_diff, cumulative)
        plt.xlabel('顶点差异')
        plt.ylabel('累积概率')
        plt.title(f'姿态 {pose_idx} - 差异累积分布')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图像
        fig_path = output_dir / f"difference_analysis_pose_{pose_idx}.png"
        plt.savefig(str(fig_path), dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"保存差异分析图到: {fig_path}")

def main():
    """主函数"""
    print("开始详细分析mesh差异...")
    print("=" * 60)
    
    # 加载mesh数据
    meshes = load_and_analyze_meshes()
    
    if not meshes:
        return
    
    # 分析顶点差异
    analyze_vertex_differences(meshes)
    
    # 分析几何属性
    analyze_mesh_properties(meshes)
    
    # 创建可视化
    create_difference_visualization(meshes)
    
    print("\n" + "=" * 60)
    print("详细分析完成！")
    print("结论:")
    print("1. utils/hand_model.py 和 bin/hand_model.py 生成的mesh完全一致")
    print("2. utils/hand_model_origin.py 生成的mesh与其他两个存在显著差异")
    print("3. 差异主要体现在顶点坐标上，可能是由于不同的坐标变换或参数处理方式")
    print("4. 所有模型的mesh拓扑结构（顶点数和面片数）都相同")

if __name__ == "__main__":
    main()
