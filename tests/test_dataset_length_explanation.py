#!/usr/bin/env python3
"""
解释数据集长度为什么不变的测试
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

def explain_dataset_length():
    """解释为什么不同 num_grasps 的数据集长度相同"""
    
    print("=" * 80)
    print("解释 SceneLeapPlusDataset 数据集长度的含义")
    print("=" * 80)
    
    # 创建一个数据集实例
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric",
        "max_grasps_per_object": 1024,
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus_explain",
        "cache_mode": "train"
    }
    
    dataset = SceneLeapPlusDatasetCached(**config)
    
    print(f"数据集长度: {len(dataset)}")
    print(f"数据集配置的 num_grasps: {dataset.num_grasps}")
    
    print("\n数据集长度的含义:")
    print("- 数据集长度 = 场景数 × 每个场景的物体数 × 每个物体的视角数")
    print("- 每个数据项代表: 一个场景中的一个物体在一个特定视角下的数据")
    print("- num_grasps 控制的是每个数据项返回多少个抓取姿态")
    
    # 检查前几个数据项的结构
    print(f"\n前5个数据项的结构:")
    for i in range(min(5, len(dataset))):
        item_data = dataset.data[i]  # 直接访问内部数据结构
        print(f"  数据项 {i}: 场景={item_data['scene_id']}, "
              f"物体={item_data['object_code']}, "
              f"视角={item_data['depth_view_index']}")
    
    # 测试不同索引的样本
    print(f"\n测试不同索引的样本形状:")
    for i in [0, 1, 2]:
        sample = dataset[i]
        if 'error' not in sample:
            print(f"  样本 {i}: hand_pose={sample['hand_model_pose'].shape}, "
                  f"se3={sample['se3'].shape}")
        else:
            print(f"  样本 {i}: 错误 - {sample['error']}")
    
    print(f"\n关键理解:")
    print(f"- 无论 num_grasps 设置为多少，数据集都有 {len(dataset)} 个数据项")
    print(f"- 每个数据项都会返回 {dataset.num_grasps} 个抓取姿态")
    print(f"- 如果某个物体的可用抓取少于 {dataset.num_grasps}，会使用采样策略填充")
    print(f"- 如果某个物体的可用抓取多于 {dataset.num_grasps}，会使用采样策略选择")
    
    # 清理
    if hasattr(dataset, '_cleanup'):
        dataset._cleanup()

if __name__ == "__main__":
    explain_dataset_length()
