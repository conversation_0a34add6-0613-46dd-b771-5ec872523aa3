#!/usr/bin/env python3
"""
测试脚本：验证多抓取推理模式重构
测试DDPMLightning的validation_step、test_step、forward_infer等方法的多抓取支持
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.diffuser_lightning import DDPMLightning
from utils.hand_helper import process_hand_pose_test
from omegaconf import OmegaConf


def create_test_config():
    """创建测试配置"""
    config = OmegaConf.create({
        'decoder': {
            'name': 'unet',
            'rot_type': 'r6d',
            'd_model': 512,
            'time_embed_mult': 2,
            'nblocks': 4,
            'resblock_dropout': 0.0,
            'transformer_num_heads': 8,
            'transformer_dim_head': 64,
            'transformer_dropout': 0.1,
            'transformer_depth': 1,
            'transformer_mult_ff': 2,
            'context_dim': 512,
            'backbone': {
                'name': 'pointnet2',
                'use_pooling': False,
                'layer1': {
                    'npoint': 2048,
                    'radius_list': [0.04],
                    'nsample_list': [64],
                    'mlp_list': [3, 64, 64, 128]
                },
                'layer2': {
                    'npoint': 1024,
                    'radius_list': [0.1],
                    'nsample_list': [32],
                    'mlp_list': [128, 128, 128, 256]
                },
                'layer3': {
                    'npoint': 512,
                    'radius_list': [0.2],
                    'nsample_list': [32],
                    'mlp_list': [256, 128, 128, 256]
                },
                'layer4': {
                    'npoint': 128,
                    'radius_list': [0.3],
                    'nsample_list': [16],
                    'mlp_list': [256, 512, 512]
                },
                'use_xyz': True,
                'normalize_xyz': True
            },
            'use_position_embedding': False,
            'use_text_condition': True,
            'text_dropout_prob': 0.1,
            'time_embedding_dim': 64,
            'n_points': 128,
            'device': 'cpu'
        },
        'criterion': {
            'hand_model': {
                'n_surface_points': 1024,
                'rot_type': 'r6d',
                'device': 'cpu'
            },
            'loss_weights': {
                'hand_chamfer': 0.0,
                'translation': 10.0,
                'rotation': 10.0,
                'qpos': 1.0,
                'neg_loss': 5.0
            },
            'cost_weights': {
                'hand_mesh': 0.0,
                'qpos': 1.0,
                'translation': 2.0,
                'rotation': 2.0
            },
            'device': 'cpu',
            'rot_type': 'r6d',
            'mode': 'camera_centric',
            'q1': {
                'lambda_torque': 10,
                'm': 8,
                'mu': 1,
                'nms': True,
                'thres_contact': 0.01,
                'thres_pen': 0.005,
                'thres_tpen': 0.01,
                'rot_type': 'r6d'
            }
        },
        'rot_type': 'r6d',
        'batch_size': 2,
        'print_freq': 10,
        'steps': 100,
        'schedule_cfg': {
            'beta': [0.0001, 0.01],
            'beta_schedule': 'linear',
            's': 0.008
        },
        'optimizer': {
            'name': 'adam',
            'lr': 0.0001,
            'weight_decay': 0.0001
        },
        'scheduler': {
            'name': 'cosine',
            't_max': 1000,
            'min_lr': 1e-5
        },
        'rand_t_type': 'half',
        'pred_x0': True,
        'mode': 'camera_centric'
    })
    return config


def create_multi_grasp_batch(B=2, num_grasps=4, max_points=1000):
    """创建多抓取测试批次"""
    pose_dim = 25  # r6d格式: 3(trans) + 16(joints) + 6(rot)
    
    batch = {
        'scene_pc': torch.randn(B, max_points, 6),  # [B, N, 6] xyz+rgb
        'hand_model_pose': torch.randn(B, num_grasps, 23),  # [B, num_grasps, 23]
        'se3': torch.eye(4).unsqueeze(0).unsqueeze(0).repeat(B, num_grasps, 1, 1),  # [B, num_grasps, 4, 4]
        'positive_prompt': ['test object'] * B,
        'negative_prompts': [['bad grasp'] * 2] * B,
        'object_mask': torch.ones(B, max_points, dtype=torch.bool)
    }
    
    # 设置部分抓取为无效（全零）
    batch['hand_model_pose'][0, 2:] = 0  # 第一个batch的后两个抓取无效
    batch['hand_model_pose'][1, 3:] = 0  # 第二个batch的最后一个抓取无效
    
    return batch


def create_single_grasp_batch(B=2, max_points=1000):
    """创建单抓取测试批次（向后兼容性测试）"""
    batch = {
        'scene_pc': torch.randn(B, max_points, 6),
        'hand_model_pose': torch.randn(B, 23),  # [B, 23]
        'se3': torch.eye(4).unsqueeze(0).repeat(B, 1, 1),  # [B, 4, 4]
        'positive_prompt': ['test object'] * B,
        'negative_prompts': [['bad grasp'] * 2] * B,
        'object_mask': torch.ones(B, max_points, dtype=torch.bool)
    }
    return batch


def test_multi_grasp_inference_modes():
    """测试多抓取推理模式"""
    print("🧪 测试多抓取推理模式...")
    
    # 1. 模型初始化
    cfg = create_test_config()
    model = DDPMLightning(cfg)
    model.eval()
    
    # 2. 测试validation_step
    print("\n📊 测试validation_step...")
    multi_batch = create_multi_grasp_batch(B=2, num_grasps=4)
    
    try:
        with torch.no_grad():
            val_result = model.validation_step(multi_batch, 0)
        
        assert 'loss' in val_result
        assert 'loss_dict' in val_result
        assert val_result['loss'].item() >= 0
        print(f"✅ 多抓取验证损失: {val_result['loss'].item():.4f}")
        
    except Exception as e:
        print(f"❌ validation_step多抓取测试失败: {e}")
        return False
    
    # 3. 测试test_step
    print("\n🔬 测试test_step...")
    try:
        with torch.no_grad():
            test_result = model.test_step(multi_batch, 0)
        
        assert test_result is not None
        print("✅ test_step多抓取测试通过")
        
    except Exception as e:
        print(f"❌ test_step多抓取测试失败: {e}")
        return False
    
    # 4. 测试forward_infer
    print("\n🔍 测试forward_infer...")
    try:
        with torch.no_grad():
            preds_hand, targets_hand = model.forward_infer(multi_batch, k=2)
        
        assert preds_hand is not None
        assert targets_hand is not None
        print("✅ forward_infer多抓取测试通过")
        
    except Exception as e:
        print(f"❌ forward_infer多抓取测试失败: {e}")
        return False
    
    # 5. 测试向后兼容性
    print("\n🔄 测试向后兼容性...")
    single_batch = create_single_grasp_batch(B=2)
    
    try:
        with torch.no_grad():
            single_val_result = model.validation_step(single_batch, 0)
        
        assert 'loss' in single_val_result
        print(f"✅ 单抓取兼容性验证损失: {single_val_result['loss'].item():.4f}")
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False
    
    print("\n🎉 多抓取推理模式测试全部通过!")
    return True


def test_inference_output_dimensions():
    """测试推理输出维度正确性"""
    print("\n📏 测试推理输出维度...")
    
    cfg = create_test_config()
    model = DDPMLightning(cfg)
    model.eval()
    
    # 多抓取测试
    multi_data = create_multi_grasp_batch(B=2, num_grasps=4)
    
    try:
        with torch.no_grad():
            # 测试sample方法输出
            pred_x0 = model.sample(multi_data, k=3)
            print(f"sample输出维度: {pred_x0.shape}")
            
            # 测试forward_get_pose
            outputs, targets = model.forward_get_pose(multi_data, k=2)
            print("✅ forward_get_pose测试通过")
            
            # 测试forward_get_pose_matched
            matched_pred, matched_targets, outputs, targets = model.forward_get_pose_matched(multi_data, k=2)
            print("✅ forward_get_pose_matched测试通过")
            
    except Exception as e:
        print(f"❌ 推理输出维度测试失败: {e}")
        return False
    
    print("✅ 推理输出维度测试通过")
    return True


def test_data_flow_consistency():
    """测试数据流一致性"""
    print("\n🔄 测试数据流一致性...")
    
    cfg = create_test_config()
    model = DDPMLightning(cfg)
    model.eval()
    
    # 创建测试数据
    batch = create_multi_grasp_batch(B=2, num_grasps=4)
    
    try:
        # 1. 数据预处理
        processed_batch = process_hand_pose_test(batch, 'r6d', 'camera_centric')
        print(f"预处理后norm_pose形状: {processed_batch['norm_pose'].shape}")
        
        # 2. 模型推理
        with torch.no_grad():
            pred_x0 = model.sample(processed_batch)
        print(f"推理输出形状: {pred_x0.shape}")
        
        # 3. 验证维度一致性
        if pred_x0.dim() == 5:
            pred_x0_final = pred_x0[:,0,-1]
            print(f"最终预测形状: {pred_x0_final.shape}")
            
            if pred_x0_final.dim() == 3:
                B, num_grasps, pose_dim = pred_x0_final.shape
                assert B == 2
                assert num_grasps == 4
                print(f"✅ 多抓取维度一致: [B={B}, num_grasps={num_grasps}, pose_dim={pose_dim}]")
            else:
                print(f"✅ 单抓取维度: {pred_x0_final.shape}")
        
    except Exception as e:
        print(f"❌ 数据流一致性测试失败: {e}")
        return False
    
    print("✅ 数据流一致性验证通过")
    return True


def main():
    """主测试函数"""
    print("🚀 开始多抓取推理模式验证测试")
    print("=" * 60)
    
    success = True
    
    # 测试1: 多抓取推理模式
    if not test_multi_grasp_inference_modes():
        success = False
    
    # 测试2: 推理输出维度
    if not test_inference_output_dimensions():
        success = False
    
    # 测试3: 数据流一致性
    if not test_data_flow_consistency():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过! 多抓取推理模式重构成功")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return success


if __name__ == "__main__":
    main()
