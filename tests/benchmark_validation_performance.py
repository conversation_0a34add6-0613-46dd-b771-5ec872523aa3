#!/usr/bin/env python3
"""
性能基准测试：对比单抓取vs多抓取模式的性能差异
测试验证时间、推理时间和内存使用
重点确保r6d和quat旋转类型能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
import time
import psutil
from typing import Dict, Any, List
import gc

# 导入相关模块
from utils.hand_helper import process_hand_pose_test


def get_memory_usage():
    """获取当前内存使用情况（MB）"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


def create_multi_grasp_batch(B=8, num_grasps=4, max_points=1024):
    """创建多抓取测试批次"""
    pose_dim = 25
    
    batch = {
        'norm_pose': torch.randn(B, num_grasps, pose_dim),
        'hand_model_pose': torch.randn(B, num_grasps, pose_dim),
        'scene_pc': torch.randn(B, max_points, 6),
        'text_condition': ['grasp object'] * B,
        'se3': torch.eye(4).unsqueeze(0).unsqueeze(0).repeat(B, num_grasps, 1, 1),
        'valid_mask': torch.ones(B, num_grasps, dtype=torch.bool),
    }
    
    # 设置部分目标为无效
    if num_grasps > 2:
        batch['norm_pose'][:, 2:] = 0
        batch['hand_model_pose'][:, 2:] = 0
        batch['valid_mask'][:, 2:] = False
    
    return batch


def create_single_grasp_batch(B=8, max_points=1024):
    """创建单抓取测试批次"""
    pose_dim = 25

    batch = {
        'norm_pose': torch.randn(B, pose_dim),
        'hand_model_pose': torch.randn(B, pose_dim),
        'scene_pc': torch.randn(B, max_points, 6),
        'text_condition': ['grasp object'] * B,
        'se3': torch.eye(4).unsqueeze(0).repeat(B, 1, 1),
    }

    return batch


def test_rotation_types():
    """测试支持的旋转类型"""
    print("🔄 测试旋转类型支持...")

    # 重点测试r6d和quat，其他类型预留
    supported_rot_types = ['r6d', 'quat']
    future_rot_types = ['euler', 'axis_angle', 'matrix']

    batch = create_multi_grasp_batch(B=2, num_grasps=4)
    results = {}

    # 测试当前支持的旋转类型
    for rot_type in supported_rot_types:
        print(f"  测试 {rot_type} 旋转类型...")
        try:
            start_time = time.time()
            processed = process_hand_pose_test(batch, rot_type, 'camera_centric')
            end_time = time.time()

            results[rot_type] = {
                'status': 'supported',
                'time': end_time - start_time,
                'output_shape': processed['norm_pose'].shape
            }
            print(f"    ✅ {rot_type} 支持 - 处理时间: {(end_time - start_time)*1000:.2f}ms")

        except Exception as e:
            results[rot_type] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"    ❌ {rot_type} 错误: {e}")

    # 预留未来支持的旋转类型
    for rot_type in future_rot_types:
        results[rot_type] = {
            'status': 'future_support',
            'note': '预留给未来实现'
        }
        print(f"  📋 {rot_type} - 预留给未来实现")

    return results


def benchmark_data_preprocessing():
    """基准测试数据预处理性能"""
    print("📊 基准测试数据预处理性能...")

    batch_sizes = [4, 8, 16]
    num_grasps_list = [2, 4, 8]
    num_iterations = 5  # 减少迭代次数以加快测试
    rot_type = 'r6d'  # 使用支持的旋转类型

    results = {}

    for batch_size in batch_sizes:
        results[batch_size] = {}

        # 测试单抓取预处理
        print(f"  测试单抓取预处理 (batch_size={batch_size})...")
        single_batch = create_single_grasp_batch(B=batch_size)

        # 预热
        for _ in range(2):
            _ = process_hand_pose_test(single_batch, rot_type, 'camera_centric')

        # 正式测试
        start_time = time.time()
        start_memory = get_memory_usage()

        for _ in range(num_iterations):
            processed = process_hand_pose_test(single_batch, rot_type, 'camera_centric')

        end_time = time.time()
        end_memory = get_memory_usage()

        single_time = (end_time - start_time) / num_iterations
        single_memory = max(0, end_memory - start_memory)  # 确保非负

        results[batch_size]['single'] = {
            'time': single_time,
            'memory': single_memory,
            'output_shape': processed['norm_pose'].shape
        }
        
        # 测试多抓取预处理
        for num_grasps in num_grasps_list:
            print(f"  测试多抓取预处理 (batch_size={batch_size}, num_grasps={num_grasps})...")
            multi_batch = create_multi_grasp_batch(B=batch_size, num_grasps=num_grasps)
            
            # 预热
            for _ in range(2):
                _ = process_hand_pose_test(multi_batch, rot_type, 'camera_centric')

            # 正式测试
            gc.collect()  # 清理内存
            start_time = time.time()
            start_memory = get_memory_usage()

            for _ in range(num_iterations):
                processed = process_hand_pose_test(multi_batch, rot_type, 'camera_centric')

            end_time = time.time()
            end_memory = get_memory_usage()

            multi_time = (end_time - start_time) / num_iterations
            multi_memory = max(0, end_memory - start_memory)  # 确保非负
            
            results[batch_size][f'multi_{num_grasps}'] = {
                'time': multi_time,
                'memory': multi_memory,
                'output_shape': processed['norm_pose'].shape
            }
    
    return results


def benchmark_tensor_operations():
    """基准测试张量操作性能"""
    print("📊 基准测试张量操作性能...")
    
    batch_size = 16
    num_grasps = 4
    pose_dim = 25
    num_iterations = 100  # 减少迭代次数
    
    results = {}
    
    # 测试单抓取张量操作
    print("  测试单抓取张量操作...")
    single_tensor = torch.randn(batch_size, pose_dim)
    
    start_time = time.time()
    for _ in range(num_iterations):
        # 模拟典型的张量操作
        result = single_tensor * 2.0
        result = result + 1.0
        result = torch.relu(result)
        result = result.mean(dim=-1)
    end_time = time.time()
    
    results['single_ops'] = (end_time - start_time) / num_iterations
    
    # 测试多抓取张量操作
    print("  测试多抓取张量操作...")
    multi_tensor = torch.randn(batch_size, num_grasps, pose_dim)
    
    start_time = time.time()
    for _ in range(num_iterations):
        # 模拟典型的张量操作
        result = multi_tensor * 2.0
        result = result + 1.0
        result = torch.relu(result)
        result = result.mean(dim=-1)
    end_time = time.time()
    
    results['multi_ops'] = (end_time - start_time) / num_iterations
    
    # 测试维度变换操作
    print("  测试维度变换操作...")
    
    # 单抓取 -> 多抓取格式转换
    start_time = time.time()
    for _ in range(num_iterations):
        expanded = single_tensor.unsqueeze(1).repeat(1, num_grasps, 1)
    end_time = time.time()
    results['single_to_multi'] = (end_time - start_time) / num_iterations
    
    # 多抓取 -> 单抓取格式转换
    start_time = time.time()
    for _ in range(num_iterations):
        flattened = multi_tensor.view(batch_size * num_grasps, pose_dim)
    end_time = time.time()
    results['multi_to_flat'] = (end_time - start_time) / num_iterations
    
    return results


def benchmark_memory_scaling():
    """基准测试内存扩展性"""
    print("📊 基准测试内存扩展性...")
    
    batch_sizes = [4, 8, 16, 32, 64]
    num_grasps_list = [1, 2, 4, 8, 16]
    
    results = {}
    
    for batch_size in batch_sizes:
        results[batch_size] = {}
        
        for num_grasps in num_grasps_list:
            gc.collect()  # 清理内存
            start_memory = get_memory_usage()
            
            if num_grasps == 1:
                # 单抓取模式
                batch = create_single_grasp_batch(B=batch_size)
            else:
                # 多抓取模式
                batch = create_multi_grasp_batch(B=batch_size, num_grasps=num_grasps)
            
            # 处理数据
            processed = process_hand_pose_test(batch, 'r6d', 'camera_centric')
            
            end_memory = get_memory_usage()
            memory_usage = end_memory - start_memory
            
            results[batch_size][num_grasps] = {
                'memory': memory_usage,
                'input_shape': batch['norm_pose'].shape if hasattr(batch['norm_pose'], 'shape') else 'list',
                'output_shape': processed['norm_pose'].shape
            }
            
            # 清理
            del batch, processed
            gc.collect()
    
    return results


def analyze_memory_efficiency(preprocessing_results):
    """分析内存使用效率"""
    memory_analysis = {
        'peak_memory': 0,
        'avg_memory_growth': 0,
        'efficiency_score': 0,
        'recommendations': []
    }

    total_memory = 0
    count = 0

    for batch_size, modes in preprocessing_results.items():
        for mode, stats in modes.items():
            memory_mb = stats['memory']
            total_memory += memory_mb
            count += 1

            if memory_mb > memory_analysis['peak_memory']:
                memory_analysis['peak_memory'] = memory_mb

    if count > 0:
        memory_analysis['avg_memory_growth'] = total_memory / count

        # 计算效率评分 (基于内存使用的合理性)
        if memory_analysis['avg_memory_growth'] < 50:  # < 50MB
            memory_analysis['efficiency_score'] = 5.0
        elif memory_analysis['avg_memory_growth'] < 200:  # < 200MB
            memory_analysis['efficiency_score'] = 4.0
        elif memory_analysis['avg_memory_growth'] < 500:  # < 500MB
            memory_analysis['efficiency_score'] = 3.0
        elif memory_analysis['avg_memory_growth'] < 1000:  # < 1GB
            memory_analysis['efficiency_score'] = 2.0
        else:
            memory_analysis['efficiency_score'] = 1.0

        # 生成建议
        if memory_analysis['peak_memory'] > 1000:
            memory_analysis['recommendations'].append("考虑减小batch_size或num_grasps以降低内存使用")
        if memory_analysis['avg_memory_growth'] < 100:
            memory_analysis['recommendations'].append("内存使用较低，可以考虑增大batch_size提高吞吐量")

    return memory_analysis


def calculate_throughput_metrics(preprocessing_results):
    """计算吞吐量指标"""
    throughput_metrics = {}

    for batch_size, modes in preprocessing_results.items():
        throughput_metrics[batch_size] = {}

        for mode, stats in modes.items():
            if mode == 'single':
                samples_per_sec = batch_size / stats['time']
            elif mode.startswith('multi_'):
                num_grasps = int(mode.split('_')[1])
                total_samples = batch_size * num_grasps
                samples_per_sec = total_samples / stats['time']
            else:
                continue

            throughput_metrics[batch_size][mode] = {
                'samples_per_sec': samples_per_sec,
                'time_per_sample': stats['time'] / (batch_size * (int(mode.split('_')[1]) if mode.startswith('multi_') else 1)),
                'memory_per_sample': stats['memory'] / (batch_size * (int(mode.split('_')[1]) if mode.startswith('multi_') else 1))
            }

    return throughput_metrics


def print_performance_results(rotation_results, preprocessing_results, tensor_results):
    """打印性能测试结果"""
    print("\n" + "="*80)
    print("📈 性能基准测试结果")
    print("="*80)

    # 旋转类型支持结果
    print("\n🔄 旋转类型支持:")
    print("-" * 60)
    print(f"{'Rotation Type':<15} {'Status':<15} {'Time (ms)':<12} {'Note'}")
    print("-" * 60)

    for rot_type, info in rotation_results.items():
        status = info['status']
        if status == 'supported':
            time_ms = info['time'] * 1000
            note = f"Shape: {info['output_shape']}"
            print(f"{rot_type:<15} {'✅ 支持':<15} {time_ms:<12.3f} {note}")
        elif status == 'error':
            print(f"{rot_type:<15} {'❌ 错误':<15} {'-':<12} {info['error'][:40]}...")
        else:
            print(f"{rot_type:<15} {'📋 预留':<15} {'-':<12} {info['note']}")

    # 数据预处理性能结果
    print("\n🔄 数据预处理性能:")
    print("-" * 70)
    print(f"{'Batch Size':<12} {'Mode':<15} {'Time (ms)':<12} {'Memory (MB)':<12} {'Output Shape'}")
    print("-" * 70)
    
    for batch_size, modes in preprocessing_results.items():
        for mode, stats in modes.items():
            time_ms = stats['time'] * 1000
            memory_mb = stats['memory']
            shape = str(stats['output_shape'])
            print(f"{batch_size:<12} {mode:<15} {time_ms:<12.3f} {memory_mb:<12.2f} {shape}")
    
    # 张量操作性能结果
    print(f"\n⚡ 张量操作性能:")
    print("-" * 40)
    print(f"{'Operation':<20} {'Time (μs)':<12}")
    print("-" * 40)
    
    for op, time_sec in tensor_results.items():
        time_us = time_sec * 1000000
        print(f"{op:<20} {time_us:<12.3f}")
    
    # 内存使用分析
    memory_analysis = analyze_memory_efficiency(preprocessing_results)
    print(f"\n💾 内存使用分析:")
    print("-" * 50)
    print(f"峰值内存使用: {memory_analysis['peak_memory']:.1f}MB")
    print(f"平均内存增长: {memory_analysis['avg_memory_growth']:.1f}MB")
    print(f"内存效率评分: {memory_analysis['efficiency_score']:.1f}/5.0")

    if memory_analysis['recommendations']:
        print("\n💡 内存优化建议:")
        for rec in memory_analysis['recommendations']:
            print(f"  • {rec}")

    # 吞吐量分析
    throughput_metrics = calculate_throughput_metrics(preprocessing_results)
    print(f"\n🚀 吞吐量分析:")
    print("-" * 80)
    print(f"{'Batch Size':<12} {'Mode':<15} {'Samples/sec':<15} {'Time/sample(ms)':<18} {'Memory/sample(MB)'}")
    print("-" * 80)

    for batch_size in sorted(throughput_metrics.keys()):
        for mode, metrics in throughput_metrics[batch_size].items():
            samples_per_sec = metrics['samples_per_sec']
            time_per_sample_ms = metrics['time_per_sample'] * 1000
            memory_per_sample = metrics['memory_per_sample']
            print(f"{batch_size:<12} {mode:<15} {samples_per_sec:<15.1f} {time_per_sample_ms:<18.3f} {memory_per_sample:<.3f}")

    # 详细性能分析
    print(f"\n📊 详细性能分析:")
    print("-" * 60)

    # 找到最优配置
    best_throughput = 0
    best_config = None

    for batch_size in throughput_metrics:
        for mode, metrics in throughput_metrics[batch_size].items():
            if metrics['samples_per_sec'] > best_throughput:
                best_throughput = metrics['samples_per_sec']
                best_config = (batch_size, mode)

    if best_config:
        print(f"最优吞吐量配置: batch_size={best_config[0]}, mode={best_config[1]}")
        print(f"最大吞吐量: {best_throughput:.1f} samples/sec")

    # 计算多抓取vs单抓取的性能比率
    if 16 in preprocessing_results:
        single_time = preprocessing_results[16]['single']['time']
        if 'multi_4' in preprocessing_results[16]:
            multi_time = preprocessing_results[16]['multi_4']['time']
            time_ratio = multi_time / single_time
            print(f"多抓取(4)vs单抓取时间比率: {time_ratio:.2f}x")

            # 计算并行效率
            theoretical_ratio = 4.0  # 理论上应该是4倍时间
            parallel_efficiency = theoretical_ratio / time_ratio
            print(f"并行效率: {parallel_efficiency:.2f} ({parallel_efficiency*100:.1f}%)")

        single_memory = preprocessing_results[16]['single']['memory']
        if 'multi_4' in preprocessing_results[16]:
            multi_memory = preprocessing_results[16]['multi_4']['memory']
            memory_ratio = multi_memory / single_memory if single_memory > 0 else float('inf')
            print(f"多抓取(4)vs单抓取内存比率: {memory_ratio:.2f}x")

    # 张量操作效率
    if 'single_ops' in tensor_results and 'multi_ops' in tensor_results:
        ops_ratio = tensor_results['multi_ops'] / tensor_results['single_ops']
        print(f"多抓取vs单抓取张量操作比率: {ops_ratio:.2f}x")

        # 计算张量操作效率
        if ops_ratio > 0:
            tensor_efficiency = 1.0 / ops_ratio  # 理想情况下应该接近1
            print(f"张量操作效率: {tensor_efficiency:.2f}")

    # 性能建议
    print(f"\n💡 性能优化建议:")
    print("-" * 40)

    if memory_analysis['efficiency_score'] < 3.0:
        print("• 内存使用效率较低，建议优化内存分配")

    if best_config and best_config[0] < 16:
        print("• 可以尝试增大batch_size以提高吞吐量")
    elif best_config and best_config[0] > 16:
        print("• 当前batch_size已较大，注意内存使用")

    # 根据时间比率给出建议
    if 16 in preprocessing_results and 'multi_4' in preprocessing_results[16]:
        time_ratio = preprocessing_results[16]['multi_4']['time'] / preprocessing_results[16]['single']['time']
        if time_ratio > 5.0:
            print("• 多抓取处理时间较长，考虑优化并行处理逻辑")
        elif time_ratio < 3.0:
            print("• 多抓取处理效率良好，可以考虑增加num_grasps")


def run_comprehensive_benchmarks():
    """运行全面的性能基准测试"""
    print("🚀 开始全面性能基准测试...")

    try:
        # 旋转类型支持测试
        print("1/4 测试旋转类型支持...")
        rotation_results = test_rotation_types()

        # 数据预处理性能测试
        print("2/4 测试数据预处理性能...")
        preprocessing_results = benchmark_data_preprocessing()

        # 张量操作性能测试
        print("3/4 测试张量操作性能...")
        tensor_results = benchmark_tensor_operations()

        # 内存使用分析
        print("4/4 分析内存使用和性能指标...")
        memory_analysis = analyze_memory_efficiency(preprocessing_results)
        throughput_metrics = calculate_throughput_metrics(preprocessing_results)

        # 打印详细结果
        print_performance_results(rotation_results, preprocessing_results, tensor_results)

        # 生成性能报告
        generate_performance_report(rotation_results, preprocessing_results, tensor_results,
                                  memory_analysis, throughput_metrics)

        print("\n🎉 全面性能基准测试完成！")
        return True

    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def generate_performance_report(rotation_results, preprocessing_results, tensor_results,
                              memory_analysis, throughput_metrics):
    """生成性能报告摘要"""
    print("\n" + "="*80)
    print("📋 性能报告摘要")
    print("="*80)

    # 支持的旋转类型统计
    supported_rotations = sum(1 for r in rotation_results.values() if r['status'] == 'supported')
    total_rotations = len(rotation_results)
    print(f"旋转类型支持: {supported_rotations}/{total_rotations} ({supported_rotations/total_rotations*100:.1f}%)")

    # 最佳性能配置
    best_throughput = 0
    best_config = None
    for batch_size in throughput_metrics:
        for mode, metrics in throughput_metrics[batch_size].items():
            if metrics['samples_per_sec'] > best_throughput:
                best_throughput = metrics['samples_per_sec']
                best_config = (batch_size, mode)

    if best_config:
        print(f"推荐配置: batch_size={best_config[0]}, mode={best_config[1]}")
        print(f"最大吞吐量: {best_throughput:.1f} samples/sec")

    # 内存效率评估
    print(f"内存效率评分: {memory_analysis['efficiency_score']:.1f}/5.0")

    # 性能等级评估
    if memory_analysis['efficiency_score'] >= 4.0 and best_throughput > 100:
        performance_grade = "A (优秀)"
    elif memory_analysis['efficiency_score'] >= 3.0 and best_throughput > 50:
        performance_grade = "B (良好)"
    elif memory_analysis['efficiency_score'] >= 2.0 and best_throughput > 20:
        performance_grade = "C (一般)"
    else:
        performance_grade = "D (需要优化)"

    print(f"整体性能等级: {performance_grade}")

    print("="*80)


def run_performance_benchmarks():
    """运行标准性能基准测试（向后兼容）"""
    return run_comprehensive_benchmarks()


if __name__ == "__main__":
    success = run_performance_benchmarks()
    sys.exit(0 if success else 1)
