#!/usr/bin/env python3
"""
测试重复采样问题
"""

import os
import sys
import torch
import numpy as np
from collections import defaultdict

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

def analyze_duplicate_sampling():
    """分析重复采样问题"""
    
    print("=" * 80)
    print("分析 SceneLeapPlusDataset 中的重复采样问题")
    print("=" * 80)
    
    # 创建数据集
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric",
        "max_grasps_per_object": 100,  # 使用较小的值
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus_dup_test",
        "cache_mode": "train"
    }
    
    dataset = SceneLeapPlusDatasetCached(**config)
    
    print(f"数据集基本信息:")
    print(f"  - 数据集长度: {len(dataset)}")
    print(f"  - num_grasps: {dataset.num_grasps}")
    print(f"  - max_grasps_per_object: {dataset.max_grasps_per_object}")
    
    # 分析原始抓取数据
    print(f"\n原始抓取数据分析:")
    total_unique_grasps = 0
    object_grasp_counts = {}
    
    for scene_id, scene_data in dataset.hand_pose_data.items():
        for obj_code, poses_tensor in scene_data.items():
            if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                # 应用 max_grasps_per_object 限制
                max_grasps = dataset.max_grasps_per_object
                if max_grasps is not None:
                    actual_grasps = min(poses_tensor.shape[0], max_grasps)
                else:
                    actual_grasps = poses_tensor.shape[0]
                
                key = f"{scene_id}_{obj_code}"
                object_grasp_counts[key] = actual_grasps
                total_unique_grasps += actual_grasps
    
    print(f"  - 总的唯一抓取数: {total_unique_grasps}")
    print(f"  - 物体数量: {len(object_grasp_counts)}")
    print(f"  - 平均每个物体的抓取数: {total_unique_grasps / len(object_grasp_counts):.1f}")
    
    # 分析数据集中的采样情况
    print(f"\n数据集采样分析:")
    
    # 统计每个物体在数据集中出现的次数
    object_appearances = defaultdict(int)
    object_view_counts = defaultdict(int)
    
    for item_data in dataset.data:
        scene_id = item_data['scene_id']
        obj_code = item_data['object_code']
        key = f"{scene_id}_{obj_code}"
        object_appearances[key] += 1
        object_view_counts[key] += 1
    
    print(f"  - 数据项总数: {len(dataset.data)}")
    print(f"  - 唯一物体数: {len(object_appearances)}")
    
    # 显示前几个物体的出现次数
    print(f"\n物体在数据集中的出现次数（前10个）:")
    for i, (obj_key, count) in enumerate(list(object_appearances.items())[:10]):
        grasp_count = object_grasp_counts.get(obj_key, 0)
        print(f"  {obj_key[:50]}...: {count} 次, 原始抓取数: {grasp_count}")
    
    # 计算理论上的总采样次数
    total_theoretical_samples = 0
    for obj_key, appearances in object_appearances.items():
        grasp_count = object_grasp_counts.get(obj_key, 0)
        total_theoretical_samples += appearances * dataset.num_grasps
    
    print(f"\n采样统计:")
    print(f"  - 理论总采样次数: {total_theoretical_samples}")
    print(f"  - 唯一抓取数: {total_unique_grasps}")
    print(f"  - 重复倍数: {total_theoretical_samples / total_unique_grasps:.2f}")
    
    # 分析重复采样的具体情况
    print(f"\n重复采样详细分析:")
    
    # 测试几个样本，看看是否真的有重复
    print(f"  测试前5个样本的抓取内容:")
    
    for i in range(min(5, len(dataset))):
        sample = dataset[i]
        if 'error' not in sample:
            item_data = dataset.data[i]
            hand_poses = sample['hand_model_pose']
            
            print(f"\n  样本 {i}:")
            print(f"    场景-物体: {item_data['scene_id'][:20]}..._{item_data['object_code'][:30]}...")
            print(f"    视角: {item_data['depth_view_index']}")
            print(f"    抓取形状: {hand_poses.shape}")
            
            # 检查抓取的唯一性（通过比较前几个参数）
            if hand_poses.shape[0] > 1:
                unique_poses = []
                for j in range(hand_poses.shape[0]):
                    pose = hand_poses[j][:5]  # 只比较前5个参数
                    is_unique = True
                    for existing_pose in unique_poses:
                        if torch.allclose(pose, existing_pose, atol=1e-6):
                            is_unique = False
                            break
                    if is_unique:
                        unique_poses.append(pose)
                
                print(f"    唯一抓取数: {len(unique_poses)} / {hand_poses.shape[0]}")
                if len(unique_poses) < hand_poses.shape[0]:
                    print(f"    ⚠️  检测到重复抓取!")
    
    # 分析替代设计中的重复问题
    print(f"\n" + "="*50)
    print(f"替代设计中的重复问题分析:")
    
    # 如果简单地按顺序分割所有抓取
    all_grasps_simple = []
    for scene_id, scene_data in dataset.hand_pose_data.items():
        for obj_code, poses_tensor in scene_data.items():
            if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                max_grasps = dataset.max_grasps_per_object
                if max_grasps is not None:
                    poses_tensor = poses_tensor[:max_grasps]
                
                for i in range(poses_tensor.shape[0]):
                    all_grasps_simple.append({
                        'scene_obj': f"{scene_id}_{obj_code}",
                        'grasp_idx': i,
                        'pose': poses_tensor[i]
                    })
    
    simple_dataset_length = len(all_grasps_simple) // dataset.num_grasps
    
    print(f"  简单替代设计:")
    print(f"    - 总抓取数: {len(all_grasps_simple)}")
    print(f"    - 数据集长度: {simple_dataset_length}")
    print(f"    - 每个抓取只出现一次: ✅ 无重复")
    
    print(f"\n  当前设计 vs 简单替代设计:")
    print(f"    - 当前设计可能的重复倍数: {total_theoretical_samples / total_unique_grasps:.2f}")
    print(f"    - 简单替代设计重复倍数: 1.00 (无重复)")
    
    # 清理
    if hasattr(dataset, '_cleanup'):
        dataset._cleanup()

if __name__ == "__main__":
    analyze_duplicate_sampling()
