#!/usr/bin/env python3
"""
测试真实缓存创建场景下的中断安全性

这个脚本模拟真实的SceneLeapPlus缓存创建过程，测试中断时的行为
"""

import os
import h5py
import logging
import sys
import signal
import time
import subprocess
import psutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datasets.utils.cache_utils import CacheManager, validate_cache_file

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

def create_dummy_cache_data(cache_path: str, num_items: int) -> None:
    """
    创建虚拟缓存数据，模拟SceneLeapPlus的缓存创建过程
    """
    logger.info(f"开始创建虚拟缓存数据: {cache_path}, 项目数: {num_items}")
    
    with h5py.File(cache_path, 'w') as hf:
        for idx in range(num_items):
            # 模拟SceneLeapPlus的数据结构
            group = hf.create_group(str(idx))
            
            # 模拟各种数据类型
            group.create_dataset('scene_pc', data=[[1.0, 2.0, 3.0]] * 100)  # 点云数据
            group.create_dataset('hand_model_pose', data=[0.1] * 48)  # 手部姿态
            group.create_dataset('se3', data=[0.0] * 7)  # SE3变换
            group.attrs['obj_code'] = f'obj_{idx:06d}'
            group.attrs['scene_id'] = f'scene_{idx:06d}'
            group.attrs['is_error'] = False
            
            # 模拟处理时间（比实际快很多）
            if idx % 10 == 0:
                time.sleep(0.01)  # 每10个项目暂停一下
            
            if idx % 100 == 0:
                logger.info(f"已创建 {idx+1}/{num_items} 个缓存项目")
    
    logger.info(f"缓存数据创建完成: {num_items} 个项目")

def test_current_cache_system_interrupt():
    """测试当前缓存系统在中断时的行为"""
    logger.info("测试当前缓存系统的中断安全性...")
    
    # 创建测试配置
    test_cache_dir = "/tmp/test_real_cache_interrupt"
    os.makedirs(test_cache_dir, exist_ok=True)
    cache_path = os.path.join(test_cache_dir, "test_sceneleapplus_cache.h5")
    
    # 清理可能存在的测试文件
    if os.path.exists(cache_path):
        os.remove(cache_path)
    
    num_items = 1000
    
    try:
        # 创建缓存管理器
        cache_manager = CacheManager(
            config={'cache_dir': test_cache_dir, 'cache_version': 'test'},
            num_items=num_items
        )
        
        logger.info("开始缓存创建过程...")
        
        # 模拟缓存创建过程
        hf, cache_loaded = cache_manager.setup_cache()
        
        if not cache_loaded:
            logger.info("缓存未加载，开始创建...")
            
            # 启动一个子进程来创建缓存，然后中断它
            def create_cache_in_subprocess():
                try:
                    cache_manager.create_cache(create_dummy_cache_data)
                except Exception as e:
                    logger.error(f"子进程缓存创建失败: {e}")
            
            # 在单独的进程中创建缓存
            import multiprocessing
            cache_process = multiprocessing.Process(target=create_cache_in_subprocess)
            cache_process.start()
            
            # 让进程运行一段时间后中断
            time.sleep(3)  # 让缓存创建运行3秒
            
            logger.info("发送中断信号到缓存创建进程...")
            cache_process.terminate()  # 发送SIGTERM
            
            # 等待进程结束
            cache_process.join(timeout=5)
            
            if cache_process.is_alive():
                logger.warning("进程未正常结束，强制杀死...")
                cache_process.kill()
                cache_process.join()
            
            logger.info(f"缓存创建进程已结束，退出码: {cache_process.exitcode}")
        
        # 检查中断后的状态
        logger.info("检查中断后的缓存状态...")
        
        # 1. 检查文件是否存在
        if os.path.exists(cache_path):
            logger.info(f"缓存文件存在: {cache_path}")
            
            # 2. 检查文件是否可以正常访问
            try:
                with h5py.File(cache_path, 'r') as hf:
                    actual_items = len(hf)
                    logger.info(f"缓存文件包含 {actual_items} 个项目（期望 {num_items}）")
                    
                    if actual_items < num_items:
                        logger.warning("缓存文件不完整，这是预期的中断结果")
                    
                logger.info("✅ 文件可以正常读取")
                
                # 3. 测试文件写入访问
                with h5py.File(cache_path, 'r+') as hf:
                    hf.attrs['test_write_access'] = 'ok'
                logger.info("✅ 文件可以正常写入")
                
            except Exception as e:
                logger.error(f"❌ 文件访问失败: {e}")
                return False
            
            # 4. 验证缓存有效性
            is_valid = validate_cache_file(cache_path, num_items)
            logger.info(f"缓存有效性检查: {'✅ 有效' if is_valid else '❌ 无效（预期结果）'}")
            
        else:
            logger.info("缓存文件不存在（可能在中断时被清理）")
        
        # 5. 检查是否有僵尸进程
        zombie_count = 0
        for proc in psutil.process_iter(['pid', 'status', 'name']):
            try:
                if proc.info['status'] == psutil.STATUS_ZOMBIE:
                    zombie_count += 1
                    logger.warning(f"发现僵尸进程: PID={proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if zombie_count == 0:
            logger.info("✅ 没有发现僵尸进程")
        else:
            logger.warning(f"❌ 发现 {zombie_count} 个僵尸进程")
        
        return zombie_count == 0
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                logger.info("清理了测试缓存文件")
            except Exception as e:
                logger.warning(f"清理测试文件失败: {e}")

def check_system_resources():
    """检查系统资源状态"""
    logger.info("检查系统资源状态...")
    
    # 检查内存使用
    memory = psutil.virtual_memory()
    logger.info(f"内存使用: {memory.percent}% ({memory.used / 1024**3:.1f}GB / {memory.total / 1024**3:.1f}GB)")
    
    # 检查磁盘空间
    disk = psutil.disk_usage('/tmp')
    logger.info(f"临时目录磁盘使用: {disk.percent}% ({disk.used / 1024**3:.1f}GB / {disk.total / 1024**3:.1f}GB)")
    
    # 检查打开的文件句柄数
    try:
        process = psutil.Process()
        open_files = len(process.open_files())
        logger.info(f"当前进程打开的文件数: {open_files}")
    except Exception as e:
        logger.warning(f"无法检查文件句柄数: {e}")

def main():
    """主函数"""
    logger.info("开始真实缓存中断安全性测试...")
    
    # 检查初始系统状态
    logger.info("=" * 60)
    logger.info("初始系统状态检查")
    logger.info("=" * 60)
    check_system_resources()
    
    # 执行中断测试
    logger.info("=" * 60)
    logger.info("执行缓存中断测试")
    logger.info("=" * 60)
    
    success = test_current_cache_system_interrupt()
    
    # 检查最终系统状态
    logger.info("=" * 60)
    logger.info("最终系统状态检查")
    logger.info("=" * 60)
    check_system_resources()
    
    # 总结
    logger.info("=" * 60)
    logger.info("测试结果总结")
    logger.info("=" * 60)
    
    if success:
        logger.info("✅ 当前缓存系统在中断时表现良好")
        logger.info("   - 没有文件句柄泄漏")
        logger.info("   - 没有僵尸进程")
        logger.info("   - 文件可以正常访问")
        logger.info("   - 中断后系统状态正常")
    else:
        logger.warning("❌ 当前缓存系统在中断时存在问题")
        logger.info("建议实施改进的缓存创建机制")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
