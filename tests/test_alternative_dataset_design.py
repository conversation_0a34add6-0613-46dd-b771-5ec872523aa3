#!/usr/bin/env python3
"""
测试按抓取数量分割的替代数据集设计
"""

import os
import sys
import torch
import numpy as np
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

class AlternativeSceneLeapPlusDataset:
    """
    按抓取数量分割的替代设计
    数据集长度 = 总抓取数 ÷ num_grasps
    """
    
    def __init__(self, base_dataset: SceneLeapPlusDatasetCached, num_grasps: int):
        self.base_dataset = base_dataset
        self.num_grasps = num_grasps
        
        # 收集所有抓取数据
        self.all_grasps = []
        self._collect_all_grasps()
        
        # 计算新的数据集长度
        self.dataset_length = len(self.all_grasps) // num_grasps
        
        print(f"替代设计统计:")
        print(f"  - 总抓取数: {len(self.all_grasps)}")
        print(f"  - num_grasps: {num_grasps}")
        print(f"  - 新数据集长度: {self.dataset_length}")
        print(f"  - 丢弃的抓取数: {len(self.all_grasps) % num_grasps}")
    
    def _collect_all_grasps(self):
        """收集所有抓取数据"""
        print("正在收集所有抓取数据...")
        
        # 遍历所有场景和物体
        for scene_id, scene_data in self.base_dataset.hand_pose_data.items():
            for obj_code, poses_tensor in scene_data.items():
                if isinstance(poses_tensor, torch.Tensor) and poses_tensor.shape[0] > 0:
                    # 应用 max_grasps_per_object 限制
                    max_grasps = self.base_dataset.max_grasps_per_object
                    if max_grasps is not None:
                        poses_tensor = poses_tensor[:max_grasps]
                    
                    # 为每个抓取添加元数据
                    for i in range(poses_tensor.shape[0]):
                        grasp_data = {
                            'scene_id': scene_id,
                            'object_code': obj_code,
                            'grasp_index': i,
                            'hand_pose': poses_tensor[i],
                            # 这里可以添加更多元数据，如SE3矩阵等
                        }
                        self.all_grasps.append(grasp_data)
    
    def __len__(self):
        return self.dataset_length
    
    def __getitem__(self, idx):
        """获取一个数据项（包含 num_grasps 个抓取）"""
        if idx >= self.dataset_length:
            raise IndexError(f"Index {idx} out of bounds for dataset with length {self.dataset_length}")
        
        # 获取连续的 num_grasps 个抓取
        start_idx = idx * self.num_grasps
        end_idx = start_idx + self.num_grasps
        
        grasp_batch = self.all_grasps[start_idx:end_idx]
        
        # 组装返回数据
        hand_poses = torch.stack([g['hand_pose'] for g in grasp_batch])
        
        # 收集元数据
        scene_ids = [g['scene_id'] for g in grasp_batch]
        object_codes = [g['object_code'] for g in grasp_batch]
        
        return {
            'hand_model_pose': hand_poses,  # [num_grasps, 23]
            'scene_ids': scene_ids,
            'object_codes': object_codes,
            'batch_info': f"抓取 {start_idx}-{end_idx-1}"
        }

def compare_dataset_designs():
    """对比两种数据集设计"""
    
    print("=" * 80)
    print("对比当前设计 vs 按抓取分割的设计")
    print("=" * 80)
    
    # 创建基础数据集
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric",
        "max_grasps_per_object": 100,  # 使用较小的值加快测试
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus_compare",
        "cache_mode": "train"
    }
    
    base_dataset = SceneLeapPlusDatasetCached(**config)
    
    print(f"\n当前设计 (SceneLeapPlusDataset):")
    print(f"  - 数据集长度: {len(base_dataset)}")
    print(f"  - num_grasps: {base_dataset.num_grasps}")
    print(f"  - max_grasps_per_object: {base_dataset.max_grasps_per_object}")
    
    # 测试几个样本
    print(f"\n  前3个样本:")
    for i in range(3):
        sample = base_dataset[i]
        if 'error' not in sample:
            item_data = base_dataset.data[i]
            print(f"    样本 {i}: {item_data['scene_id'][:20]}..., "
                  f"{item_data['object_code'][:30]}..., "
                  f"视角 {item_data['depth_view_index']}, "
                  f"形状 {sample['hand_model_pose'].shape}")
    
    # 创建替代设计
    print(f"\n" + "="*50)
    alternative_dataset = AlternativeSceneLeapPlusDataset(base_dataset, num_grasps=4)
    
    print(f"\n  前3个样本:")
    for i in range(3):
        sample = alternative_dataset[i]
        print(f"    样本 {i}: {sample['batch_info']}, "
              f"形状 {sample['hand_model_pose'].shape}")
        print(f"      场景: {set(sample['scene_ids'])}")
        print(f"      物体: {set([obj[:30] + '...' for obj in sample['object_codes']])}")
    
    # 分析差异
    print(f"\n" + "="*50)
    print(f"设计对比分析:")
    
    print(f"\n1. 数据集长度:")
    print(f"   当前设计: {len(base_dataset)}")
    print(f"   替代设计: {len(alternative_dataset)}")
    print(f"   比例: {len(alternative_dataset) / len(base_dataset):.2f}")
    
    print(f"\n2. 数据分布:")
    print(f"   当前设计: 每个样本来自同一个(场景,物体,视角)")
    print(f"   替代设计: 每个样本可能包含不同场景/物体的抓取")
    
    print(f"\n3. 训练特性:")
    print(f"   当前设计: 适合学习特定场景下的抓取分布")
    print(f"   替代设计: 适合学习跨场景的抓取模式")
    
    # 分析替代设计的问题
    analyze_alternative_issues(alternative_dataset)
    
    # 清理
    if hasattr(base_dataset, '_cleanup'):
        base_dataset._cleanup()

def analyze_alternative_issues(alt_dataset):
    """分析替代设计的潜在问题"""
    
    print(f"\n" + "="*50)
    print(f"替代设计的潜在问题分析:")
    
    # 检查数据一致性
    print(f"\n1. 数据一致性问题:")
    
    # 检查前几个样本的场景/物体分布
    scene_consistency = []
    object_consistency = []
    
    for i in range(min(10, len(alt_dataset))):
        sample = alt_dataset[i]
        unique_scenes = len(set(sample['scene_ids']))
        unique_objects = len(set(sample['object_codes']))
        
        scene_consistency.append(unique_scenes)
        object_consistency.append(unique_objects)
        
        if i < 3:
            print(f"   样本 {i}: {unique_scenes} 个不同场景, {unique_objects} 个不同物体")
    
    print(f"   平均每个样本的场景数: {np.mean(scene_consistency):.2f}")
    print(f"   平均每个样本的物体数: {np.mean(object_consistency):.2f}")
    
    print(f"\n2. 训练问题:")
    print(f"   - 混合场景: 一个批次包含不同场景的数据，可能影响学习")
    print(f"   - 混合物体: 一个批次包含不同物体的抓取，语义不一致")
    print(f"   - 缺少上下文: 丢失了场景点云、物体网格等上下文信息")
    
    print(f"\n3. 实现复杂性:")
    print(f"   - 需要重新设计数据加载逻辑")
    print(f"   - 需要处理不同物体的上下文信息对齐")
    print(f"   - 缓存系统需要重新设计")

if __name__ == "__main__":
    compare_dataset_designs()
