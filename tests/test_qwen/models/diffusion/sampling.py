import torch
import torch.nn.functional as F
from typing import Dict, <PERSON><PERSON>

def compute_pred_x0_from_noise(x_t: torch.Tensor, t: torch.Tensor, pred_noise: torch.Tensor, 
                               sqrt_recip_alphas_cumprod: torch.Tensor, 
                               sqrt_recipm1_alphas_cumprod: torch.Tensor) -> torch.Tensor:
    """
    从预测噪声计算预测的干净数据
    
    Args:
        x_t: 带噪声的数据
        t: 时间步
        pred_noise: 预测的噪声
        sqrt_recip_alphas_cumprod: sqrt(1/alphas_cumprod) 的缓存
        sqrt_recipm1_alphas_cumprod: sqrt(1/alphas_cumprod - 1) 的缓存
        
    Returns:
        pred_x0: 预测的干净数据
    """
    if x_t.dim() == 2:
        # 单抓取格式
        B, *x_shape = x_t.shape
        pred_x0 = sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                 sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * pred_noise
    elif x_t.dim() == 3:
        # 多抓取格式
        B, num_grasps, pose_dim = x_t.shape
        t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
        sqrt_recip = sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        sqrt_recipm1 = sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        pred_x0 = sqrt_recip * x_t - sqrt_recipm1 * pred_noise
    else:
        raise ValueError(f"Unsupported input dimension: {x_t.dim()}")
    return pred_x0