import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any, List
from models.utils.diffusion_utils import make_schedule_ddpm
from .sampling import compute_pred_x0_from_noise


class DDPMModel(nn.Module):
    def __init__(self, eps_model: nn.Module, timesteps: int, schedule_cfg: Dict, 
                 pred_x0: bool = False, use_cfg: bool = False, guidance_scale: float = 7.5,
                 use_negative_guidance: bool = False, negative_guidance_scale: float = 1.0):
        """
        DDPM扩散模型核心实现
        
        Args:
            eps_model: 噪声预测网络（UNet等）
            timesteps: 扩散步数
            schedule_cfg: 扩散调度配置
            pred_x0: 是否直接预测干净数据
            use_cfg: 是否使用分类器自由引导
            guidance_scale: 引导强度
            use_negative_guidance: 是否使用负向引导
            negative_guidance_scale: 负向引导强度
        """
        super().__init__()
        
        self.eps_model = eps_model
        self.timesteps = timesteps
        self.schedule_cfg = schedule_cfg
        self.pred_x0 = pred_x0
        self.use_cfg = use_cfg
        self.guidance_scale = guidance_scale
        self.use_negative_guidance = use_negative_guidance
        self.negative_guidance_scale = negative_guidance_scale
        
        # 注册扩散参数buffers
        for k, v in make_schedule_ddpm(self.timesteps, **self.schedule_cfg).items():
            self.register_buffer(k, v)
            
    def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        """
        前向扩散过程：给干净数据加噪
        支持单抓取格式 [B, pose_dim] 和多抓取格式 [B, num_grasps, pose_dim]
        """
        if x0.dim() == 2:
            # 单抓取格式（向后兼容）
            B, *x_shape = x0.shape
            x_t = self.sqrt_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x0 + \
                self.sqrt_one_minus_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * noise
            return x_t

        elif x0.dim() == 3:
            # 多抓取格式
            B, num_grasps, pose_dim = x0.shape

            # 时间步扩展到所有抓取
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]

            # 获取扩散系数并扩展维度
            alpha_cumprod = self.sqrt_alphas_cumprod[t_expanded]  # [B, num_grasps]
            alpha_cumprod = alpha_cumprod.unsqueeze(-1)  # [B, num_grasps, 1]

            one_minus_alpha = self.sqrt_one_minus_alphas_cumprod[t_expanded]
            one_minus_alpha = one_minus_alpha.unsqueeze(-1)  # [B, num_grasps, 1]

            # 并行加噪
            x_t = alpha_cumprod * x0 + one_minus_alpha * noise
            return x_t

        else:
            raise ValueError(f"Unsupported input dimension: {x0.dim()}. Expected 2 or 3.")

    def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        模型预测：根据噪声数据预测噪声或干净数据
        支持单抓取格式 [B, pose_dim] 和多抓取格式 [B, num_grasps, pose_dim]
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._model_predict_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._model_predict_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _model_predict_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """单抓取格式的模型预测"""
        B, *x_shape = x_t.shape
        if self.pred_x0:
            pred_x0 = self.eps_model(x_t, t, data)
            pred_noise = (self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * x_t - pred_x0) / \
                         self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape)))
        else:
            pred_noise = self.eps_model(x_t, t, data)
            pred_x0 = self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * x_t - \
                      self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1,) * len(x_shape))) * pred_noise
        return pred_noise, pred_x0

    def _model_predict_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """多抓取格式的模型预测"""
        B, num_grasps, pose_dim = x_t.shape

        if self.pred_x0:
            # 预测干净数据
            pred_x0 = self.eps_model(x_t, t, data)  # [B, num_grasps, pose_dim]

            # 计算预测噪声
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

            pred_noise = (sqrt_recip * x_t - pred_x0) / sqrt_recipm1
        else:
            # 预测噪声
            pred_noise = self.eps_model(x_t, t, data)  # [B, num_grasps, pose_dim]

            # 计算预测干净数据
            t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
            sqrt_recip = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
            sqrt_recipm1 = self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

            pred_x0 = sqrt_recip * x_t - sqrt_recipm1 * pred_noise

        return pred_noise, pred_x0

    def _compute_pred_x0_from_noise(self, x_t: torch.Tensor, t: torch.Tensor, pred_noise: torch.Tensor) -> torch.Tensor:
        """从预测噪声计算预测的干净数据"""
        return compute_pred_x0_from_noise(
            x_t, t, pred_noise, 
            self.sqrt_recip_alphas_cumprod, 
            self.sqrt_recipm1_alphas_cumprod
        )

    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算后验分布的均值和方差
        支持单抓取和多抓取格式
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._p_mean_variance_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._p_mean_variance_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _p_mean_variance_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """单抓取格式的后验分布计算"""
        B, *x_shape = x_t.shape
        pred_noise, pred_x0 = self.model_predict(x_t, t, data)

        model_mean = self.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape))) * pred_x0 + \
            self.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape))) * x_t
        posterior_variance = self.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))

        return model_mean, posterior_variance, posterior_log_variance

    def _p_mean_variance_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """多抓取格式的后验分布计算"""
        B, num_grasps, pose_dim = x_t.shape
        pred_noise, pred_x0 = self.model_predict(x_t, t, data)

        # 时间步扩展
        t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]

        # 获取后验分布系数并扩展维度
        coef1 = self.posterior_mean_coef1[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        coef2 = self.posterior_mean_coef2[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        # 计算后验均值
        model_mean = coef1 * pred_x0 + coef2 * x_t

        # 计算后验方差
        posterior_variance = self.posterior_variance[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        posterior_log_variance = self.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        return model_mean, posterior_variance, posterior_log_variance

    def p_mean_variance_cfg(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        分类器自由引导的后验分布计算
        支持单抓取和多抓取格式
        """
        if x_t.dim() == 2:
            # 单抓取格式（向后兼容）
            return self._p_mean_variance_cfg_single(x_t, t, data)
        elif x_t.dim() == 3:
            # 多抓取格式
            return self._p_mean_variance_cfg_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")

    def _p_mean_variance_cfg_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """单抓取格式的CFG"""
        B, *x_shape = x_t.shape

        guidance_scale = getattr(self, 'guidance_scale', 7.5)
        negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        x_t_expanded = x_t.repeat(3, 1)
        t_expanded = t.repeat(3)

        data_expanded = self._prepare_cfg_data(data, B)

        pred_noise_all, pred_x0_all = self.model_predict(x_t_expanded, t_expanded, data_expanded)

        pred_noise_uncond = pred_noise_all[:B]
        pred_noise_pos = pred_noise_all[B:2*B]
        pred_noise_neg = pred_noise_all[2*B:3*B]

        pred_x0_uncond = pred_x0_all[:B]
        pred_x0_pos = pred_x0_all[B:2*B]
        pred_x0_neg = pred_x0_all[2*B:3*B]

        if hasattr(self, 'use_negative_guidance') and self.use_negative_guidance:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                          negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                        negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
        else:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond)

        model_mean = self.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape))) * guided_x0 + \
            self.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape))) * x_t
        posterior_variance = self.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))

        return model_mean, posterior_variance, posterior_log_variance

    def _p_mean_variance_cfg_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """多抓取格式的CFG"""
        B, num_grasps, pose_dim = x_t.shape

        guidance_scale = getattr(self, 'guidance_scale', 7.5)
        negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        # 扩展输入: [B, num_grasps, pose_dim] -> [3*B, num_grasps, pose_dim]
        x_t_expanded = x_t.repeat(3, 1, 1)
        t_expanded = t.repeat(3)

        data_expanded = self._prepare_cfg_data(data, B)

        pred_noise_all, pred_x0_all = self.model_predict(x_t_expanded, t_expanded, data_expanded)

        # 分离无条件、正向、负向预测
        pred_noise_uncond = pred_noise_all[:B]
        pred_noise_pos = pred_noise_all[B:2*B]
        pred_noise_neg = pred_noise_all[2*B:3*B]

        pred_x0_uncond = pred_x0_all[:B]
        pred_x0_pos = pred_x0_all[B:2*B]
        pred_x0_neg = pred_x0_all[2*B:3*B]

        # CFG计算
        if hasattr(self, 'use_negative_guidance') and self.use_negative_guidance:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                          negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                        negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
        else:
            guided_noise = pred_noise_uncond + guidance_scale * (pred_noise_pos - pred_noise_uncond)
            guided_x0 = pred_x0_uncond + guidance_scale * (pred_x0_pos - pred_x0_uncond)

        # 计算后验分布参数（适配多抓取维度）
        t_expanded = t.unsqueeze(1).expand(-1, num_grasps)  # [B, num_grasps]
        coef1 = self.posterior_mean_coef1[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        coef2 = self.posterior_mean_coef2[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        model_mean = coef1 * guided_x0 + coef2 * x_t
        posterior_variance = self.posterior_variance[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]
        posterior_log_variance = self.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)  # [B, num_grasps, 1]

        return model_mean, posterior_variance, posterior_log_variance

    def _prepare_cfg_data(self, data: Dict, B: int) -> Dict:
        """
        为CFG准备数据，支持多抓取格式
        """
        cfg_data = {}

        # 处理张量数据
        for key in ['scene_pc', 'norm_pose', 'scene_cond']:
            if key in data and isinstance(data[key], torch.Tensor):
                if data[key].dim() == 2:
                    cfg_data[key] = data[key].repeat(3, 1)
                elif data[key].dim() == 3:
                    # 多抓取格式: [B, num_grasps, pose_dim] -> [3*B, num_grasps, pose_dim]
                    cfg_data[key] = data[key].repeat(3, 1, 1)
                elif data[key].dim() == 4:
                    cfg_data[key] = data[key].repeat(3, 1, 1, 1)
                else:
                    cfg_data[key] = data[key].repeat(3)
            elif key in data:
                # 非张量数据
                cfg_data[key] = data[key] * 3

        # 文本条件处理保持不变（每个场景一个文本条件）
        if 'text_cond' in data and data['text_cond'] is not None:
            text_cond = data['text_cond']

            uncond_text = torch.zeros_like(text_cond)
            pos_text = text_cond
            if 'neg_pred' in data and data['neg_pred'] is not None:
                neg_text = data['neg_pred']
            else:
                neg_text = torch.zeros_like(text_cond)

            cfg_data['text_cond'] = torch.cat([uncond_text, pos_text, neg_text], dim=0)
        else:
            cfg_data['text_cond'] = None

        for key in ['neg_pred', 'neg_text_features', 'text_mask']:
            if key in data and data[key] is not None:
                if isinstance(data[key], torch.Tensor):
                    if data[key].dim() == 2:
                        cfg_data[key] = data[key].repeat(3, 1)
                    elif data[key].dim() == 3:
                        cfg_data[key] = data[key].repeat(3, 1, 1)
                    else:
                        cfg_data[key] = data[key].repeat(3)
                else:
                    cfg_data[key] = data[key]

        return cfg_data

    @torch.no_grad()
    def p_sample(self, x_t: torch.Tensor, t: int, data: Dict) -> torch.Tensor:
        B, *_ = x_t.shape
        batch_timestep = torch.full((B, ), t, device=self.device, dtype=torch.long)

        if hasattr(self, 'use_cfg') and self.use_cfg and self.training == False:
            model_mean, model_variance, model_log_variance = self.p_mean_variance_cfg(x_t, batch_timestep, data)
        else:
            model_mean, model_variance, model_log_variance = self.p_mean_variance(x_t, batch_timestep, data)

        noise = torch.randn_like(x_t) if t > 0 else 0.
        pred_x = model_mean + (0.5 * model_log_variance).exp() * noise
        return pred_x
    
    @torch.no_grad()
    def p_sample_loop(self, data: Dict) -> torch.Tensor:
        """
        采样循环：从噪声逐步去噪生成干净数据
        支持单抓取和多抓取格式，实现 One-shot Parallel Decoding
        """
        # 根据数据格式确定初始噪声形状
        if 'norm_pose' in data:
            if isinstance(data['norm_pose'], torch.Tensor):
                if data['norm_pose'].dim() == 3:
                    # 多抓取格式: [B, num_grasps, pose_dim]
                    x_t = torch.randn_like(data['norm_pose'], device=self.device)
                elif data['norm_pose'].dim() == 2:
                    # 单抓取格式: [B, pose_dim]
                    x_t = torch.randn_like(data['norm_pose'], device=self.device)
                else:
                    raise ValueError(f"Unsupported norm_pose dimension: {data['norm_pose'].dim()}")
            else:
                # 列表格式处理（向后兼容）
                x_t = torch.randn(len(data['norm_pose']), data['norm_pose'][0].shape[-1], device=self.device)
        else:
            raise ValueError("norm_pose not found in data")

        # 条件编码
        condition_dict = self.eps_model.condition(data)
        data.update(condition_dict)

        # 逐步去噪
        all_x_t = [x_t]
        for t in reversed(range(0, self.timesteps)):
            x_t = self.p_sample(x_t, t, data)
            all_x_t.append(x_t)

        # 返回所有时间步的结果
        # 单抓取: [B, timesteps+1, pose_dim]
        # 多抓取: [B, timesteps+1, num_grasps, pose_dim]
        return torch.stack(all_x_t, dim=1)
    
    @torch.no_grad()
    def sample(self, data: Dict, k: int = 1, use_cfg: bool = None, guidance_scale: float = None,
               use_negative_guidance: bool = None, negative_guidance_scale: float = None) -> torch.Tensor:
        original_use_cfg = getattr(self, 'use_cfg', False)
        original_guidance_scale = getattr(self, 'guidance_scale', 7.5)
        original_use_negative_guidance = getattr(self, 'use_negative_guidance', False)
        original_negative_guidance_scale = getattr(self, 'negative_guidance_scale', 1.0)

        if use_cfg is not None:
            self.use_cfg = use_cfg
        if guidance_scale is not None:
            self.guidance_scale = guidance_scale
        if use_negative_guidance is not None:
            self.use_negative_guidance = use_negative_guidance
        if negative_guidance_scale is not None:
            self.negative_guidance_scale = negative_guidance_scale

        try:
            ksamples = []
            for _ in range(k):
                ksamples.append(self.p_sample_loop(data))
            ksamples = torch.stack(ksamples, dim=1)
            return ksamples
        finally:
            self.use_cfg = original_use_cfg
            self.guidance_scale = original_guidance_scale
            self.use_negative_guidance = original_use_negative_guidance
            self.negative_guidance_scale = original_negative_guidance_scale