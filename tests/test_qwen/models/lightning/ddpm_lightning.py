import pytorch_lightning as pl
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any, List
from models.loss import GraspLossPose
from statistics import mean
import logging
from utils.hand_helper import process_hand_pose, process_hand_pose_test, denorm_hand_pose_robust
from utils.log_colors import HEADER, BLUE, GREEN, YELLOW, RED, ENDC, BOLD, UNDERLINE
from models.diffusion.ddpm_model import DDPMModel


class DDPMLightning(pl.LightningModule):
    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning model{ENDC}")
        self.save_hyperparameters()
        
        # 初始化扩散模型核心
        from models.decoder import build_decoder
        eps_model = build_decoder(cfg.decoder)
        self.diffusion_model = DDPMModel(
            eps_model=eps_model,
            timesteps=cfg.steps,
            schedule_cfg=cfg.schedule_cfg,
            pred_x0=cfg.pred_x0,
            use_cfg=cfg.get('use_cfg', False),
            guidance_scale=cfg.get('guidance_scale', 7.5),
            use_negative_guidance=cfg.get('use_negative_guidance', False),
            negative_guidance_scale=cfg.get('negative_guidance_scale', 1.0)
        )
        
        self.criterion = GraspLossPose(cfg.criterion)
        self.loss_weights = cfg.criterion.loss_weights
        self.rot_type = cfg.rot_type
        self.batch_size = cfg.batch_size
        self.print_freq = cfg.print_freq
        self.use_score = cfg.get('use_score', False)
        self.score_pretrain = cfg.get('score_pretrain', False)
        
        self.rand_t_type = cfg.rand_t_type
        self.mode = cfg.mode
        
        self.optimizer_cfg = cfg.optimizer
        self.scheduler = cfg.scheduler

    def training_step(self, batch, batch_idx):
        """
        训练步骤：支持单抓取和多抓取并行训练
        """
        # 手部姿态处理（已支持多抓取）
        batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)

        # 获取批次大小和抓取数量
        norm_pose = batch['norm_pose']
        if norm_pose.dim() == 3:
            # 多抓取格式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = norm_pose.shape
            total_samples = B * num_grasps  # 用于日志记录的有效样本数
        elif norm_pose.dim() == 2:
            # 单抓取格式: [B, pose_dim]
            B = norm_pose.shape[0]
            total_samples = B
        else:
            raise ValueError(f"Unsupported norm_pose dimension: {norm_pose.dim()}")

        # 时间步采样
        ts = self._sample_timesteps(B)

        # 噪声生成和加噪
        noise = torch.randn_like(norm_pose, device=self.device)
        x_t = self.diffusion_model.q_sample(x0=norm_pose, t=ts, noise=noise)

        # 条件编码
        condition_dict = self.diffusion_model.eps_model.condition(batch)
        batch.update(condition_dict)

        # 模型前向传播
        output = self.diffusion_model.eps_model(x_t, ts, batch)

        # 构建预测字典
        if self.diffusion_model.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            # 计算pred_x0（需要适配多抓取格式）
            pred_x0 = self.diffusion_model._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }

        # 添加负向条件（如果存在）
        if 'neg_pred' in condition_dict and condition_dict['neg_pred'] is not None:
            pred_dict['neg_pred'] = condition_dict['neg_pred']
            pred_dict['neg_text_features'] = condition_dict['neg_text_features']

        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='train')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        # 日志记录（使用总样本数）
        self.log_dict(loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=False, batch_size=total_samples)
        self.log("train/lr", self.optimizers().param_groups[0]['lr'], batch_size=total_samples)

        if batch_idx % self.print_freq == 0:
            empty_formatter = logging.Formatter('')
            root_logger = logging.getLogger()
            original_formatters = [handler.formatter for handler in root_logger.handlers]

            for handler in root_logger.handlers:
                handler.setFormatter(empty_formatter)
            logging.info("")

            for handler, formatter in zip(root_logger.handlers, original_formatters):
                handler.setFormatter(formatter)

            logging.info(f'{HEADER}Epoch {self.current_epoch} - Batch [{batch_idx}/{len(self.trainer.train_dataloader)}]{ENDC}')
            logging.info(f'{GREEN}{"Loss:":<21s} {loss.item():.4f}{ENDC}')
            for k, v in loss_dict.items():
                logging.info(f'{BLUE}{k.title() + ":":<21s} {v.item():.4f}{ENDC}')

        return loss

    def on_validation_epoch_start(self):
        self.validation_step_outputs = []
        return

    def validation_step(self, batch, batch_idx):
        """
        验证步骤：支持多抓取并行推理
        """
        # 数据预处理保持不变（已支持多抓取）
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

        # 推理模式重构
        pred_x0 = self.diffusion_model.sample(batch)  # 返回 [B, k, T+1, num_grasps, pose_dim] 或 [B, k, T+1, pose_dim]
        pred_x0 = pred_x0[:,0,-1]     # 取第一个采样的最后时间步

        # 根据数据维度确定处理方式
        if pred_x0.dim() == 3:
            # 多抓取模式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = pred_x0.shape
            batch_size = B * num_grasps

            # 构建多抓取预测字典
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],  # 关节角度
                "translation_norm": pred_x0[..., :3],  # 平移
                "rotation": pred_x0[..., 19:],  # 旋转
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式（向后兼容）
            pred_x0 = pred_x0 if pred_x0.dim() == 2 else pred_x0.squeeze(1)  # 确保是 [B, pose_dim]
            batch_size = pred_x0.shape[0]
            pred_dict = self._build_single_grasp_pred_dict(pred_x0)

        # 损失计算
        loss_dict = self.criterion(pred_dict, batch, mode='val')
        loss = sum(v * self.loss_weights[k] for k, v in loss_dict.items() if k in self.loss_weights)

        # 日志记录使用正确的batch_size
        self.log("val/loss", loss, prog_bar=True, batch_size=batch_size, sync_dist=True)
        self.validation_step_outputs.append({
            "loss": loss.item(),
            "loss_dict": {k: v.item() for k, v in loss_dict.items()}
        })

        return {"loss": loss, "loss_dict": loss_dict}

    def _build_single_grasp_pred_dict(self, pred_x0):
        """构建单抓取预测字典（向后兼容）"""
        pose_dim = pred_x0.shape[-1]
        return {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],  # 关节角度: [3:19]
            "translation_norm": pred_x0[..., :3],  # 平移: [0:3]
            "rotation": pred_x0[..., 19:],  # 旋转: [19:]
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
        
    def on_validation_epoch_end(self):
        val_loss = [x["loss"] for x in self.validation_step_outputs]
        avg_loss = mean(val_loss)
        
        val_detailed_loss = {}
        for k in self.validation_step_outputs[0]["loss_dict"].keys():
            val_detailed_loss[k] = mean([x["loss_dict"][k] for x in self.validation_step_outputs])
        
        empty_formatter = logging.Formatter('')
        root_logger = logging.getLogger()
        original_formatters = [handler.formatter for handler in root_logger.handlers]
        
        for handler in root_logger.handlers:
            handler.setFormatter(empty_formatter)
        logging.info("")
        
        for handler, formatter in zip(root_logger.handlers, original_formatters):
            handler.setFormatter(formatter)
        logging.info(f'{GREEN}Epoch {self.current_epoch} - Validation completed{ENDC}')
        
        avg_loss_str = f"{avg_loss:.4f}"
        emoji_loss = self._convert_number_to_emoji(avg_loss_str)
        logging.info(f'{BLUE}{"Loss:":<21s} {avg_loss_str} {emoji_loss}{ENDC}')
        
        for k, v in val_detailed_loss.items():
            v_str = f"{v:.4f}"
            emoji_v = self._convert_number_to_emoji(v_str)
            logging.info(f'{BLUE}{k.title() + ":":<21s} {v_str} {emoji_v}{ENDC}')
        
        self.log('val_loss', avg_loss, batch_size=self.batch_size, sync_dist=True)
        
        self.validation_step_outputs.clear()

    def on_test_start(self):
        self.metric_results = []
        return

    def test_step(self, batch, batch_idx):
        # 数据预处理
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

        # 推理
        pred_x0 = self.diffusion_model.sample(batch)
        pred_x0 = pred_x0[:,0,-1]  # 取第一个采样的最后时间步

        # 根据维度确定处理方式
        if pred_x0.dim() == 3:
            # 多抓取模式: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = pred_x0.shape
            batch_size = B * num_grasps
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式（向后兼容）
            pred_x0 = pred_x0 if pred_x0.dim() == 2 else pred_x0.squeeze(1)
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        # 指标计算
        metric_dict, metric_details = self.criterion(pred_dict, batch, mode='test')
        self.metric_results.append(metric_details)

        return metric_dict

    def test_step_teaser(self, batch):
        B = self.batch_size
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(batch)
        pred_x0 = pred_x0[:,0,-1]
        
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }
        metric_dict, metric_details = self.criterion.forward_metric(pred_dict, batch)

        return metric_dict, metric_details

    def configure_optimizers(self):
        if self.optimizer_cfg.name.lower() == "adam":
            optimizer = torch.optim.Adam(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        elif self.optimizer_cfg.name.lower() == "adamw":
            optimizer = torch.optim.AdamW(
                self.parameters(),
                lr=self.optimizer_cfg.lr,
                weight_decay=self.optimizer_cfg.weight_decay
            )
        else:
            raise NotImplementedError(f"No such optimizer: {self.optimizer_cfg.name}")
        
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            logging.info("Using score model without pretrain, will not load optimizer state")
            self.trainer.fit_loop.epoch_progress.current.completed = 0
            self.last_epoch = -1
        else:
            self.last_epoch = self.current_epoch - 1 if self.current_epoch else -1
        
        logging.info(f"Setting last_epoch to: {self.last_epoch}")
        
        if self.scheduler.name.lower() == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.scheduler.t_max,
                eta_min=self.scheduler.min_lr,
                last_epoch=self.last_epoch
            )
        elif self.scheduler.name.lower() == "steplr":
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                self.scheduler.step_size,
                gamma=self.scheduler.step_gamma,
                last_epoch=self.last_epoch
            )
            
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss"
            }
        }

    def forward_infer(self, data: Dict, k=4, timestep=-1):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, timestep]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]

        if pred_x0.dim() == 4:
            # 多抓取模式: [B, k, num_grasps, pose_dim]
            B, k, num_grasps, pose_dim = pred_x0.shape
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式: [B, k, pose_dim]
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
        return preds_hand, targets_hand
    
    def forward_infer_step(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)

        results = []
        for timestep in timesteps:
            pred_x0_t = pred_x0[:, :, timestep]
            pred_dict = {
                    "pred_pose_norm": pred_x0_t,
                    "qpos_norm": pred_x0_t[..., 3:19],
                    "translation_norm": pred_x0_t[..., :3],
                    "rotation": pred_x0_t[..., 19:],
                    "pred_noise": torch.tensor([1.0], device=self.device),
                    "noise": torch.tensor([1.0], device=self.device)
                }
            
            preds_hand, targets_hand = self.criterion.infer_norm_process_dict(pred_dict, data)
            results.append(preds_hand)

        return results

    def forward_get_pose(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]  # [B, k, num_grasps, pose_dim] 或 [B, k, pose_dim]
        print(f"pred_x0.shape {pred_x0.shape}")

        if pred_x0.dim() == 4:
            # 多抓取模式
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取模式
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }

        outputs, targets = self.criterion.infer_norm_process_dict_get_pose(pred_dict, data)
        return outputs, targets

    def forward_get_pose_matched(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_x0 = self.diffusion_model.sample(data, k=k)
        pred_x0 = pred_x0[:, :, -1]
        print(f"pred_x0.shape {pred_x0.shape}")

        # 构建pred_dict（支持多抓取和单抓取）
        pred_dict = self._build_pred_dict_adaptive(pred_x0)

        matched_pred, matched_targets, outputs, targets = self.criterion.infer_norm_process_dict_get_pose_matched(pred_dict, data)
        return matched_pred, matched_targets, outputs, targets

    def _build_pred_dict_adaptive(self, pred_x0):
        """自适应构建预测字典"""
        return {
            "pred_pose_norm": pred_x0,
            "qpos_norm": pred_x0[..., 3:19],
            "translation_norm": pred_x0[..., :3],
            "rotation": pred_x0[..., 19:],
            "pred_noise": torch.tensor([1.0], device=self.device),
            "noise": torch.tensor([1.0], device=self.device)
        }

    def forward_get_pose_raw(self, data: Dict, k=4):
        data = process_hand_pose_test(data, rot_type=self.rot_type, mode=self.mode)
        pred_pose_norm = self.diffusion_model.sample(data, k=k)
        pred_pose_norm = pred_pose_norm[:, :, -1]
        print(f"pred_pose_norm.shape {pred_pose_norm.shape}")

        hand_model_pose = denorm_hand_pose_robust(pred_pose_norm, self.rot_type, self.mode)

        return hand_model_pose

    def forward_train_instance(self, data: Dict):
        data = process_hand_pose(data, rot_type=self.rot_type, mode=self.mode)
        B = data['norm_pose'].shape[0]
        
        if self.rand_t_type == 'all':
            ts = torch.randint(0, self.diffusion_model.timesteps, (B, ), device=self.device).long()
        elif self.rand_t_type == 'half':
            ts = torch.randint(0, self.diffusion_model.timesteps, ((B + 1) // 2, ), device=self.device)
            if B % 2 == 1:
                ts = torch.cat([ts, self.diffusion_model.timesteps - ts[:-1] - 1], dim=0).long()
            else:
                ts = torch.cat([ts, self.diffusion_model.timesteps - ts - 1], dim=0).long()
        else:
            raise Exception('Unsupported rand ts type.')
        
        noise = torch.randn_like(data['norm_pose'], device=self.device)
        x_t = self.diffusion_model.q_sample(x0=data['norm_pose'], t=ts, noise=noise)

        condition = self.diffusion_model.eps_model.condition(data)
        data["cond"] = condition

        output = self.diffusion_model.eps_model(x_t, ts, data)

        if self.diffusion_model.pred_x0:
            pred_dict = {
                "pred_pose_norm": output,
                "noise": noise
            }
        else:
            pred_x0 = self.diffusion_model._compute_pred_x0_from_noise(x_t, ts, output)
            pred_dict = {
                "pred_noise": output,
                "pred_pose_norm": pred_x0,
                "noise": noise,
            }
        outputs, targets = self.criterion.get_hand_model_pose(pred_dict, data)
        outputs, targets = self.criterion.get_hand(outputs, targets)

        return outputs, targets

    def _sample_timesteps(self, batch_size):
        if self.rand_t_type == 'all':
            return torch.randint(0, self.diffusion_model.timesteps, (batch_size,), device=self.device).long()
        elif self.rand_t_type == 'half':
            ts = torch.randint(0, self.diffusion_model.timesteps, ((batch_size + 1) // 2,), device=self.device)
            if batch_size % 2 == 1:
                return torch.cat([ts, self.diffusion_model.timesteps - ts[:-1] - 1], dim=0).long()
            else:
                return torch.cat([ts, self.diffusion_model.timesteps - ts - 1], dim=0).long()
        else:
            raise ValueError(f'Unsupported rand_t_type: {self.rand_t_type}. Expected "all" or "half".')

    def on_load_checkpoint(self, checkpoint: Dict[str, Any]) -> None:
        if hasattr(self, 'use_score') and self.use_score and not self.score_pretrain:
            model_state_dict = self.state_dict()
            checkpoint_state_dict = checkpoint['state_dict']
            
            new_state_dict = {}
            for key, value in checkpoint_state_dict.items():
                if 'score_heads' not in key:
                    new_state_dict[key] = value
                    
            model_state_dict.update(new_state_dict)
            self.load_state_dict(model_state_dict)
            logging.info("Loaded checkpoint weights (excluding score_heads)")
        else:
            current_state = self.state_dict()
            different = self._check_state_dict(checkpoint['state_dict'], current_state)
            
            if different:
                logging.warning("State dict inconsistency detected!")
                input("Press ENTER to continue or Ctrl-C to interrupt")
            else:
                logging.info("Checkpoint state dict is CONSISTENT with model state dict")
                logging.info("Loading full checkpoint")

    def _check_state_dict(self, dict1, dict2):
        if dict1.keys() != dict2.keys():
            logging.warning("Keys mismatch!")
            logging.warning(f"Only in dict1: {set(dict1.keys()) - set(dict2.keys())}")
            logging.warning(f"Only in dict2: {set(dict2.keys()) - set(dict1.keys())}")
            return True
        
        for key in dict1:
            if dict1[key].shape != dict2[key].shape:
                logging.warning(f"Shape mismatch for {key}!")
                logging.warning(f"Shape in dict1: {dict1[key].shape}")
                logging.warning(f"Shape in dict2: {dict2[key].shape}")
                return True
        
        return False

    def _convert_number_to_emoji(self, number_str: str) -> str:
        number_emoji_map = {
            '0': '0️⃣ ',
            '1': '1️⃣ ',
            '2': '2️⃣ ',
            '3': '3️⃣ ',
            '4': '4️⃣ ',
            '5': '5️⃣ ',
            '6': '6️⃣ ',
            '7': '7️⃣ ',
            '8': '8️⃣ ',
            '9': '9️⃣ ',
            '.': '🔸'
        }
        return ''.join(number_emoji_map.get(char, char) for char in number_str)