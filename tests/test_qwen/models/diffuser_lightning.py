import pytorch_lightning as pl
import torch
from typing import Dict, Optional, Tuple, Any, List
import logging
from utils.log_colors import GREEN, ENDC
from models.lightning.ddpm_lightning import DDPMLightning as NewDDPMLightning

class DDPMLightning(pl.LightningModule):
    """向后兼容的DDPMLightning包装器"""
    
    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning compatibility wrapper{ENDC}")
        self._ddpm_lightning = NewDDPMLightning(cfg)
        # 复制属性以保持向后兼容性
        self.__dict__.update(self._ddpm_lightning.__dict__)
        
    def training_step(self, batch, batch_idx):
        return self._ddpm_lightning.training_step(batch, batch_idx)
        
    def validation_step(self, batch, batch_idx):
        return self._ddpm_lightning.validation_step(batch, batch_idx)
        
    def test_step(self, batch, batch_idx):
        return self._ddpm_lightning.test_step(batch, batch_idx)
        
    def configure_optimizers(self):
        return self._ddpm_lightning.configure_optimizers()
        
    def on_validation_epoch_start(self):
        return self._ddpm_lightning.on_validation_epoch_start()
        
    def on_validation_epoch_end(self):
        return self._ddpm_lightning.on_validation_epoch_end()
        
    def on_test_start(self):
        return self._ddpm_lightning.on_test_start()
        
    def on_load_checkpoint(self, checkpoint: Dict[str, Any]) -> None:
        return self._ddpm_lightning.on_load_checkpoint(checkpoint)
        
    def forward_infer(self, data: Dict, k=4, timestep=-1):
        return self._ddpm_lightning.forward_infer(data, k, timestep)
        
    def forward_infer_step(self, data: Dict, k=4, timesteps=[1, 3, 5, 7, 9, 11, 91, 93, 95, 97, -1]):
        return self._ddpm_lightning.forward_infer_step(data, k, timesteps)
        
    def forward_get_pose(self, data: Dict, k=4):
        return self._ddpm_lightning.forward_get_pose(data, k)
        
    def forward_get_pose_matched(self, data: Dict, k=4):
        return self._ddpm_lightning.forward_get_pose_matched(data, k)
        
    def forward_get_pose_raw(self, data: Dict, k=4):
        return self._ddpm_lightning.forward_get_pose_raw(data, k)
        
    def forward_train_instance(self, data: Dict):
        return self._ddpm_lightning.forward_train_instance(data)
        
    def test_step_teaser(self, batch):
        return self._ddpm_lightning.test_step_teaser(batch)
        
    def sample(self, data: Dict, k: int = 1, use_cfg: bool = None, guidance_scale: float = None,
               use_negative_guidance: bool = None, negative_guidance_scale: float = None) -> torch.Tensor:
        return self._ddpm_lightning.diffusion_model.sample(data, k, use_cfg, guidance_scale, 
                                                           use_negative_guidance, negative_guidance_scale)
        
    def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        return self._ddpm_lightning.diffusion_model.q_sample(x0, t, noise)
        
    def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        return self._ddpm_lightning.diffusion_model.model_predict(x_t, t, data)
        
    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self._ddpm_lightning.diffusion_model.p_mean_variance(x_t, t, data)
        
    def p_mean_variance_cfg(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self._ddpm_lightning.diffusion_model.p_mean_variance_cfg(x_t, t, data)
        
    def p_sample(self, x_t: torch.Tensor, t: int, data: Dict) -> torch.Tensor:
        return self._ddpm_lightning.diffusion_model.p_sample(x_t, t, data)
        
    def p_sample_loop(self, data: Dict) -> torch.Tensor:
        return self._ddpm_lightning.diffusion_model.p_sample_loop(data)