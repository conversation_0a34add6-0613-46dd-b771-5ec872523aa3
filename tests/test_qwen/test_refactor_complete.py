import sys
import os
import torch
import torch.nn as nn
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建一个模拟的配置对象
class Config:
    def __init__(self):
        self.steps = 100
        self.schedule_cfg = {}
        self.pred_x0 = False
        self.use_cfg = False
        self.guidance_scale = 7.5
        self.use_negative_guidance = False
        self.negative_guidance_scale = 1.0
        self.rand_t_type = 'all'
        self.mode = 'train'
        self.rot_type = 'quat'
        self.batch_size = 4
        self.print_freq = 10
        
        # 模拟优化器配置
        class Optimizer:
            def __init__(self):
                self.name = 'adam'
                self.lr = 1e-4
                self.weight_decay = 1e-5
                
        class Scheduler:
            def __init__(self):
                self.name = 'cosine'
                self.t_max = 100
                self.min_lr = 1e-6
                
        self.optimizer = Optimizer()
        self.scheduler = Scheduler()
        
        # 模拟损失函数配置
        class Criterion:
            def __init__(self):
                self.loss_weights = {'loss': 1.0}
                
        self.criterion = Criterion()
        
        # 模拟解码器配置
        class Decoder:
            def __init__(self):
                pass
                
        self.decoder = Decoder()

# 测试导入
try:
    from models.diffuser_lightning import DDPMLightning
    print("✅ 向后兼容包装器导入成功")
    
    from models.diffusion.ddpm_model import DDPMModel
    print("✅ DDPMModel 核心模型导入成功")
    
    from models.lightning.ddpm_lightning import DDPMLightning as NewDDPMLightning
    print("✅ NewDDPMLightning 训练系统导入成功")
    
    # 创建模拟的eps_model
    class MockEpsModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.net = nn.Sequential(
                nn.Linear(64, 128),
                nn.ReLU(),
                nn.Linear(128, 64)
            )
        
        def forward(self, x_t, t, data):
            # 忽略 t 和 data 参数，只使用 x_t
            return self.net(x_t)
    
    # 添加一个模拟的condition方法
    def condition(data):
        return {}
    
    eps_model = MockEpsModel()
    eps_model.condition = condition
    
    # 测试DDPMModel核心功能
    cfg = Config()
    diffusion_model = DDPMModel(
        eps_model=eps_model,
        timesteps=cfg.steps,
        schedule_cfg=cfg.schedule_cfg,
        pred_x0=cfg.pred_x0,
        use_cfg=cfg.use_cfg,
        guidance_scale=cfg.guidance_scale,
        use_negative_guidance=cfg.use_negative_guidance,
        negative_guidance_scale=cfg.negative_guidance_scale
    )
    
    print("✅ DDPMModel实例化成功")
    
    # 测试前向扩散
    x0 = torch.randn(2, 64)
    t = torch.tensor([50, 75])
    noise = torch.randn_like(x0)
    x_t = diffusion_model.q_sample(x0, t, noise)
    print(f"✅ 前向扩散测试成功，输出形状: {x_t.shape}")
    
    # 测试模型预测（使用pred_x0=True来避免调用eps_model）
    diffusion_model.pred_x0 = True
    pred_noise, pred_x0 = diffusion_model.model_predict(x_t, t, {})
    print(f"✅ 模型预测测试成功，预测噪声形状: {pred_noise.shape}, 预测x0形状: {pred_x0.shape}")
    
    print("\n✅ 所有模块和核心功能测试通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()