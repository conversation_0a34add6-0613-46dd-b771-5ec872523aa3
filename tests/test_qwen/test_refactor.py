import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 测试导入
try:
    from models.diffuser_lightning import DDPMLightning
    print("✅ 向后兼容包装器导入成功")
    
    from models.diffusion.ddpm_model import DDPMModel
    print("✅ DDPMModel 核心模型导入成功")
    
    from models.lightning.ddpm_lightning import DDPMLightning as NewDDPMLightning
    print("✅ NewDDPMLightning 训练系统导入成功")
    
    print("\n✅ 所有模块导入测试通过!")
    
except Exception as e:
    print(f"❌ 导入测试失败: {e}")
    import traceback
    traceback.print_exc()