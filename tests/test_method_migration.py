#!/usr/bin/env python3
"""
测试方法迁移的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel
from utils.hand_physics import HandPhysics
from utils.hand_types import HandModelType

# 检查CUDA是否可用
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

def test_sample_contact_points_migration():
    """测试sample_contact_points方法迁移"""
    print("Testing sample_contact_points migration...")
    
    hand_model = HandModel(device=DEVICE)
    
    # 测试通过HandModel调用
    batch_size = 3
    n_contacts_per_finger = 2
    contact_indices = hand_model.sample_contact_points(batch_size, n_contacts_per_finger)
    
    # 检查输出形状
    expected_shape = (batch_size, hand_model.n_fingers * n_contacts_per_finger)
    assert contact_indices.shape == expected_shape, f"Expected {expected_shape}, got {contact_indices.shape}"
    
    # 检查输出类型
    assert contact_indices.dtype == torch.long, f"Expected torch.long, got {contact_indices.dtype}"
    
    # 测试直接通过HandPhysics调用
    physics_result = hand_model.physics.sample_contact_points(batch_size, n_contacts_per_finger)
    
    # 由于是随机采样，我们只检查形状和类型一致性
    assert physics_result.shape == contact_indices.shape
    assert physics_result.dtype == contact_indices.dtype
    
    print("✓ sample_contact_points migration test passed")

def test_decompose_hand_pose_migration():
    """测试decompose_hand_pose方法迁移"""
    print("Testing decompose_hand_pose migration...")
    
    hand_model = HandModel(device=DEVICE, rot_type="quat")
    
    # 创建测试数据
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4  # translation + joints + quaternion
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    
    # 测试通过HandModel调用
    trans1, rot1, qpos1 = hand_model.decompose_hand_pose(hand_pose)
    
    # 测试直接通过HandPhysics静态方法调用
    trans2, rot2, qpos2 = HandPhysics.decompose_hand_pose(hand_pose, hand_model.n_dofs, "quat")
    
    # 检查结果一致性
    assert torch.allclose(trans1, trans2, atol=1e-6), "Translation decomposition mismatch"
    assert torch.allclose(rot1, rot2, atol=1e-6), "Rotation decomposition mismatch"
    assert torch.allclose(qpos1, qpos2, atol=1e-6), "Joint angles decomposition mismatch"
    
    # 检查输出形状
    assert trans1.shape == (batch_size, 3)
    assert rot1.shape == (batch_size, 3, 3)
    assert qpos1.shape == (batch_size, hand_model.n_dofs)
    
    print("✓ decompose_hand_pose migration test passed")

def test_different_rotation_types():
    """测试不同旋转类型的decompose_hand_pose"""
    print("Testing decompose_hand_pose with different rotation types...")
    
    batch_size = 2
    n_dofs = 16  # LEAP hand DOFs
    
    # 测试不同的旋转类型
    rotation_types = {
        'quat': 4,
        'r6d': 6,
        'axis': 3,
        'euler': 3
    }
    
    for rot_type, rot_dim in rotation_types.items():
        pose_dim = 3 + n_dofs + rot_dim
        hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
        
        try:
            trans, rot, qpos = HandPhysics.decompose_hand_pose(hand_pose, n_dofs, rot_type)
            
            # 检查输出形状
            assert trans.shape == (batch_size, 3), f"Wrong translation shape for {rot_type}"
            assert rot.shape == (batch_size, 3, 3), f"Wrong rotation shape for {rot_type}"
            assert qpos.shape == (batch_size, n_dofs), f"Wrong qpos shape for {rot_type}"
            
            print(f"  ✓ {rot_type} rotation type works correctly")
            
        except Exception as e:
            print(f"  ✗ {rot_type} rotation type failed: {e}")
            raise

    print("✓ Different rotation types test passed")

def test_method_accessibility():
    """测试方法的可访问性"""
    print("Testing method accessibility...")
    
    hand_model = HandModel(device=DEVICE)
    
    # 检查方法是否可以通过HandModel访问
    assert hasattr(hand_model, 'sample_contact_points'), "sample_contact_points not accessible via HandModel"
    assert hasattr(hand_model, 'decompose_hand_pose'), "decompose_hand_pose not accessible via HandModel"
    assert callable(hand_model.sample_contact_points), "sample_contact_points not callable"
    assert callable(hand_model.decompose_hand_pose), "decompose_hand_pose not callable"
    
    # 检查方法是否可以通过HandPhysics访问
    assert hasattr(hand_model.physics, 'sample_contact_points'), "sample_contact_points not accessible via HandPhysics"
    assert hasattr(HandPhysics, 'decompose_hand_pose'), "decompose_hand_pose not accessible as static method"
    assert callable(hand_model.physics.sample_contact_points), "sample_contact_points not callable via HandPhysics"
    assert callable(HandPhysics.decompose_hand_pose), "decompose_hand_pose not callable as static method"
    
    print("✓ Method accessibility test passed")

def test_error_handling():
    """测试错误处理"""
    print("Testing error handling...")
    
    # 测试错误的旋转类型
    try:
        hand_pose = torch.randn(2, 23, device=DEVICE)  # 假设的维度
        HandPhysics.decompose_hand_pose(hand_pose, 16, "invalid_rot_type")
        assert False, "Should raise error for invalid rotation type"
    except ValueError as e:
        assert "Unsupported rotation type" in str(e)
    
    # 测试错误的手部姿态维度
    try:
        hand_pose = torch.randn(2, 10, device=DEVICE)  # 错误的维度
        HandPhysics.decompose_hand_pose(hand_pose, 16, "quat")
        assert False, "Should raise error for wrong pose dimensions"
    except AssertionError:
        pass  # 预期的错误
    
    print("✓ Error handling test passed")

def main():
    """运行所有迁移测试"""
    print("Starting method migration validation tests...\n")
    
    try:
        test_sample_contact_points_migration()
        test_decompose_hand_pose_migration()
        test_different_rotation_types()
        test_method_accessibility()
        test_error_handling()
        
        print("\n🎉 All method migration tests passed!")
        print("✅ sample_contact_points has been successfully migrated to HandPhysics")
        print("✅ decompose_hand_pose has been successfully migrated to HandPhysics")
        print("✅ set_parameters remains in HandModel (as it should)")
        
    except Exception as e:
        print(f"\n❌ Method migration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
