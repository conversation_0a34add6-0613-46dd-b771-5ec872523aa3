#!/usr/bin/env python3
"""
缓存系统改进方案测试

这个脚本提供了一个改进的缓存创建策略，解决中途中断导致的缓存损坏问题。
"""

import os
import h5py
import logging
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Callable

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

class ImprovedCacheManager:
    """
    改进的缓存管理器，解决中途中断导致的缓存损坏问题
    
    主要改进：
    1. 使用临时文件 + 原子性重命名
    2. 添加缓存完整性标记
    3. 更好的错误处理和清理
    """
    
    def __init__(self, cache_path: str, num_items: int):
        self.cache_path = cache_path
        self.num_items = num_items
        self.temp_cache_path = cache_path + ".tmp"
        self.hf: Optional[h5py.File] = None
        self.cache_loaded = False
    
    def is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not os.path.exists(self.cache_path):
            return False
        
        try:
            with h5py.File(self.cache_path, 'r') as hf:
                # 检查项目数量（排除元数据组）
                actual_items = len(hf) - (1 if 'cache_metadata' in hf else 0)
                if actual_items != self.num_items:
                    logger.warning(f"缓存项目数量不匹配: {actual_items} != {self.num_items}")
                    return False
                
                # 检查完整性标记
                if 'cache_metadata' in hf:
                    metadata = hf['cache_metadata']
                    if 'is_complete' in metadata.attrs:
                        is_complete = metadata.attrs['is_complete']
                        if not is_complete:
                            logger.warning("缓存未完成创建")
                            return False
                    else:
                        logger.warning("缺少完整性标记")
                        return False
                else:
                    logger.warning("缺少元数据组")
                    return False
                
                logger.info(f"缓存验证通过: {actual_items} 个项目")
                return True
                
        except Exception as e:
            logger.error(f"缓存验证失败: {e}")
            return False
    
    def create_cache_atomic(self, data_creator_func: Callable[[str, int], None]) -> bool:
        """
        原子性创建缓存文件
        
        Args:
            data_creator_func: 数据创建函数，接受(cache_path, num_items)参数
            
        Returns:
            bool: 创建是否成功
        """
        try:
            logger.info(f"开始原子性创建缓存: {self.cache_path}")
            
            # 清理可能存在的临时文件
            if os.path.exists(self.temp_cache_path):
                os.remove(self.temp_cache_path)
                logger.info("清理了旧的临时缓存文件")
            
            # 在临时文件中创建缓存
            logger.info(f"在临时文件中创建缓存: {self.temp_cache_path}")
            
            # 创建临时缓存文件并添加元数据
            with h5py.File(self.temp_cache_path, 'w') as temp_hf:
                # 创建元数据组
                metadata_group = temp_hf.create_group('cache_metadata')
                metadata_group.attrs['num_items'] = self.num_items
                metadata_group.attrs['is_complete'] = False  # 标记为未完成
                metadata_group.attrs['creation_timestamp'] = str(os.path.getmtime(self.temp_cache_path))
            
            # 调用数据创建函数填充数据
            logger.info("开始填充缓存数据...")
            data_creator_func(self.temp_cache_path, self.num_items)
            
            # 标记缓存为完成状态
            with h5py.File(self.temp_cache_path, 'r+') as temp_hf:
                if 'cache_metadata' in temp_hf:
                    temp_hf['cache_metadata'].attrs['is_complete'] = True
                    logger.info("标记缓存为完成状态")
            
            # 验证临时缓存
            temp_manager = ImprovedCacheManager(self.temp_cache_path, self.num_items)
            if not temp_manager.is_cache_valid():
                raise Exception("临时缓存验证失败")
            
            # 原子性重命名（这是关键步骤）
            logger.info("执行原子性重命名...")
            shutil.move(self.temp_cache_path, self.cache_path)
            
            logger.info("缓存创建成功")
            return True
            
        except Exception as e:
            logger.error(f"缓存创建失败: {e}")
            
            # 清理临时文件
            if os.path.exists(self.temp_cache_path):
                try:
                    os.remove(self.temp_cache_path)
                    logger.info("清理了失败的临时缓存文件")
                except Exception as cleanup_e:
                    logger.warning(f"清理临时文件失败: {cleanup_e}")
            
            return False
    
    def load_cache(self) -> bool:
        """加载缓存文件"""
        try:
            if not self.is_cache_valid():
                return False
            
            self.hf = h5py.File(self.cache_path, 'r')
            self.cache_loaded = True
            actual_items = len(self.hf) - (1 if 'cache_metadata' in self.hf else 0)
            logger.info(f"缓存加载成功: {actual_items} 个项目")
            return True
            
        except Exception as e:
            logger.error(f"缓存加载失败: {e}")
            self.hf = None
            self.cache_loaded = False
            return False
    
    def cleanup_corrupted_cache(self) -> bool:
        """清理损坏的缓存文件"""
        try:
            if os.path.exists(self.cache_path):
                os.remove(self.cache_path)
                logger.info(f"清理了损坏的缓存文件: {self.cache_path}")
            
            if os.path.exists(self.temp_cache_path):
                os.remove(self.temp_cache_path)
                logger.info(f"清理了临时缓存文件: {self.temp_cache_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理缓存文件失败: {e}")
            return False

def dummy_data_creator(cache_path: str, num_items: int) -> None:
    """
    示例数据创建函数
    
    Args:
        cache_path: 缓存文件路径
        num_items: 项目数量
    """
    logger.info(f"开始创建 {num_items} 个示例数据项...")
    
    with h5py.File(cache_path, 'r+') as hf:
        for i in range(num_items):
            # 创建示例数据组
            group = hf.create_group(str(i))
            group.create_dataset('data', data=[i, i*2, i*3])
            group.create_dataset('label', data=i % 10)
            
            if i % 100 == 0:
                logger.info(f"已创建 {i+1}/{num_items} 个数据项")
    
    logger.info(f"数据创建完成: {num_items} 个项目")

def test_improved_cache_system():
    """测试改进的缓存系统"""
    logger.info("开始测试改进的缓存系统...")
    
    # 创建测试缓存路径
    test_cache_dir = "/tmp/test_cache_improvement"
    os.makedirs(test_cache_dir, exist_ok=True)
    test_cache_path = os.path.join(test_cache_dir, "test_cache.h5")
    
    # 清理可能存在的测试文件
    if os.path.exists(test_cache_path):
        os.remove(test_cache_path)
    
    try:
        # 测试缓存创建
        cache_manager = ImprovedCacheManager(test_cache_path, 1000)
        
        logger.info("测试1: 正常缓存创建")
        success = cache_manager.create_cache_atomic(dummy_data_creator)
        assert success, "缓存创建应该成功"
        
        logger.info("测试2: 缓存验证")
        assert cache_manager.is_cache_valid(), "缓存应该有效"
        
        logger.info("测试3: 缓存加载")
        assert cache_manager.load_cache(), "缓存加载应该成功"
        assert cache_manager.cache_loaded, "缓存应该已加载"
        
        logger.info("测试4: 数据完整性检查")
        actual_items = len(cache_manager.hf) - (1 if 'cache_metadata' in cache_manager.hf else 0)
        assert actual_items == 1000, f"缓存项目数量应该正确: {actual_items} != 1000"
        
        # 关闭文件句柄
        if cache_manager.hf:
            cache_manager.hf.close()
        
        logger.info("所有测试通过！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise
    
    finally:
        # 清理测试文件
        if os.path.exists(test_cache_path):
            os.remove(test_cache_path)
        if os.path.exists(test_cache_dir):
            os.rmdir(test_cache_dir)

if __name__ == "__main__":
    test_improved_cache_system()
