#!/usr/bin/env python3
"""
测试缓存系统在中断时的安全性

这个脚本用于测试：
1. 文件句柄是否会泄漏
2. 是否会产生僵尸进程
3. 中断后文件是否可以正常访问
"""

import os
import h5py
import logging
import sys
import signal
import time
import threading
import psutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(name)s] {%(filename)s:%(lineno)d} [%(funcName)s] <%(levelname)s> - %(message)s'
)
logger = logging.getLogger(__name__)

class InterruptibleCacheCreator:
    """可中断的缓存创建器，模拟当前系统的行为"""
    
    def __init__(self, cache_path: str, num_items: int):
        self.cache_path = cache_path
        self.num_items = num_items
        self.interrupted = False
        self.hf = None
        
    def create_cache_current_method(self):
        """使用当前系统的方法创建缓存（可能有问题）"""
        logger.info(f"开始创建缓存: {self.cache_path}")
        
        try:
            # 模拟当前系统的缓存创建方式
            with h5py.File(self.cache_path, 'w') as hf:
                self.hf = hf  # 保存引用（这可能导致问题）
                
                for idx in range(self.num_items):
                    if self.interrupted:
                        logger.warning("检测到中断信号，停止创建")
                        break
                    
                    # 模拟数据创建
                    group = hf.create_group(str(idx))
                    group.create_dataset('data', data=[idx, idx*2, idx*3])
                    
                    # 模拟处理时间
                    time.sleep(0.01)
                    
                    if idx % 100 == 0:
                        logger.info(f"已创建 {idx+1}/{self.num_items} 个项目")
                
                logger.info("缓存创建完成")
                
        except KeyboardInterrupt:
            logger.warning("收到键盘中断信号")
            self.interrupted = True
            raise
        except Exception as e:
            logger.error(f"缓存创建出错: {e}")
            raise
        finally:
            # 清理文件句柄引用
            self.hf = None
    
    def create_cache_improved_method(self):
        """使用改进的方法创建缓存（更安全）"""
        logger.info(f"开始创建缓存（改进方法）: {self.cache_path}")
        temp_path = self.cache_path + ".tmp"
        
        try:
            # 清理可能存在的临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            # 在临时文件中创建缓存
            with h5py.File(temp_path, 'w') as hf:
                # 添加元数据
                metadata = hf.create_group('cache_metadata')
                metadata.attrs['num_items'] = self.num_items
                metadata.attrs['is_complete'] = False
                
                for idx in range(self.num_items):
                    if self.interrupted:
                        logger.warning("检测到中断信号，停止创建")
                        raise KeyboardInterrupt("用户中断")
                    
                    # 创建数据
                    group = hf.create_group(str(idx))
                    group.create_dataset('data', data=[idx, idx*2, idx*3])
                    
                    time.sleep(0.01)
                    
                    if idx % 100 == 0:
                        logger.info(f"已创建 {idx+1}/{self.num_items} 个项目")
                
                # 标记为完成
                metadata.attrs['is_complete'] = True
                logger.info("缓存数据创建完成，准备原子性重命名")
            
            # 原子性重命名
            os.rename(temp_path, self.cache_path)
            logger.info("缓存创建完成（改进方法）")
            
        except KeyboardInterrupt:
            logger.warning("收到键盘中断信号（改进方法）")
            self.interrupted = True
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
                logger.info("清理了临时文件")
            raise
        except Exception as e:
            logger.error(f"缓存创建出错（改进方法）: {e}")
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise

def check_file_handles():
    """检查当前进程的文件句柄数量"""
    try:
        process = psutil.Process()
        open_files = process.open_files()
        logger.info(f"当前进程打开的文件数量: {len(open_files)}")
        
        # 检查是否有HDF5文件句柄
        hdf5_files = [f for f in open_files if f.path.endswith('.h5')]
        if hdf5_files:
            logger.warning(f"发现未关闭的HDF5文件句柄: {[f.path for f in hdf5_files]}")
            return False
        else:
            logger.info("没有发现未关闭的HDF5文件句柄")
            return True
            
    except Exception as e:
        logger.error(f"检查文件句柄失败: {e}")
        return False

def check_zombie_processes():
    """检查是否有僵尸进程"""
    try:
        zombie_count = 0
        for proc in psutil.process_iter(['pid', 'status', 'name']):
            if proc.info['status'] == psutil.STATUS_ZOMBIE:
                zombie_count += 1
                logger.warning(f"发现僵尸进程: PID={proc.info['pid']}, Name={proc.info['name']}")
        
        if zombie_count == 0:
            logger.info("没有发现僵尸进程")
            return True
        else:
            logger.warning(f"发现 {zombie_count} 个僵尸进程")
            return False
            
    except Exception as e:
        logger.error(f"检查僵尸进程失败: {e}")
        return False

def test_file_access_after_interrupt(cache_path: str):
    """测试中断后文件是否可以正常访问"""
    logger.info(f"测试文件访问: {cache_path}")
    
    if not os.path.exists(cache_path):
        logger.info("缓存文件不存在，跳过访问测试")
        return True
    
    try:
        # 尝试读取访问
        with h5py.File(cache_path, 'r') as hf:
            logger.info(f"文件读取正常，包含 {len(hf)} 个项目")
        
        # 尝试写入访问
        with h5py.File(cache_path, 'r+') as hf:
            # 尝试添加一个测试属性
            if 'test_access' not in hf.attrs:
                hf.attrs['test_access'] = 'ok'
                logger.info("文件写入访问正常")
        
        return True
        
    except Exception as e:
        logger.error(f"文件访问失败: {e}")
        return False

def simulate_interrupt_test(method='current'):
    """模拟中断测试"""
    test_cache_dir = "/tmp/test_interrupt_safety"
    os.makedirs(test_cache_dir, exist_ok=True)
    cache_path = os.path.join(test_cache_dir, f"test_cache_{method}.h5")
    
    # 清理可能存在的测试文件
    if os.path.exists(cache_path):
        os.remove(cache_path)
    
    creator = InterruptibleCacheCreator(cache_path, 1000)
    
    def interrupt_after_delay():
        """延迟后发送中断信号"""
        time.sleep(2)  # 让缓存创建运行2秒
        logger.info("发送中断信号...")
        creator.interrupted = True
    
    try:
        logger.info(f"开始测试 {method} 方法...")
        
        # 启动中断线程
        interrupt_thread = threading.Thread(target=interrupt_after_delay)
        interrupt_thread.start()
        
        # 开始创建缓存
        if method == 'current':
            creator.create_cache_current_method()
        else:
            creator.create_cache_improved_method()
            
    except KeyboardInterrupt:
        logger.info("捕获到中断信号")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    
    # 等待中断线程结束
    interrupt_thread.join()
    
    # 检查系统状态
    logger.info("检查中断后的系统状态...")
    handles_ok = check_file_handles()
    zombies_ok = check_zombie_processes()
    access_ok = test_file_access_after_interrupt(cache_path)
    
    # 清理测试文件
    if os.path.exists(cache_path):
        os.remove(cache_path)
    
    return handles_ok and zombies_ok and access_ok

def main():
    """主函数"""
    logger.info("开始缓存中断安全性测试...")
    
    logger.info("=" * 60)
    logger.info("测试1: 当前方法的中断安全性")
    logger.info("=" * 60)
    current_ok = simulate_interrupt_test('current')
    
    logger.info("=" * 60)
    logger.info("测试2: 改进方法的中断安全性")
    logger.info("=" * 60)
    improved_ok = simulate_interrupt_test('improved')
    
    logger.info("=" * 60)
    logger.info("测试结果总结")
    logger.info("=" * 60)
    logger.info(f"当前方法安全性: {'✅ 通过' if current_ok else '❌ 失败'}")
    logger.info(f"改进方法安全性: {'✅ 通过' if improved_ok else '❌ 失败'}")
    
    if not current_ok:
        logger.warning("当前缓存系统在中断时可能存在文件句柄泄漏或僵尸进程问题")
        logger.info("建议实施改进的缓存创建方法")

if __name__ == "__main__":
    main()
