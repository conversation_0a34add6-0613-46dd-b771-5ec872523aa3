#!/usr/bin/env python3
"""
详细测试抓取采样过程
"""

import os
import sys
import torch
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

def analyze_grasp_sampling():
    """分析抓取采样的详细过程"""
    
    print("=" * 80)
    print("分析 SceneLeapPlusDataset 的抓取采样机制")
    print("=" * 80)
    
    # 创建数据集实例
    config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "num_grasps": 4,
        "mode": "camera_centric",
        "max_grasps_per_object": 1024,
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus_sampling",
        "cache_mode": "train"
    }
    
    dataset = SceneLeapPlusDatasetCached(**config)
    
    print(f"数据集配置:")
    print(f"  - 数据集长度: {len(dataset)}")
    print(f"  - num_grasps: {dataset.num_grasps}")
    print(f"  - max_grasps_per_object: {dataset.max_grasps_per_object}")
    print(f"  - grasp_sampling_strategy: {dataset.grasp_sampling_strategy}")
    
    # 分析数据集结构
    print(f"\n数据集结构分析:")
    
    # 统计场景和物体
    scenes = set()
    objects = set()
    scene_object_views = {}
    
    for i, item_data in enumerate(dataset.data[:20]):  # 只看前20个
        scene_id = item_data['scene_id']
        object_code = item_data['object_code']
        view_idx = item_data['depth_view_index']
        
        scenes.add(scene_id)
        objects.add(object_code)
        
        key = f"{scene_id}_{object_code}"
        if key not in scene_object_views:
            scene_object_views[key] = []
        scene_object_views[key].append(view_idx)
    
    print(f"  - 前20个数据项涉及场景数: {len(scenes)}")
    print(f"  - 前20个数据项涉及物体数: {len(objects)}")
    print(f"  - 场景-物体组合数: {len(scene_object_views)}")
    
    # 显示每个场景-物体组合的视角数
    print(f"\n场景-物体组合的视角分布:")
    for key, views in list(scene_object_views.items())[:5]:
        print(f"  - {key}: {len(views)} 个视角 {sorted(views)}")
    
    # 分析抓取数据
    print(f"\n抓取数据分析:")
    
    # 检查手部姿态数据
    if hasattr(dataset, 'hand_pose_data'):
        print(f"  - 手部姿态数据结构:")
        scene_count = 0
        total_objects = 0
        total_grasps = 0
        
        for scene_id, scene_data in list(dataset.hand_pose_data.items())[:3]:
            scene_count += 1
            scene_objects = len(scene_data)
            total_objects += scene_objects
            
            scene_grasps = 0
            for obj_code, poses in scene_data.items():
                if isinstance(poses, torch.Tensor):
                    scene_grasps += poses.shape[0]
            
            total_grasps += scene_grasps
            print(f"    场景 {scene_id}: {scene_objects} 个物体, {scene_grasps} 个抓取")
        
        print(f"  - 总计（前3个场景）: {scene_count} 个场景, {total_objects} 个物体, {total_grasps} 个抓取")
    
    # 测试采样过程
    print(f"\n采样过程测试:")
    
    # 获取几个样本并分析
    for i in [0, 1, 2]:
        sample = dataset[i]
        if 'error' not in sample:
            item_data = dataset.data[i]
            print(f"\n  样本 {i}:")
            print(f"    - 场景: {item_data['scene_id']}")
            print(f"    - 物体: {item_data['object_code']}")
            print(f"    - 视角: {item_data['depth_view_index']}")
            print(f"    - 返回的抓取形状: {sample['hand_model_pose'].shape}")
            print(f"    - SE3 矩阵形状: {sample['se3'].shape}")
            
            # 检查抓取姿态的多样性（通过查看前几个参数）
            hand_poses = sample['hand_model_pose']
            if hand_poses.shape[0] > 1:
                # 计算第一个抓取和其他抓取的差异
                first_pose = hand_poses[0]
                differences = []
                for j in range(1, min(4, hand_poses.shape[0])):
                    diff = torch.norm(hand_poses[j] - first_pose).item()
                    differences.append(diff)
                print(f"    - 抓取间差异: {[f'{d:.3f}' for d in differences]}")
        else:
            print(f"  样本 {i}: 错误 - {sample['error']}")
    
    # 解释采样策略
    print(f"\n采样策略解释:")
    print(f"  当前策略: {dataset.grasp_sampling_strategy}")
    
    if dataset.grasp_sampling_strategy == "farthest_point":
        print("  - farthest_point: 基于抓取位置的最远点采样")
        print("  - 选择在3D空间中分布最分散的抓取")
        print("  - 有助于提高抓取的多样性")
    elif dataset.grasp_sampling_strategy == "random":
        print("  - random: 随机采样")
        print("  - 每次访问同一数据项可能返回不同的抓取组合")
    elif dataset.grasp_sampling_strategy == "first_n":
        print("  - first_n: 选择前N个抓取")
        print("  - 确定性采样，每次返回相同的抓取")
    
    print(f"\n关键理解:")
    print(f"  1. 数据集长度 = (场景, 物体, 视角) 组合数，与 num_grasps 无关")
    print(f"  2. 每个数据项从该物体的所有可用抓取中采样 {dataset.num_grasps} 个")
    print(f"  3. max_grasps_per_object 限制每个物体最多使用多少个抓取")
    print(f"  4. 采样策略决定如何从可用抓取中选择")
    print(f"  5. 这种设计支持并行多抓取学习，而不是传统的单抓取学习")
    
    # 清理
    if hasattr(dataset, '_cleanup'):
        dataset._cleanup()

if __name__ == "__main__":
    analyze_grasp_sampling()
