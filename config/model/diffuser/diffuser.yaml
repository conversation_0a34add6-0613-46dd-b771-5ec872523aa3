#  项目与框架配置 
defaults:
  - decoder: unet_cleaned         # 加载 'decoder/unet_cleand.yaml'
  - criterion: loss       # 加载 'criterion/loss.yaml'
  - _self_                # 允许命令行覆盖

name: GraspDiffuser         # 实验名称
save_root: ${save_root}      # 结果保存根目录
device: cuda:0              # 运行设备 (通常由Pytorch Lightning自动管理)


#  模型与数据配置 
rot_type: ${rot_type}        # 旋转表示方法 ('6d', 'quat', etc.)
mode: ${mode}                # 运行模式 ('train', 'test', etc.)
print_freq: 250             # 日志打印频率
batch_size: ${batch_size}    # 批处理大小


#  扩散过程配置 
steps: 100                  # 扩散总步数 T
pred_x0: true               # 预测目标 (true: x0, false: 噪声)
rand_t_type: half           # 时间步t的采样策略

schedule_cfg:
  beta:                     # beta 范围
    - 0.0001                # beta 起始值
    - 0.01                  # beta 结束值
  beta_schedule: linear     # 噪声调度类型
  s: 0.008                  # cosine 调度参数


#  训练与优化配置 
optimizer:
  name: adam                # 优化器
  lr: 0.0001                # 学习率
  weight_decay: 0.0001      # 权重衰减

scheduler:
  name: cosine              # 学习率调度器
  t_max: 1000               # cosine 调度周期
  min_lr: 1.0e-05           # 最小学习率

loss_type: l2               # 损失类型 (传递给criterion)
out_sigmoid: false          # 解码器输出激活 (传递给decoder)
use_score: false            # 是否启用分数模型
score_pretrain: false       # 分数模型是否已预训练


#  无分类器引导 (CFG) 配置 
use_cfg: true                 # 是否启用CFG (推理时)
guidance_scale: 7.5           # CFG引导强度
use_negative_guidance: true   # 是否启用负向引导
negative_guidance_scale: 1.0  # 负向引导强度
use_negative_prompts: ${use_negative_prompts}

#  WandB 优化配置 (默认值) 
wandb_optimization:
  enable_visualization: false   # 是否启用可视化
  visualization_freq: 20        # 可视化频率 (every N epochs)
  log_histograms: false         # 是否记录参数直方图
  histogram_freq: 50            # 直方图记录频率 (every N batches)
  monitor_system: false         # 是否监控系统资源
  log_gradients: false          # 是否记录梯度信息
  gradient_freq: 1000           # 梯度记录频率 (steps)
  system_freq: 500              # 系统资源记录频率 (steps)


# defaults:
#   - decoder: unet
#   - criterion: loss
#   - _self_

# name: GraspDiffuser
# use_score: false
# score_pretrain: false
# print_freq: 500
# steps: 100
# batch_size: ${batch_size}
# schedule_cfg:
#   beta:
#   - 0.0001
#   - 0.01
#   beta_schedule: linear
#   s: 0.008
# optimizer:
#   name: adam
#   lr: 0.0001
#   weight_decay: 0.0001
# scheduler:
#   name: cosine
#   t_max: 1000
#   min_lr: 1.0e-05
# rand_t_type: half
# loss_type: l2
# out_sigmoid: false
# pred_x0: true
# device: cuda:0
# rot_type: ${rot_type}
# mode: ${mode}
# save_root: ${save_root}

# # Classifier-Free Guidance (CFG) settings
# use_cfg: true  # 是否启用CFG，推理时可以动态覆盖
# guidance_scale: 7.5  # CFG引导强度，越大越遵循文本条件
# use_negative_guidance: true  # 是否启用负向提示引导
# negative_guidance_scale: 1.0  # 负向引导强度

# # 多抓取训练配置
# multi_grasp:
#   enabled: true  # 是否启用多抓取模式
#   num_grasps: 8  # 每个场景的抓取数量
#   loss_aggregation: "mean"  # mean, sum, weighted
#   use_consistency_loss: true  # 是否使用抓取间一致性损失
#   consistency_loss_weight: 0.1  # 一致性损失权重
#   use_diversity_loss: true  # 是否使用多样性损失
#   diversity_loss_weight: 0.05  # 多样性损失权重

#   # 采样配置
#   sampling:
#     parallel_decode: true  # 是否使用并行解码
#     temperature: 1.0  # 采样温度
#     top_k: null  # Top-K采样，null表示不使用

#   # 训练策略
#   training:
#     grasp_dropout_prob: 0.1  # 训练时随机丢弃部分抓取的概率
#     progressive_training: false  # 是否使用渐进式训练（从少到多抓取）
#     warmup_epochs: 10  # 如果使用渐进式训练，预热轮数

#   # 验证和测试阶段多抓取配置
#   validation_test:
#     multi_grasp_mode: true  # 启用多抓取验证/测试
#     backward_compatibility: true  # 保持向后兼容

#     # 推理配置
#     inference:
#       parallel_decode: true  # 并行多抓取推理
#       num_inference_grasps: 4  # 推理时生成的抓取数量，可以与训练时不同
#       k_samples: 4  # 每次推理的采样数量
#       timestep: -1  # 推理时使用的时间步，-1表示最后一步

#     # 匹配器配置
#     matcher:
#       multi_grasp_matching: true  # 启用多对多匹配
#       cost_aggregation: "weighted"  # 成本聚合方式: mean, sum, weighted
#       hungarian_algorithm: true  # 使用匈牙利算法进行最优分配

#     # 评估配置
#     evaluation:
#       compute_per_grasp_metrics: true  # 计算每个抓取的指标
#       compute_distribution_metrics: true  # 计算抓取分布指标
#       top_k_evaluation: [1, 3, 5]  # Top-K评估
#       diversity_threshold: 0.1  # 多样性评估阈值

#     # 输出配置
#     output:
#       save_predictions: false  # 是否保存预测结果
#       save_matched_pairs: false  # 是否保存匹配对
#       log_detailed_metrics: true  # 是否记录详细指标