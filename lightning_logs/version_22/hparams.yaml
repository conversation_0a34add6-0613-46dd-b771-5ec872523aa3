cfg:
  decoder:
    name: unet
    rot_type: r6d
    d_model: 512
    time_embed_mult: 2
    nblocks: 4
    resblock_dropout: 0.0
    transformer_num_heads: 8
    transformer_dim_head: 64
    transformer_dropout: 0.1
    transformer_depth: 1
    transformer_mult_ff: 2
    context_dim: 512
    backbone:
      name: pointnet2
      use_pooling: false
      layer1:
        npoint: 2048
        radius_list:
        - 0.04
        nsample_list:
        - 64
        mlp_list:
        - 3
        - 64
        - 64
        - 128
      layer2:
        npoint: 1024
        radius_list:
        - 0.1
        nsample_list:
        - 32
        mlp_list:
        - 128
        - 128
        - 128
        - 256
      layer3:
        npoint: 512
        radius_list:
        - 0.2
        nsample_list:
        - 32
        mlp_list:
        - 256
        - 128
        - 128
        - 256
      layer4:
        npoint: 128
        radius_list:
        - 0.3
        nsample_list:
        - 16
        mlp_list:
        - 256
        - 512
        - 512
      use_xyz: true
      normalize_xyz: true
    use_position_embedding: false
    use_text_condition: true
    text_dropout_prob: 0.1
  criterion:
    mode: camera_centric_scene_mean_normalized
    device: cuda:0
    rot_type: r6d
    hand_model:
      n_surface_points: 1024
      rot_type: r6d
    loss_weights:
      translation: 10.0
      rotation: 20.0
      qpos: 1.0
      neg_loss: 0.5
      hand_chamfer: 0.0
    multi_grasp:
      loss_aggregation: mean
      use_consistency_loss: true
      consistency_loss_weight: 0.1
      diversity_loss_weight: 0.05
    cost_weights:
      translation: 2.0
      rotation: 2.0
      qpos: 1.0
    scale: 0.1
    q1:
      lambda_torque: 10
      m: 8
      mu: 1
      nms: true
      thres_contact: 0.01
      thres_pen: 0.005
      thres_tpen: 0.01
      rot_type: r6d
  name: GraspDiffuser
  save_root: ./experiments/test_test_test727_1
  device: cuda:0
  rot_type: r6d
  mode: camera_centric_scene_mean_normalized
  print_freq: 250
  batch_size: 24
  steps: 100
  pred_x0: true
  rand_t_type: half
  schedule_cfg:
    beta:
    - 0.0001
    - 0.01
    beta_schedule: linear
    s: 0.008
  optimizer:
    name: adam
    lr: 0.0001
    weight_decay: 0.0001
  scheduler:
    name: cosine
    t_max: 1000
    min_lr: 1.0e-05
  loss_type: l2
  out_sigmoid: false
  use_score: false
  score_pretrain: false
  use_cfg: true
  guidance_scale: 7.5
  use_negative_guidance: true
  negative_guidance_scale: 1.0
data_cfg:
  name: plussceneleap
  mode: camera_centric_scene_mean_normalized
  train:
    root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed
    succ_grasp_dir: /home/<USER>/source/grasp/SceneLeapPro/data/succ_collect
    obj_root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models
    num_grasps: 64
    mode: camera_centric_scene_mean_normalized
    max_grasps_per_object: 1024
    mesh_scale: 0.1
    num_neg_prompts: 4
    enable_cropping: true
    max_points: 10000
    grasp_sampling_strategy: farthest_point
    cache_version: v1.0_plus
    cache_mode: train
    batch_size: 24
    num_workers: 16
  val:
    root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15
    succ_grasp_dir: /home/<USER>/source/grasp/SceneLeapPro/data/succ_collect
    obj_root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models
    num_grasps: 64
    mode: camera_centric_scene_mean_normalized
    max_grasps_per_object: 1024
    mesh_scale: 0.1
    num_neg_prompts: 4
    enable_cropping: true
    max_points: 10000
    grasp_sampling_strategy: farthest_point
    cache_version: v1.0_plus
    cache_mode: test
    batch_size: 24
    num_workers: 16
  test:
    root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/723_sub_15
    succ_grasp_dir: /home/<USER>/source/grasp/SceneLeapPro/data/succ_collect
    obj_root_dir: /home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models
    num_grasps: 64
    mode: camera_centric_scene_mean_normalized
    max_grasps_per_object: 1024
    mesh_scale: 0.1
    num_neg_prompts: 4
    enable_cropping: true
    max_points: 10000
    grasp_sampling_strategy: farthest_point
    cache_version: v1.0_plus
    cache_mode: test
    batch_size: 24
    num_workers: 16
